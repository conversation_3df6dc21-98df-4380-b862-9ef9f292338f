import React from 'react';
import PropTypes from 'prop-types';
import { Route, Switch } from 'react-router-dom';
import { ConnectedRouter } from 'connected-react-router';
import Container from './Container'

const propTypes = {
  history: PropTypes.object.isRequired,
};

const Router = ({ history }) => (
  <ConnectedRouter history={history}>
    <Route path="/" component={Container} />
  </ConnectedRouter>
);

Router.propTypes = propTypes;
export default Router;
