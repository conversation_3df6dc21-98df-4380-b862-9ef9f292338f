@charset "utf-8";
body {
  color: #333;
  font-family: 'HanHei SC', 'PingFang SC',"Microsoft YaHei" ;
  font-size: 14px;
  direction: ltr;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  text-rendering: optimizelegibility;
  position: relative;
  background: #fff;
}

/* 内外边距通常让各个浏览器样式的表现位置不同 */
body,
dl,
dt,
dd,
ul,
ol,
li,
h1,
h2,
h3,
h4,
h5,
h6,
pre,
code,
form,
fieldset,
legend,
input,
textarea,
p,
blockquote,
button,
article,
aside,
details,
figcaption,
figure,
footer,
header,
menu,
nav,
i,
select,
section {
  margin: 0;
  padding: 0;
  font-weight: normal;
  vertical-align: baseline;
}

/* 重设 HTML5 标签, IE 需要在 js 中 createElement(TAG) */
article,
aside,
details,
figcaption,
figure,
footer,
header,
menu,
nav,
section {
  display: block;
}

select:hover{
  cursor:pointer;
}

/*表格外层嵌套 隐藏超出部分*/
table {
  border-collapse: inherit;
  border-spacing: 0;
  width: 100%;
}

ul,
ol {
  list-style: none;
  padding: 0px;
  margin: 0px;
}

/* 去除默认边框 */
img {
  border: 0;
}

/* 统一上标和下标 */
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
}

:root sub,
:root sup {
  vertical-align: baseline;
  /* for ie9 and other modern browsers */
}

sup {
  top: -0.5em;
}

sub {
  bottom: -0.25em;
}

/* 让链接在 hover 状态下显示下划线 */
a {
  text-decoration: none;
  outline: none;
}

a:hover {
  text-decoration: none;
}