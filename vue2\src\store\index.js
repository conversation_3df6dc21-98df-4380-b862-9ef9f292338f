import Vue from 'vue';
import Vuex from 'vuex';
import createPersistedState from 'vuex-persistedstate';
import app from './modules/app';
import employeeInfo from './modules/employeeInfo';
import entryRegister from './modules/entryRegister';
import document from './modules/document';
import personInfoAgreement from './modules/personInfoAgreement';

Vue.use(Vuex);

export default new Vuex.Store({
  modules: {
    app,
    employeeInfo,
    entryRegister,
    document,
    personInfoAgreement
  },
  plugins: [
    createPersistedState({
      key: 'hr-saas-h5-vue2',
      storage: window.sessionStorage,
      reducer: (state) => ({
        // Only persist specific parts of the state
        app: state.app,
        employeeInfo: state.employeeInfo,
        entryRegister: state.entryRegister
      })
    })
  ]
});
