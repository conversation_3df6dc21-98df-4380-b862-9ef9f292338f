<template>
  <div class="not-found">
    <van-empty 
      image="error" 
      description="没有设定这个路由地址！404！谢谢！"
    >
      <van-button 
        type="primary" 
        size="small" 
        @click="goHome"
      >
        返回首页
      </van-button>
    </van-empty>
  </div>
</template>

<script>
export default {
  name: 'NotFound',
  methods: {
    goHome() {
      this.$router.push('/');
    }
  }
};
</script>

<style lang="scss" scoped>
.not-found {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  
  h1 {
    color: red;
    text-align: center;
  }
}
</style>
