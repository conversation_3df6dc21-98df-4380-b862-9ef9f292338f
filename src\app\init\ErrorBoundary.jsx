import React from 'react'
import <PERSON><PERSON><PERSON><PERSON><PERSON> from 'react-json-pretty';
import <PERSON><PERSON><PERSON><PERSON><PERSON>Mon from 'react-json-pretty/dist/monikai';
import 'react-json-pretty/themes/monikai.css';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, errStackList:[] };
  }

  componentDidCatch(error, info) {
    this.setState({ hasError: true, errStackList:error.stack.toString().split('\n') });
    // 仅上报 生产模式 且 线上域名 的错误
    if(process.env.NODE_ENV==='production' && location.host.includes('olading')){
      console.info('__begin report component js error!__')
      fundebug.notifyError(error, {
        metaData: {
          info: info
        }
      });
    }
  }

  render() {
    if (this.state.hasError) {
      return (
        <div style={{padding:'14px'}}>
          <h2>JS出错了！</h2>
          <div style={{height:'1px',width:'99%',backgroundColor:'#e3e3e3',margin:'14px auto'}} />
          <JSONPretty id="json-pretty" data={this.state.errStackList} theme={JSONPrettyMon} />
        </div>
      );
    }
    return this.props.children;
  }
}
export default ErrorBoundary;
