module.exports = {
  "vendor": {
    name: 'vendor',
    test: /[\\/]node_modules[\\/]/,
    chunks: 'all',
    priority: 10,
    enforce: true
  },
  "react-vendor":{
    name: 'react-vendor',
    test: module => /react|redux/.test(module.context),
    chunks: 'initial',
    priority: 10,
    enforce: true
  },
  "antd-vendor": {
    name: 'antd-vendor',
    test: (module) => {
      return /ant/.test(module.context);
    },
    chunks: 'initial',
    priority: 11,
    enforce: true
  },
  "lodash": {
    name: 'lodash',
    test: (module) => {
      return /lodash/.test(module.context);
    },
    chunks: 'initial',
    priority: 11,
    enforce: true
  },
  "react-dom": {
    name: 'react-dom',
    test: (module) => {
      return /react-dom/.test(module.context);
    },
    chunks: 'initial',
    priority: 11,
    enforce: true
  },
  "react-router": {
    name: 'react-router',
    test: (module) => {
      return /react-router/.test(module.context);
    },
    chunks: 'initial',
    priority: 11,
    enforce: true
  },
  "core-js": {
    name: 'core-js',
    test: (module) => {
      return /core-js/.test(module.context);
    },
    chunks: 'initial',
    priority: 11,
    enforce: true
  },
  "connected-react-router": {
    name: 'connected-react-router',
    test: (module) => {
      return /connected-react-router/.test(module.context);
    },
    chunks: 'initial',
    priority: 11,
    enforce: true
  },
  "axios": {
    name: 'axios',
    test: (module) => {
      return /axios/.test(module.context);
    },
    chunks: 'initial',
    priority: 11,
    enforce: true
  },
}
