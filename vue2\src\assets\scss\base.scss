@import 'helpers';
@import 'reset';
@import '../font/iconfont.css';
@import 'vant-design';
html,
body {
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
  font-family: 'PingFang SC', 'Helvetica Neue', arial, sans-serif;
  font-weight: 400;
  font-size: 16px;
  color: $ui-text;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
  background-color: #ffffff !important;
}
#app {
  height: 100%;
  .container {
    width: 100%;
    height: 100%;
  }
}
.flex-box {
  display: flex;
}
.flex-space-center {
  align-items: center;
  justify-content: space-between;
}
.flex-center {
  align-items: center;
  justify-content: center;
}
.flex-around-center {
  align-items: center;
  justify-content: space-around;
}
.flex_shink {
  flex-shrink: 0;
}
.required {
  color: #f30505;
  display: inline-block;
  font-size: 18px;
  position: absolute;
  top: 7px;
  left: 0;
}
// 文档附件查看 - 适配Vant的Modal
.modalClass {
  .van-dialog__content {
    padding-top: 0px !important;
    background-color: transparent !important;

    .van-dialog__close {
      top: 50px;
      background-color: #fff !important;
    }

    .van-dialog__message {
      padding: 0px !important;

      canvas {
        display: inline-block !important;
      }
    }
  }
}
.wrapModalClass{
  display: flex !important;
}
.mobile__pdf__container #viewerContainer .pdfViewer {
  overflow: hidden !important;
}
