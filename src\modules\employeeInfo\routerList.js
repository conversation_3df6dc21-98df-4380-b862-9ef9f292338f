import Loadable from "react-loadable";
import Loading from "components/Loading";
import Form from './pages/form';

const Index = Loadable({ loader: () => import("./"), loading: Loading });
// const Form = Loadable({ loader: () => import("./pages/form"), loading: Loading });
const Detail = Loadable({ loader: () => import("./pages/detail"), loading: Loading });

const routerList = [
  {
    path: "/index",
    component: Index,
    title: "员工信息",
  },
  {
    path: "/detail",
    component: Detail,
    titlel: "员工信息",
  },
  {
    path: "/form",
    component: Form,
    //title: "信息详情",
  },
];
//需要指定父级路由
Array.from(routerList, (rt) => (rt.path = "/employee-info" + rt.path));
export default routerList;
