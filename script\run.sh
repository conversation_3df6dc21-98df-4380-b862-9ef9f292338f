envsubst '$NGINX_PORT' < /script/mysite.template > /etc/nginx/conf.d/default.conf
envsubst < /script/env.template > /usr/share/nginx/html/static/env.js

sed -i "s|/__PUBLIC_PATH_PLACEHOLDER__|$BOOT_PUBLIC_PATH|g" /usr/share/nginx/html/*.html
sed -i "s|/__PUBLIC_PATH_PLACEHOLDER__|$BOOT_PUBLIC_PATH|g" /usr/share/nginx/html/static/css/*.css
sed -i "s|/__PUBLIC_PATH_PLACEHOLDER__|$BOOT_PUBLIC_PATH|g" /usr/share/nginx/html/static/js/*.js

exec nginx -g 'daemon off;'