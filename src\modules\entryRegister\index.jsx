import React, { Component } from 'react';
import {
  List, Button, InputItem, Icon, Toast,Checkbox
} from 'antd-mobile';
//import { Icon as AIcon } from '@ant-design/icons';
import './index.scss';
import { connect } from 'react-redux';
import axios from 'axios';
import action from './redux/action';
import fetch from '../../request/fetch';
import { func } from 'prop-types';
import Header from "components/Header";
const AgreeItem = Checkbox.AgreeItem;

class Entry extends Component {
  constructor(props) {
    super(props);
    this.state = {
      compEmpId:'',//企业员工ID
      compId:'',//企业ID
      iphone: '', // 手机号
      verifyCode: '',// 验证码
      isGetCode: false,// 是否可以获取验证码(手机号校验)
      smsCodeWait: false,// 是否等待接收验证码
      requestNo: '',// 流水编号
      smsTime: null,//短信验证有效期
      smsText: '获取验证码',// 获取短信验证码按钮提示信息
      isCorrectCode:false,// 6位验证码是否合法
      isOnDuty:false,//是否在岗
      isQrOk:false,//二维码是否无效
      isOnDutyTime:'',//已提交时间
      errorMessage:'',//错误提示
      smsToken:'',//token
      smsRequestNo:'',//请求流水号
      taxSubId:"",//公司id
      uid:"",//二维码唯一标识
      isRead:false, //阅读协议
    };
  }
  //发送验证码按钮绑定
  handleSendMsgBtn() {
    const { isGetCode } = this.state;
    if (isGetCode) {
      this.handleSendMsg()
    } else {
      Toast.info("请输入正确的手机号");
    }
  }
  //验证码计时器
  handleSmsTime(){
    const { smsTime } = this.state;
    let second = 60;
    let siv = setInterval(() => {
      this.setState({ smsTime: second})
      if (second < 0) {
        clearInterval(siv);
        this.setState({ smsText: '重新发送', smsCodeWait: false})
      }else{
        this.setState({ smsText: `${second--}s` ,smsCodeWait: true})
      }
    }, 1000);
  }
  
  //获取验证码
  handleSendMsg() {
    const { acSendMsg } = this.props;
    const { iphone ,compEmpId,smsCodeWait,compId,taxSubId,uid} = this.state;
    if (smsCodeWait) return;
    let second = 60;
    Toast.loading("发送中", 0);
    console.log(iphone,compEmpId,compId)
    acSendMsg({ mobile :iphone,compId,taxSubId,uid,smsType:"ENTRY_H5"}).then(res => {
      Toast.hide();
      if (res.success) {
        Toast.info("发送成功");
        if(res.data.taxSubName){
          Toast.info(`您曾入职过【${res.data.taxSubName}】，已为您自动查询历史登记信息`);
        }
        this.setState({
          smsRequestNo:res.data.requestNo,//请求流水号
        });
        this.handleSmsTime();
      }else{
        Toast.info(res.message);
      }
    })
  }

  // 输入手机号 -- 校验手机号格式
  changeInputMoblie (e) {
    let value = e.target.value
    let values = value.replace(/[^\d]/g,'')
    this.setState({
      [e.target.name]: values
    })
    if (/^1[0-9]{10}$/.test(values)) {
      this.setState({
        isGetCode: true
      })
    } else {
      this.setState({
        isGetCode: false
      })
    }
  }
  // 输入验证码 -- 校验验证码格式
  changeInputCode(e) {
    let value = e.target.value
    let values = value.replace(/[^\d]/g,'')
    this.setState({
        verifyCode: values
    })
    if (values.length === 6) {
        this.setState({
            isCorrectCode: true
        })
    } else {
        this.setState({
            isCorrectCode: false
        })
    }
  }

  //是否阅读了协议
  changeReadProtocol(e) {
    console.log(e.target.checked)
    this.setState({
      isRead:e.target.checked
    })

  }




  //验证身份
  checkIdentity(){
    

    Toast.loading("验证中", 0);
    this.setState({
      errorMessage: '',
    })
    const { isGetCode, isCorrectCode} = this.state;
    if (isGetCode && isCorrectCode) {
      if(!this.state.isRead) return Toast.info('请勾选已阅读并同意协议');
      this.checkNext()
    } else if (!isGetCode) {
      this.setState({
        errorMessage: '请输入正确的手机号',
      })
      Toast.hide();
    } else if (!isCorrectCode) {
      this.setState({
        errorMessage: '请输入正确的验证码',
      })
      Toast.hide();
    }
  }
  //验证通过跳转页面
  checkNext() {
    const { acCheckMsg ,acSetBaseInfo,acSetCardInfo} = this.props;
    const { iphone ,smsRequestNo,verifyCode,compId,taxSubId} = this.state;
    acCheckMsg({ mobile :iphone,requestNo:smsRequestNo,taxSubId,verifyCode,compId }).then(res => {
      Toast.hide();
      if (res.success) {
        console.log("验证成功")
        if(!res.data.token){
          this.setState({isOnDuty: true,isOnDutyTime:res.data.updateTime}, ()=>{
            console.log('在岗状态变为true')
          })
        }else{
          const compEmpId = res.data.empCompId;
          sessionStorage.setItem("token",res.data.token);
          //清空基础信息，身份证信息缓存
          acSetBaseInfo(null)
          acSetCardInfo(null)
          this.props.history.push({ pathname:'/entry-register/detail',state:{compEmpId}});
        }
      }else{
        console.log("验证失败")
        Toast.info(res.message);
        return;
      }
    })
  }

  componentDidMount () { 
    const { getUrlParams ,acSetSource ,acSetTaxsubId ,acQrCodeRuleCheck} = this.props;
    const compId = getUrlParams ('compId',this.props.location.search);
    const taxSubId = getUrlParams ('taxSubId',this.props.location.search);
    const isOnDuty = getUrlParams ('isOnDuty',this.props.location.search) == "true" ? true : false;
    const isOnDutyTime = getUrlParams ('updateTime',this.props.location.search);
    const uid = getUrlParams ('uid',this.props.location.search);
    if(uid){
      acQrCodeRuleCheck({uid}).then(res=>{
        if(res.success){
  
        }
      }).catch(err=>{
        this.setState({isQrOk: true}, ()=>{
          console.log('二维码无效')
        })
      })
    }
    this.setState({
      compId,
      isOnDuty,
      isOnDutyTime,
      taxSubId,
      uid
    })

    acSetSource('ENTRY');
    acSetTaxsubId(taxSubId);
  }

  render () {
    const { iphone ,smsText ,smsCodeWait,isOnDuty,verifyCode,errorMessage,isOnDutyTime,isQrOk} = this.state;
    const smsStyle = {
      color: smsCodeWait ? '#666' : '#4185F8',
      border: smsCodeWait ? '1px solid #666' : '1px solid #4185f8'
    }
    return (
      <div style={{ width: '100%', height: '100%' }}>
        {
          !isOnDuty && !isQrOk && 
          <div className="login" style={{ display: 'block' }}>
            {/* 登录页 */}
            {/* <header className="title">阿拉钉入职登记</header> */}
            <Header title={'入职登记'}/>
            {/* 阿拉钉界面 */}
            <div className="logo"></div>
            {/* 输入手机号 */}
            <div className="content">
              <p className="tip">请通过您的手机号登录</p>
              <div className="iphone">
                {/* <span></span> */}
                <p></p>
                <input type="number" pattern="\d*" placeholder="请输入手机号码" name="iphone" value={iphone} onChange={this.changeInputMoblie.bind(this)} />
              </div>
              {/* 获取验证码 */}
              <div className="code">
                <div className="left">
                  {/* <span></span> */}
                  <p></p>
                  <input type="number" pattern="\d*" placeholder="请输入短信验证码" value={verifyCode}  onChange={this.changeInputCode.bind(this)}/>
                </div>
                <div className="right" style={smsStyle} onClick={this.handleSendMsgBtn.bind(this)}>
                {
                    smsCodeWait &&
                    <span>{smsText}</span>
                }
                {
                    !smsCodeWait &&
                    <span>{smsText}</span>
                }
                </div>
              </div>
              <p className="err-tip">{errorMessage}</p>

              
            </div>
            <div className="vertify" onClick={this.checkIdentity.bind(this)}>登 录</div>
          
            {/* <p className="read">
            <AgreeItem data-seed="logId" onChange={this.changeReadProtocol.bind(this)  }>
            已阅读并同意 <a onClick={(e) => { e.preventDefault(); this.props.history.push({ pathname: "/entry-register/protocol" }) }}> <span>《用户服务协议》</span></a>
          </AgreeItem>  </p> */}
            <p className="read">
            <AgreeItem data-seed="logId" onChange={this.changeReadProtocol.bind(this)  }>
            已阅读并同意 <a onClick={(e) => { e.preventDefault(); window.location.href=  window.env.baseRouter + 'document' }}> <span>《用户服务协议》</span></a>
          </AgreeItem>  </p>
          </div>
        }
        {
          isOnDuty && 
          <div className="onduty">
            {/* 到岗 */}
            {/* <header className="title">阿拉钉入职登记</header> */}
            <Header title={'入职登记'}/>
            <section className="body">
              <div className="body-pic">
                <div></div>
              </div>
              <p className="body-msg">入职信息已经在{isOnDutyTime}提交给HR。请等待HR审核确认</p>
            </section>
          </div>
        }
        {
          isQrOk && 
          <div className="onduty">
            {/* 到岗 */}
            {/* <header className="title">阿拉钉入职登记</header> */}
            <Header title={'入职登记'}/>
            <section className="body">
              <div className="body-qr">
                <div></div>
              </div>
              <p className="body-msg">二维码已失效</p>
            </section>
          </div>
        }
      </div>
    );
  }
}

const mapStateToProps = state => ({});

const mapDispatchToProps = {
  acSendMsg: action.acSendMsg,//发送验证码
  acCheckMsg: action.acCheckMsg,//验证短信
  acQrCodeRuleCheck:action.acQrCodeRuleCheck,//二维码验证
  acGetCountries:action.acGetCountries,//国家列表
  acGetCity:action.acGetCity,//城市列表
  getUrlParams:action.getUrlParams,//url参数
  acSetTaxsubId:action.acSetTaxsubId,//公司id
  acSetSource:action.acSetSource,//来源
  acSetBaseInfo:action.acSetBaseInfo ,
  acSetCardInfo:action.acSetCardInfo

};

export default connect(mapStateToProps, mapDispatchToProps)(Entry);
