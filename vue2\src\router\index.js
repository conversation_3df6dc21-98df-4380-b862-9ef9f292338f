import Vue from 'vue';
import VueRouter from 'vue-router';
import Container from '@/views/Container.vue';

Vue.use(VueRouter);

const routes = [
  {
    path: '/',
    name: 'Container',
    component: Container,
    children: [
      {
        path: '/employee-info',
        name: 'EmployeeInfo',
        component: () => import('@/views/employeeInfo/index.vue'),
        children: [
          {
            path: 'detail',
            name: 'EmployeeDetail',
            component: () => import('@/views/employeeInfo/pages/detail.vue')
          }
        ]
      },
      {
        path: '/entry-register',
        name: 'EntryRegister',
        component: () => import('@/views/entryRegister/index.vue'),
        children: [
          {
            path: 'form',
            name: 'EntryForm',
            component: () => import('@/views/entryRegister/pages/form.vue')
          },
          {
            path: 'success',
            name: 'EntrySuccess',
            component: () => import('@/views/entryRegister/pages/success.vue')
          }
        ]
      },
      {
        path: '/document',
        name: 'Document',
        component: () => import('@/views/document/index.vue')
      },
      {
        path: '/person-info-agreement',
        name: 'PersonInfoAgreement',
        component: () => import('@/views/personInfoAgreement/index.vue')
      }
    ]
  },
  {
    path: '/404',
    name: 'NotFound',
    component: () => import('@/components/NotFound.vue')
  },
  {
    path: '*',
    redirect: '/404'
  }
];

const router = new VueRouter({
  mode: 'history',
  base: process.env.BASE_URL,
  routes
});

export default router;
