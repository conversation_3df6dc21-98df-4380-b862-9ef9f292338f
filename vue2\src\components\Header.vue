<template>
  <van-nav-bar
    :title="title"
    class="comp-header"
    :style="headerStyle"
  />
</template>

<script>
import { mapGetters } from 'vuex';

export default {
  name: 'CompHeader',
  props: {
    title: {
      type: String,
      default: ''
    }
  },
  computed: {
    ...mapGetters('app', ['title']),
    headerStyle() {
      return {
        textAlign: "center",
        height: "40px",
        lineHeight: "40px",
        background: "#ffffff",
        fontSize: "16px",
        fontFamily: "PingFangSC-Medium, PingFang SC",
        fontWeight: "500",
        color: "#383838",
      };
    }
  },
  mounted() {
    // 组件挂载后的逻辑
  }
};
</script>

<style lang="scss" scoped>
.comp-header {
  // 额外的样式定制
}
</style>
