//import fetch from 'request/fetch';
import fetch from 'request/newFetch';
// import fetchH5 from 'request/fetchH5';
import fetchUpload from 'request/fetchUpload';
import * as AT from './actionTypes'

const acSetSelectInfo = (payload) => (dispatch, getState) => {dispatch({type: AT.SELECTEDINFO, payload})}
//下发短信
const acSendMsg = (param) => (dispatch, getState) => fetch.post(`/hrsaas-emp/sms/v1/sms-captcha`, param).then(res => res);
//验证短信
const acCheckMsg = (param) => (dispatch, getState) => fetch.post(`/hrsaas-emp/sms/v1/sms-auth`, param).then(res => res);
//保存token
const acSaveToken = (payload) => (dispatch, getState) => {dispatch({type: AT.LOGIN_TOKEN,payload})};
//登记详情列表
const acRegistDetail = (param) => (dispatch, getState) => fetch.get(`/hrsaas-emp/entry/v1/emp/query/registdetail`, param).then(res => res);
//员工详情操作
const acOperateEmp = (param) => (dispatch, getState) => fetch.post(`/hrsaas-emp/empDetail/operateEmp`, param,{Source:"WECHAT_APPLET"}).then(res => res);
//手机号校验
const acCheckIdAndMobile = (param) => (dispatch, getState) => fetch.post(`/hrsaas-emp/empDetail/checkIdNoAndMobile`, param,{Source:"WECHAT_APPLET"}).then(res => res);
//员工详情查询
const acgetEmpDetail = (param) => (dispatch, getState) => fetch.post(`/hrsaas-emp/empDetail/getEmpDetail`, param,{Source:"WECHAT_APPLET",'Cache-Control': 'no-cache, no-store, must-revalidate'}).then(res => res);
//查询企业ID
const acgetcompId = (param) => (dispatch, getState) => fetch.post(`/hrsaas-emp/empDetail/query/compId`, param,{Source:"WECHAT_APPLET"}).then(res => res);
//OCR识别
const acOcrCheck = (param) => (dispatch, getState) => fetch.post(`/hrsaas-emp/empDetail/ocrCheck`, param,{Source:"WECHAT_APPLET"}).then(res => res);
//获取员工所在公司
const acGetEmpTaxSubject = (param) => (dispatch, getState) => fetch.post(`/hrsaas-emp/empDetail/getEmpTaxSubject`, param,{Source:"WECHAT_APPLET"}).then(res => res);

//上传文件
const acUploadFile = (file,name) => (dispatch, getState) => {
  let formData = new FormData();
  formData.append("file", file, name)
  return fetchUpload({
    url: `/hrsaas-emp/archive/upload`,
    method: 'post',
    data: formData
  }).then(res => {
    return res
  });
}
//下载文件
const acDownload = (param) => (dispatch, getState) => fetch.down(`/hrsaas-emp/archive/download?archiveId=`+param.archiveId).then(res=>res)

//国际列表
const acGetCountries = (params) => (dispatch, getState) => {
  return fetch.get('/hrsaas-salary/enterprise/employee/countries', params).then((res) => {
    dispatch({
      type: AT.SET_COUNTRYLIST,
      payload: res.data
    })
    return res;
  })
}
//城市列表
const acGetCity = (params) => (dispatch, getState) => {
  return fetch.get('/hrsaas-salary/insuredProject/manage/getCity', params).then((res) => {
    dispatch({
      type: AT.SET_CITYLIST,
      payload: res.data
    })
    return res;
  })
}
//联动城市列表
const acGetProvince = (params) => (dispatch, getState) => {
  return fetch.get('/hrsaas-emp/custom/v1/query/province', params).then((res) => {
    dispatch({
      type: AT.SET_PROVINCELIST,
      payload: res.data
    })
    return res;
  })
}
//获取url参数
const getUrlParams = (name, str) => (dispatch, getState)=>{
  const reg = new RegExp(`(^|&)${ name}=([^&]*)(&|$)`);
  const r = str.substr(1).match(reg);
  if (r != null) return  decodeURIComponent(r[2]); return null;
}

//来源
const acSetSource = (params) => (dispatch, getState) => {
  dispatch({
    type: AT.SET_OPTIONSOURCE,
    payload: params
  })
}

//各个分组是否编辑保存必填字段
const acSetGroupRequired = (params) => (dispatch, getState) => {
  dispatch({
    type: AT.SET_GROUPREQUIRED,
    payload: params
  })
}



export default {
  acSetSelectInfo,
  acSendMsg,
  acCheckMsg,
  acRegistDetail,
  acSaveToken,
  acOperateEmp,
  acGetCountries,
  acGetCity,
  acCheckIdAndMobile,
  acgetEmpDetail,
  acUploadFile,
  acDownload,
  getUrlParams,
  acSetSource,
  acgetcompId,
  acGetProvince,
  acSetGroupRequired,
  acOcrCheck,
  acGetEmpTaxSubject
};
