.entry-form {

  //height: calc(100vh - 50px);
  // .submit-btn {
  //     width: calc(100% - 40px);
  //     position: fixed !important;
  //     bottom: 0;
  //     left: 0;
  //     margin: 0 20px;
  //     border-radius: 20px;
  // }
  .label-name {
    margin-left: 7px;
  }

  .am-list-item .am-list-line .am-list-content {
    margin: 10px 20px 0 20px;
    padding-bottom: 0 !important;
    position: relative;
    //border-bottom: 1px solid #e6e7e9 !important;
    line-height: 1;
    .take {
      position: absolute;
      right: 0;
      top: 6px;
      font-size: 14px;
      color: #6A6F7F;
      display: flex;
      align-items: center;
      img {
        height: 20px;
        width: 20px;
        margin-right: 8px;
       

      }
    }


  }

  .am-list-item.am-input-item {
    height: 30px;
    padding-left: 0;
  }

  .am-list-item .am-list-line {
    padding-right: 0;
  }

  .am-list-item {
    padding-left: 0;
  }

  .am-list-item .am-list-line .am-list-arrow-horizontal {
    position: absolute;
    right: 0;
  }

  .am-list-item .am-list-line .am-list-extra {
    text-align: left;
    flex-basis: 100%;
  }

  .am-list-item {
    .am-list-line {
      .am-list-content {
        .am-list-item {
          border-bottom: 1px solid #e6e7e9 !important;
          position: relative;
        }
      }
    }
  }

  .footer {
    height: 80px;
    width: 100%;
    position: fixed;
    bottom: 0;

    .submit-btn {
      margin: 0 26px;
      border-radius: 20px;
      height: 40px;
      line-height: 40px !important;
    }
  }

  .requiredNode {
    color: red;
    font-size: 12px;
    // display: none;
  }

  .fake-input {
    font-size: 14px !important;
    color: #a5a5a5 !important;
  }

  .fake-input-placeholder {
    font-size: 14px !important;
  }


}