import createReducer from 'utils/createReducer';
import * as AT from "./actionTypes";

const defaultState = () => ({
    selectedInfo: {},
    token:'',
    countrylist:{},
    citylist:{},
    provincelist:{},
    optionSource:'',//来源
    groupRequired:[]//各个分组是否已编辑必填
});

export default createReducer(defaultState, {
    [AT.SELECTEDINFO](state, action) {
        const data = {
            ...state,
            selectedInfo: action.payload
        }
        return data;
    },
    [AT.LOGIN_TOKEN](state, action) {
        const data = {
            ...state,
            token: action.payload
        }
        return data;
    },
    [AT.SET_COUNTRYLIST]: (state, action) => {
        const data = {
            ...state,
            countrylist: action.payload
        }
        return data;
        // //逻辑处理示例
        // const {test} = state;
        // let result = {...action.payload};
        // delete result.data;
        // return ({
        //     ...state,
        //     message: JSON.stringify({...result,test}),
        // })
    },
    [AT.SET_CITYLIST]: (state, action) => {
        const data = {
            ...state,
            citylist: action.payload
        }
        return data;
    },
    [AT.SET_OPTIONSOURCE]: (state, action) => {
        const data = {
            ...state,
            optionSource: action.payload
        }
        return data;
    },
    [AT.SET_PROVINCELIST] :(state, action) => {
        const data = {
            ...state,
            provincelist: action.payload
        }
        return data;
    },
    [AT.SET_GROUPREQUIRED]: (state, action) => {
        const data = {
            ...state,
            groupRequired: action.payload
        }
        return data;
    },
});