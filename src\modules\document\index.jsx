import React, { Component } from 'react';
import { withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import "./index.scss";
import action from "modules/document/redux/action"


class Document extends Component {
  componentDidMount() {
    this.handleGetConfigInfo();
  }

  handleGetConfigInfo() {
    const { acGetConfigInfo } = this.props;
    acGetConfigInfo();
  }

  render() {
    const richText = this.props.configInfo.agreementContent ?
          this.props.configInfo.agreementContent : '';
    document.title = '用户协议中心';
    return (
      <div className="ql-container">
        <div className="ql-editor" dangerouslySetInnerHTML={{ __html: richText }}></div>
      </div>
    );
  }
}

const mapStateToProps = (state) => ({
  configInfo: state.document.configInfo
});

const mapDispatchToProps = {
  acGetConfigInfo: action.acGetConfigInfo
};

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(Document));
