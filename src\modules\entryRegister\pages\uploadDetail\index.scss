@import "../../../../assets/scss/helpers";

@mixin x-c {
  display: flex;
  justify-content: center;
  align-items: center;
}

.upload-detail {
  background: #FAFAFA;
  height: 100%;

  .tips {
    font-size: 14px;
    color: #6A6F7F;
    padding: 12px 6px;
  }

  .container {
    margin-bottom:64px;
    .item {
      background: #fff;
      height: 60px;
      padding: 0 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 16px;
      color: #070F29; 
      position: relative;   
      &:after {
        content: " ";
        position: absolute;
        bottom: 0;
        left: 16px;
        width: 100%;
        border-bottom: 1px solid #F2F4F6;  
      } 
    }
    .m-b-10 {
      margin-bottom: 10px;
      
    }
  }

  .next {
    position: fixed;
    bottom: 0;
    width: 100%;
    height: 64px;
    box-shadow: 0 -1px 4px 0 rgba(224, 224, 224, 0.60);
    background: #fff;
    padding: 0 25px;
    box-sizing: border-box;
    @include x-c;

    .btn {
      width: 100%;
      height: 41px;
      background: #4185F7;
      border-radius: 20px;
      font-weight: 600;
      font-size: 14px;
      color: #FFF;
      @include x-c;
    }
  }
}