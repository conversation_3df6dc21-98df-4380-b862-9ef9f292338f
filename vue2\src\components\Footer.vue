<template>
  <footer :style="footerStyle">
    <van-button 
      type="primary" 
      :style="submitBtn" 
      @click="handleClick"
    >
      {{ text }}
    </van-button>
  </footer>
</template>

<script>
export default {
  name: 'Co<PERSON>Footer',
  props: {
    text: {
      type: String,
      default: '提交'
    },
    fun: {
      type: Function,
      default: () => {}
    }
  },
  computed: {
    footerStyle() {
      return {
        height: "80px",
        width: "100%",
        position: "fixed",
        bottom: "0",
        zIndex: 999,
      };
    },
    submitBtn() {
      return {
        margin: "0 26px",
        borderRadius: "20px",
        height: "40px",
        lineHeight: "40px",
      };
    }
  },
  methods: {
    handleClick() {
      if (this.fun) {
        this.fun();
      }
    }
  },
  mounted() {
    // 组件挂载后的逻辑
  }
};
</script>

<style lang="scss" scoped>
// 额外的样式定制
</style>
