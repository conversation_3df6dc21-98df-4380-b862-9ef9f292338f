<template>
  <div v-if="shouldShowLoading">
    <van-loading v-if="!error" type="spinner" color="#2C7CFF">
      Loading...
    </van-loading>
    <div v-else class="error-message">
      Error! 抱歉，页面加载出现错误，请刷新后重试
    </div>
  </div>
</template>

<script>
export default {
  name: 'Loading',
  props: {
    error: {
      type: Boolean,
      default: false
    },
    pastDelay: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    shouldShowLoading() {
      return this.error || this.pastDelay;
    }
  },
  watch: {
    error(newVal) {
      if (newVal) {
        // 加载错误时刷新页面
        setTimeout(() => {
          document.location.reload();
        }, 2000);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.error-message {
  color: #ee0a24;
  text-align: center;
  padding: 20px;
}
</style>
