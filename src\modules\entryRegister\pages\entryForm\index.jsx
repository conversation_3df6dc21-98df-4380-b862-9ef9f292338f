import React, { Component } from "react";
import "./index.scss";
import {
  List,
  Button,
  Toast,
  InputItem,
  DatePicker,
  Picker,
  ImagePicker,
  NavBar,
  Icon,
  Modal,
} from "antd-mobile";
import { createForm } from "rc-form";
import { connect } from "react-redux";
import action from "modules/entryRegister/redux/action";
import { validPhoneReg, valideEmail, validIdNo } from "utils/validReg";
import Footer from "components/Footer";
import Upload from "components/UpLoad";
import CompDrawer from "../../components/Drawer";
import CompForm from "../../components/CompForm";
import DefForm from "../../components/DefForm";
import { createFromIconfontCN } from "@ant-design/icons";
import smoothscroll from "smoothscroll-polyfill";

const alert = Modal.alert;
class EntryForm extends Component {
  constructor(props) {
    super(props);
    this.state = {
      ref: {}, //存放子组件的方法和状态
      handleFun: {}, //子组件相应操作，EDIT/DELETE/ADD
      DrawerState: false, //模态框状态
      list: [], //分组数据
      groupName: "", //分组名称
      addMultipleYn: false, //是否支持多条
      addMultipleYnTemp: {}, //多条新增模板
      countList: 0, //当前明细条数
      stateNow: "", //当前操作状态
      isSaveState: [], //是否已保存
    };
  }
  //初始化数据
  componentDidMount() {
    console.log('this.props.location.state',this.props.location.state)
    smoothscroll.polyfill();
    const list = this.props.location.state.list;
    const groupName = this.props.location.state.groupName;
    const addMultipleYn = this.props.location.state.addMultipleYn; //是否支持多条
    this.setState({ list, groupName, addMultipleYn }, () => {
      this.handleDefTemp(list[0]); //初始化新增模板
      this.handleCountList();
    });
  }
  //初始化新增模板
  handleDefTemp(list) {
    if (this.state.addMultipleYn) {
      this.setState({
        addMultipleYnTemp: Object.assign({}, list, { isTemp: true, isTempSave: false }),
      });
    }
  }
  //back
  handleBack() {
    //this.props.history.push({ pathname:'/entry-register/detail'});
    window.history.back(-1);
  }
  //保存
  submit() {
    console.log(this.state.ref);
    const { ref, handleFun, addMultipleYn, countList, isSaveState } = this.state;
    if (!addMultipleYn) {
      this.handleSaveList(0);
    } else {
      if (isSaveState.every((value) => value)) {
        Toast.info(`已添加${countList}条分组`, 2, () => {
          this.handleBack(); //所有子组件保存状态校验通过，back
        });
      } else {
        Toast.info(`请保存所有分组`, 1, () => {
          let _elmAdd = document.querySelector(".buttomSaveNode");
          if (_elmAdd) {
            _elmAdd.scrollIntoView({ behavior: "smooth", block: "start" });
          }
        });
      }
    }
  }
  //绑定组件的状态和方法
  handleForm(ref, index, type) {
    let newRef = Object.assign(this.state.ref, { [index]: ref }),
      newhandleFun = Object.assign(this.state.handleFun, { [index]: type });
    this.setState(
      {
        ref: newRef,
        handleFun: newhandleFun,
        list: this.state.list.map((val, i) =>
          i == index && !!val["isTemp"]
            ? Object.assign(val, { isTempSave: ref.state["isSave"] })
            : val
        ),
      },
      () => {
        const { ref, countList, addMultipleYn } = this.state;
        const isSaveState = [];
        for (let i in ref) {
          if (!!ref[i]) {
            isSaveState.push(ref[i].state["isSave"]);
          } else {
            isSaveState.push(true);
          }
        }
        console.log(isSaveState);
        this.setState({ isSaveState }, () => {});
      }
    );
  }

  //控制新增模态框的显示
  handleDrawer() {
    //this.setState({ DrawerState: !this.state.DrawerState });//模态框形式新增

    console.log("新增");
    let addMultipleYnTemp = Object.assign({}, this.state.addMultipleYnTemp, { id: Math.random() });
    this.setState(
      {
        list: [...this.state.list, addMultipleYnTemp],
        stateNow: "ADD",
      },
      () => {
        this.handleCountList();
      }
    );
  }
  //保存分组信息
  handleSaveList(i, addMultipleYn) {
    console.log(i);
    console.log(this.state.ref);
    const { ref, handleFun, isSaveState } = this.state;
    if (handleFun[i] == "DELETE") ref[i].handleDelete();
    if (handleFun[i] == "EDIT" || handleFun[i] == "ADD") ref[i].submit();
  }
  //删除分组信息
  handleDeleteList(index, isTemp, isTempSave) {
    if (!!isTemp && !isTempSave) {
      //删除未保存的模板，清空虚拟数据,handleFun,ref赋值为undefined
      let newHandleFun = this.state.handleFun;
      let newRef = this.state.ref;
      newHandleFun[index] = undefined;
      newRef[index] = undefined;
      this.setState(
        {
          list: this.state.list.map((val, i) => (i == index ? { id: "delTmp" } : val)), //delTmp表示：删除未保存自定义组件
          handleFun: Object.assign({}, this.state.handleFun, newHandleFun),
          ref: Object.assign({}, this.state.ref, newRef),
          isSaveState: this.state.isSaveState.map((val, i) => (i == index ? true : val)),
        },
        () => {
          this.handleCountList();
          console.log(this.state.list);
          console.log(this.state.handleFun);
          console.log(this.state.ref);
        }
      );
    } else {
      this.state.ref[index].handleDelete();
      //原先list+模板已保存的，删除后仅留id
      let newRef = this.state.ref;
      this.setState(
        {
          list: this.state.list.map((val, i) => (i == index ? { id: newRef[i].state["id"] } : val)),
          handleFun: Object.assign(this.state.handleFun, { [index]: "DELETE" }),
        },
        () => {
          this.handleCountList();
          console.log(this.state.list);
          console.log(this.state.handleFun);
        }
      );
    }
  }
  //计算当前明细条数
  handleCountList() {
    const { list } = this.state;
    let countList = 0;
    list.map((v, i) => {
      if (v["fieldModels"]) {
        countList++;
      }
    });
    this.setState({ countList });
  }
  //周期-重新渲染
  componentDidUpdate() {
    // let _elm = document.querySelector(".requiredNode");
    // if(_elm){
    //   _elm.scrollIntoView({ behavior: "smooth",block:"center"});
    // }
    if (this.state.stateNow === "ADD") {
      setTimeout(() => {
        //let _elmAdd = document.querySelector(".list");
        let _elmAddAll = document.querySelectorAll(".list>.am-list>.am-list-body>div");
        _elmAddAll[_elmAddAll.length - 1].scrollIntoView({ behavior: "smooth", block: "start" });
        //window.scroll({ top: _elmAdd.scrollHeight+window.screen.height, left: 0, behavior: 'smooth' });
        this.setState({
          stateNow: "",
        });
      }, 0);
    }
  }

  render() {
    const { id, list, groupName, addMultipleYn, countList, isSaveState } = this.state;
    const { DrawerState } = this.state;
    const DrawerList = list[0] || [];
    const IconFont = createFromIconfontCN({
      scriptUrl: "//at.alicdn.com/t/font_2363017_nlczau11b3c.js", //自定义图标
    });
  console.log('this.props.location.state',this.props.location.state)


    let count = 0; //标题序号
    const renderForm = (
      <div data-po="list" className="list">
        <List>
          {list.map((item, index) =>
            item["fieldModels"] ? (
              <div>
                {addMultipleYn ? (
                  <header className="groupTitle">
                    <p>
                      <span className="groupTitleP">
                        {groupName}
                        {list.length == 1 ? "" : ++count}
                      </span>
                    </p>
                    <div>
                      {!isSaveState[index] ? (
                        //<button className="buttomSaveNode" onClick={()=>this.handleSaveList(index,item["isTemp"],item["isTempSave"])} style={{marginRight:"5px"}}>保存</button> :
                        <Button
                          className="buttomSaveNode"
                          type="primary"
                          style={{
                            height: "20px",
                            lineHeight: "20px",
                            padding: "0 8px",
                            marginRight: "5px",
                            fontSize: "12px",
                          }}
                          inline
                          size="small"
                          onClick={() =>
                            this.handleSaveList(index, item["isTemp"], item["isTempSave"])
                          }
                        >
                          保存
                        </Button>
                      ) : (
                        <Button
                          className=""
                          disabled
                          type="primary"
                          style={{
                            height: "20px",
                            lineHeight: "20px",
                            padding: "0 8px",
                            marginRight: "5px",
                            fontSize: "12px",
                          }}
                          inline
                          size="small"
                          onClick={() =>
                            this.handleSaveList(index, item["isTemp"], item["isTempSave"])
                          }
                        >
                          已保存
                        </Button>
                      )}
                      {list.length > 1 && countList > 1 && item.id !== null ? (
                        <Button
                          type=""
                          style={{
                            height: "20px",
                            lineHeight: "20px",
                            padding: "0 8px",
                            fontSize: "12px",
                          }}
                          inline
                          size="small"
                          onClick={() =>
                            alert("提示", "您确定要删除本条记录", [
                              { text: "取消", onPress: () => console.log("cancel") },
                              {
                                text: "确定",
                                onPress: () =>
                                  this.handleDeleteList(index, item["isTemp"], item["isTempSave"]),
                              },
                            ])
                          }
                        >
                          删除
                        </Button>
                      ) : null}
                    </div>
                  </header>
                ) : null}
                {item["isTemp"] ? (
                  <DefForm
                    key={item.id}
                    data={this.props.location.state}
                    id={0}
                    list={item.fieldModels}
                    onRef={(ref) => {
                      this.handleForm(ref, index, "ADD");
                    }}
                  />
                ) : (
                  <CompForm
                    key={item.id}
                    data={this.props.location.state}
                    id={item.id}
                    list={item.fieldModels}
                    onRef={(ref) => {
                      this.handleForm(ref, index, "EDIT");
                    }}
                  />
                )}
              </div>
            ) : item["id"] !== "delTmp" ? (
              <CompForm
                key={item.id}
                data={this.props.location.state}
                id={item.id}
                list={[]}
                onRef={(ref) => {
                  this.handleForm(ref, index, "DELETE");
                }}
              />
            ) : null
          )}
        </List>
        <div style={{ height: "80px" }}></div>
        <Footer fun={this.submit.bind(this)} text={"确定"} />
      </div>
    );
    return (
      <div className="entry-form">
        <NavBar
          key={1}
          mode="light"
          icon={<Icon type="left" />}
          style={{ color: "black" }}
          onLeftClick={this.handleBack.bind(this)}
          rightContent={
            addMultipleYn
              ? [
                  <IconFont
                    type={!DrawerState ? "icontianjia" : "iconjianhao"}
                    style={{ fontSize: "17px" }}
                    onClick={this.handleDrawer.bind(this)}
                  />,
                ]
              : null
          }
        >
          {groupName}
        </NavBar>
        {
          renderForm
          // addMultipleYn ? <CompDrawer data={this.props.location.state} groupModels={DrawerList} DrawerState={DrawerState}>{renderForm}</CompDrawer>
          // : renderForm
        }
      </div>
    );
  }
}

const mapStateToProps = (state) => ({});

const mapDispatchToProps = {};

export default connect(mapStateToProps, mapDispatchToProps)(createForm()(EntryForm));
