/* eslint-disable global-require */
/* eslint-disable no-undef */
/* eslint-disable no-return-assign */
/* eslint-disable max-len */
import initReactFastclick from 'react-fastclick';

import ReactDOM from 'react-dom';
import { createBrowserHistory } from 'history';
import isNil from 'lodash/isNil';
import app from 'app/index';
import './assets/scss/base.scss';
import smoothscroll from 'smoothscroll-polyfill';

if (process.env.NODE_ENV === 'production' && __ENV_CONFIG__.BUILD_ENV === 'online') {
  const fundebug = require('fundebug-javascript');
  fundebug.apikey = 'e2a48d47e4a406ca9a9c5ed71c32656c7e59936af358971a6302f75d5f8a55c3';
}
initReactFastclick();

let client;
let initialState;

if (typeof window !== 'undefined') {
  // eslint-disable-next-line no-underscore-dangle
  initialState = window.__INITIAL_STATE__;
}

if (isNil(initialState)) {
  client = app.createStore(createBrowserHistory());
} else {
  client = app.createStore(createBrowserHistory(), initialState);
}

// 监听全局state
const updCurrentState = () => sessionStorage.currentState = JSON.stringify(client.store.getState() || {});
client.store.subscribe(() => updCurrentState());
// updCurrentState();

// const application = app.createApp(client.store,client.history);
const application = app.createApp(client.store);
ReactDOM.render(application, window.document.getElementById('app'));

if (module.hot) {
  module.hot.accept();
}
//smoothscroll.polyfill();
//移动端关闭软键盘
document.addEventListener("touchend", function(e){
  if(e.target.tagName!=="INPUT"){
    document.activeElement.blur();
  }
  // else{
  //   let _el = document.activeElement;
  //   _el.scrollIntoView({ behavior: "smooth",block:"start"});
  // }
}, false);


// // 通过persisted属性判断是否存在 BF Cache
// window.addEventListener('pageshow',function(e){
//   if(e.persisted) {
//     window.location.reload();
//   }
// })

window.addEventListener('popstate',function(e){
  window.location.reload();
})
