# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@ant-design/colors@^6.0.0":
  version "6.0.0"
  resolved "https://r.cnpmjs.org/@ant-design/colors/download/@ant-design/colors-6.0.0.tgz#9b9366257cffcc47db42b9d0203bb592c13c0298"
  integrity sha1-m5NmJXz/zEfbQrnQIDu1ksE8Apg=
  dependencies:
    "@ctrl/tinycolor" "^3.4.0"

"@ant-design/icons-svg@^4.0.0":
  version "4.1.0"
  resolved "https://r.cnpmjs.org/@ant-design/icons-svg/download/@ant-design/icons-svg-4.1.0.tgz#480b025f4b20ef7fe8f47d4a4846e4fee84ea06c"
  integrity sha1-SAsCX0sg73/o9H1KSEbk/uhOoGw=

"@ant-design/icons@^4.4.0":
  version "4.6.2"
  resolved "https://r.cnpmjs.org/@ant-design/icons/download/@ant-design/icons-4.6.2.tgz#290f2e8cde505ab081fda63e511e82d3c48be982"
  integrity sha1-KQ8ujN5QWrCB/aY+UR6C08SL6YI=
  dependencies:
    "@ant-design/colors" "^6.0.0"
    "@ant-design/icons-svg" "^4.0.0"
    "@babel/runtime" "^7.11.2"
    classnames "^2.2.6"
    rc-util "^5.9.4"

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.0.0-beta.35", "@babel/code-frame@^7.5.5":
  version "7.5.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/code-frame/-/code-frame-7.5.5.tgz#bc0782f6d69f7b7d49531219699b988f669a8f9d"
  integrity sha1-vAeC9tafe31JUxIZaZuYj2aaj50=
  dependencies:
    "@babel/highlight" "^7.0.0"

"@babel/core@^7.0.0":
  version "7.5.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/core/-/core-7.5.5.tgz#17b2686ef0d6bc58f963dddd68ab669755582c30"
  integrity sha1-F7JobvDWvFj5Y93daKtml1VYLDA=
  dependencies:
    "@babel/code-frame" "^7.5.5"
    "@babel/generator" "^7.5.5"
    "@babel/helpers" "^7.5.5"
    "@babel/parser" "^7.5.5"
    "@babel/template" "^7.4.4"
    "@babel/traverse" "^7.5.5"
    "@babel/types" "^7.5.5"
    convert-source-map "^1.1.0"
    debug "^4.1.0"
    json5 "^2.1.0"
    lodash "^4.17.13"
    resolve "^1.3.2"
    semver "^5.4.1"
    source-map "^0.5.0"

"@babel/generator@^7.5.5":
  version "7.5.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/generator/-/generator-7.5.5.tgz#873a7f936a3c89491b43536d12245b626664e3cf"
  integrity sha1-hzp/k2o8iUkbQ1NtEiRbYmZk488=
  dependencies:
    "@babel/types" "^7.5.5"
    jsesc "^2.5.1"
    lodash "^4.17.13"
    source-map "^0.5.0"
    trim-right "^1.0.1"

"@babel/helper-annotate-as-pure@^7.0.0":
  version "7.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.0.0.tgz#323d39dd0b50e10c7c06ca7d7638e6864d8c5c32"
  integrity sha1-Mj053QtQ4Qx8Bsp9djjmhk2MXDI=
  dependencies:
    "@babel/types" "^7.0.0"

"@babel/helper-builder-binary-assignment-operator-visitor@^7.1.0":
  version "7.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/helper-builder-binary-assignment-operator-visitor/-/helper-builder-binary-assignment-operator-visitor-7.1.0.tgz#6b69628dfe4087798e0c4ed98e3d4a6b2fbd2f5f"
  integrity sha1-a2lijf5Ah3mODE7Zjj1Kay+9L18=
  dependencies:
    "@babel/helper-explode-assignable-expression" "^7.1.0"
    "@babel/types" "^7.0.0"

"@babel/helper-builder-react-jsx@^7.3.0":
  version "7.3.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/helper-builder-react-jsx/-/helper-builder-react-jsx-7.3.0.tgz#a1ac95a5d2b3e88ae5e54846bf462eeb81b318a4"
  integrity sha1-oayVpdKz6Irl5UhGv0Yu64GzGKQ=
  dependencies:
    "@babel/types" "^7.3.0"
    esutils "^2.0.0"

"@babel/helper-call-delegate@^7.4.4":
  version "7.4.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/helper-call-delegate/-/helper-call-delegate-7.4.4.tgz#87c1f8ca19ad552a736a7a27b1c1fcf8b1ff1f43"
  integrity sha1-h8H4yhmtVSpzanonscH8+LH/H0M=
  dependencies:
    "@babel/helper-hoist-variables" "^7.4.4"
    "@babel/traverse" "^7.4.4"
    "@babel/types" "^7.4.4"

"@babel/helper-create-class-features-plugin@^7.5.5":
  version "7.5.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.5.5.tgz#401f302c8ddbc0edd36f7c6b2887d8fa1122e5a4"
  integrity sha1-QB8wLI3bwO3Tb3xrKIfY+hEi5aQ=
  dependencies:
    "@babel/helper-function-name" "^7.1.0"
    "@babel/helper-member-expression-to-functions" "^7.5.5"
    "@babel/helper-optimise-call-expression" "^7.0.0"
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/helper-replace-supers" "^7.5.5"
    "@babel/helper-split-export-declaration" "^7.4.4"

"@babel/helper-define-map@^7.5.5":
  version "7.5.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/helper-define-map/-/helper-define-map-7.5.5.tgz#3dec32c2046f37e09b28c93eb0b103fd2a25d369"
  integrity sha1-PewywgRvN+CbKMk+sLED/Sol02k=
  dependencies:
    "@babel/helper-function-name" "^7.1.0"
    "@babel/types" "^7.5.5"
    lodash "^4.17.13"

"@babel/helper-explode-assignable-expression@^7.1.0":
  version "7.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/helper-explode-assignable-expression/-/helper-explode-assignable-expression-7.1.0.tgz#537fa13f6f1674df745b0c00ec8fe4e99681c8f6"
  integrity sha1-U3+hP28WdN90WwwA7I/k6ZaByPY=
  dependencies:
    "@babel/traverse" "^7.1.0"
    "@babel/types" "^7.0.0"

"@babel/helper-function-name@^7.1.0":
  version "7.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/helper-function-name/-/helper-function-name-7.1.0.tgz#a0ceb01685f73355d4360c1247f582bfafc8ff53"
  integrity sha1-oM6wFoX3M1XUNgwSR/WCv6/I/1M=
  dependencies:
    "@babel/helper-get-function-arity" "^7.0.0"
    "@babel/template" "^7.1.0"
    "@babel/types" "^7.0.0"

"@babel/helper-get-function-arity@^7.0.0":
  version "7.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/helper-get-function-arity/-/helper-get-function-arity-7.0.0.tgz#83572d4320e2a4657263734113c42868b64e49c3"
  integrity sha1-g1ctQyDipGVyY3NBE8QoaLZOScM=
  dependencies:
    "@babel/types" "^7.0.0"

"@babel/helper-hoist-variables@^7.4.4":
  version "7.4.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/helper-hoist-variables/-/helper-hoist-variables-7.4.4.tgz#0298b5f25c8c09c53102d52ac4a98f773eb2850a"
  integrity sha1-Api18lyMCcUxAtUqxKmPdz6yhQo=
  dependencies:
    "@babel/types" "^7.4.4"

"@babel/helper-member-expression-to-functions@^7.5.5":
  version "7.5.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.5.5.tgz#1fb5b8ec4453a93c439ee9fe3aeea4a84b76b590"
  integrity sha1-H7W47ERTqTxDnun+Ou6kqEt2tZA=
  dependencies:
    "@babel/types" "^7.5.5"

"@babel/helper-module-imports@^7.0.0":
  version "7.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/helper-module-imports/-/helper-module-imports-7.0.0.tgz#96081b7111e486da4d2cd971ad1a4fe216cc2e3d"
  integrity sha1-lggbcRHkhtpNLNlxrRpP4hbMLj0=
  dependencies:
    "@babel/types" "^7.0.0"

"@babel/helper-module-transforms@^7.1.0", "@babel/helper-module-transforms@^7.4.4":
  version "7.5.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/helper-module-transforms/-/helper-module-transforms-7.5.5.tgz#f84ff8a09038dcbca1fd4355661a500937165b4a"
  integrity sha1-+E/4oJA43Lyh/UNVZhpQCTcWW0o=
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"
    "@babel/helper-simple-access" "^7.1.0"
    "@babel/helper-split-export-declaration" "^7.4.4"
    "@babel/template" "^7.4.4"
    "@babel/types" "^7.5.5"
    lodash "^4.17.13"

"@babel/helper-optimise-call-expression@^7.0.0":
  version "7.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.0.0.tgz#a2920c5702b073c15de51106200aa8cad20497d5"
  integrity sha1-opIMVwKwc8Fd5REGIAqoytIEl9U=
  dependencies:
    "@babel/types" "^7.0.0"

"@babel/helper-plugin-utils@^7.0.0":
  version "7.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.0.0.tgz#bbb3fbee98661c569034237cc03967ba99b4f250"
  integrity sha1-u7P77phmHFaQNCN8wDlnupm08lA=

"@babel/helper-regex@^7.0.0", "@babel/helper-regex@^7.4.4":
  version "7.5.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/helper-regex/-/helper-regex-7.5.5.tgz#0aa6824f7100a2e0e89c1527c23936c152cab351"
  integrity sha1-CqaCT3EAouDonBUnwjk2wVLKs1E=
  dependencies:
    lodash "^4.17.13"

"@babel/helper-remap-async-to-generator@^7.1.0":
  version "7.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.1.0.tgz#361d80821b6f38da75bd3f0785ece20a88c5fe7f"
  integrity sha1-Nh2AghtvONp1vT8HheziCojF/n8=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.0.0"
    "@babel/helper-wrap-function" "^7.1.0"
    "@babel/template" "^7.1.0"
    "@babel/traverse" "^7.1.0"
    "@babel/types" "^7.0.0"

"@babel/helper-replace-supers@^7.5.5":
  version "7.5.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/helper-replace-supers/-/helper-replace-supers-7.5.5.tgz#f84ce43df031222d2bad068d2626cb5799c34bc2"
  integrity sha1-+EzkPfAxIi0rrQaNJibLV5nDS8I=
  dependencies:
    "@babel/helper-member-expression-to-functions" "^7.5.5"
    "@babel/helper-optimise-call-expression" "^7.0.0"
    "@babel/traverse" "^7.5.5"
    "@babel/types" "^7.5.5"

"@babel/helper-simple-access@^7.1.0":
  version "7.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/helper-simple-access/-/helper-simple-access-7.1.0.tgz#65eeb954c8c245beaa4e859da6188f39d71e585c"
  integrity sha1-Ze65VMjCRb6qToWdphiPOdceWFw=
  dependencies:
    "@babel/template" "^7.1.0"
    "@babel/types" "^7.0.0"

"@babel/helper-split-export-declaration@^7.4.4":
  version "7.4.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.4.4.tgz#ff94894a340be78f53f06af038b205c49d993677"
  integrity sha1-/5SJSjQL549T8GrwOLIFxJ2ZNnc=
  dependencies:
    "@babel/types" "^7.4.4"

"@babel/helper-wrap-function@^7.1.0":
  version "7.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/helper-wrap-function/-/helper-wrap-function-7.2.0.tgz#c4e0012445769e2815b55296ead43a958549f6fa"
  integrity sha1-xOABJEV2nigVtVKW6tQ6lYVJ9vo=
  dependencies:
    "@babel/helper-function-name" "^7.1.0"
    "@babel/template" "^7.1.0"
    "@babel/traverse" "^7.1.0"
    "@babel/types" "^7.2.0"

"@babel/helpers@^7.5.5":
  version "7.5.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/helpers/-/helpers-7.5.5.tgz#63908d2a73942229d1e6685bc2a0e730dde3b75e"
  integrity sha1-Y5CNKnOUIinR5mhbwqDnMN3jt14=
  dependencies:
    "@babel/template" "^7.4.4"
    "@babel/traverse" "^7.5.5"
    "@babel/types" "^7.5.5"

"@babel/highlight@^7.0.0":
  version "7.5.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/highlight/-/highlight-7.5.0.tgz#56d11312bd9248fa619591d02472be6e8cb32540"
  integrity sha1-VtETEr2SSPphlZHQJHK+boyzJUA=
  dependencies:
    chalk "^2.0.0"
    esutils "^2.0.2"
    js-tokens "^4.0.0"

"@babel/parser@^7.0.0", "@babel/parser@^7.4.4", "@babel/parser@^7.5.5":
  version "7.5.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/parser/-/parser-7.5.5.tgz#02f077ac8817d3df4a832ef59de67565e71cca4b"
  integrity sha1-AvB3rIgX099Kgy71neZ1Zeccyks=

"@babel/plugin-proposal-async-generator-functions@^7.2.0":
  version "7.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/plugin-proposal-async-generator-functions/-/plugin-proposal-async-generator-functions-7.2.0.tgz#b289b306669dce4ad20b0252889a15768c9d417e"
  integrity sha1-somzBmadzkrSCwJSiJoVdoydQX4=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/helper-remap-async-to-generator" "^7.1.0"
    "@babel/plugin-syntax-async-generators" "^7.2.0"

"@babel/plugin-proposal-class-properties@^7.5.5":
  version "7.5.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.5.5.tgz#a974cfae1e37c3110e71f3c6a2e48b8e71958cd4"
  integrity sha1-qXTPrh43wxEOcfPGouSLjnGVjNQ=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.5.5"
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-proposal-dynamic-import@^7.5.0":
  version "7.5.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/plugin-proposal-dynamic-import/-/plugin-proposal-dynamic-import-7.5.0.tgz#e532202db4838723691b10a67b8ce509e397c506"
  integrity sha1-5TIgLbSDhyNpGxCme4zlCeOXxQY=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-syntax-dynamic-import" "^7.2.0"

"@babel/plugin-proposal-json-strings@^7.2.0":
  version "7.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/plugin-proposal-json-strings/-/plugin-proposal-json-strings-7.2.0.tgz#568ecc446c6148ae6b267f02551130891e29f317"
  integrity sha1-Vo7MRGxhSK5rJn8CVREwiR4p8xc=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-syntax-json-strings" "^7.2.0"

"@babel/plugin-proposal-object-rest-spread@^7.5.5":
  version "7.5.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/plugin-proposal-object-rest-spread/-/plugin-proposal-object-rest-spread-7.5.5.tgz#61939744f71ba76a3ae46b5eea18a54c16d22e58"
  integrity sha1-YZOXRPcbp2o65Gte6hilTBbSLlg=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-syntax-object-rest-spread" "^7.2.0"

"@babel/plugin-proposal-optional-catch-binding@^7.2.0":
  version "7.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/plugin-proposal-optional-catch-binding/-/plugin-proposal-optional-catch-binding-7.2.0.tgz#135d81edb68a081e55e56ec48541ece8065c38f5"
  integrity sha1-E12B7baKCB5V5W7EhUHs6AZcOPU=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-syntax-optional-catch-binding" "^7.2.0"

"@babel/plugin-proposal-unicode-property-regex@^7.4.4":
  version "7.4.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/plugin-proposal-unicode-property-regex/-/plugin-proposal-unicode-property-regex-7.4.4.tgz#501ffd9826c0b91da22690720722ac7cb1ca9c78"
  integrity sha1-UB/9mCbAuR2iJpByByKsfLHKnHg=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/helper-regex" "^7.4.4"
    regexpu-core "^4.5.4"

"@babel/plugin-syntax-async-generators@^7.2.0":
  version "7.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.2.0.tgz#69e1f0db34c6f5a0cf7e2b3323bf159a76c8cb7f"
  integrity sha1-aeHw2zTG9aDPfiszI78VmnbIy38=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-syntax-dynamic-import@^7.0.0", "@babel/plugin-syntax-dynamic-import@^7.2.0":
  version "7.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/plugin-syntax-dynamic-import/-/plugin-syntax-dynamic-import-7.2.0.tgz#69c159ffaf4998122161ad8ebc5e6d1f55df8612"
  integrity sha1-acFZ/69JmBIhYa2OvF5tH1XfhhI=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-syntax-json-strings@^7.2.0":
  version "7.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.2.0.tgz#72bd13f6ffe1d25938129d2a186b11fd62951470"
  integrity sha1-cr0T9v/h0lk4Ep0qGGsR/WKVFHA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-syntax-jsx@^7.2.0":
  version "7.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.2.0.tgz#0b85a3b4bc7cdf4cc4b8bf236335b907ca22e7c7"
  integrity sha1-C4WjtLx830zEuL8jYzW5B8oi58c=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-syntax-object-rest-spread@^7.2.0":
  version "7.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.2.0.tgz#3b7a3e733510c57e820b9142a6579ac8b0dfad2e"
  integrity sha1-O3o+czUQxX6CC5FCpleayLDfrS4=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-syntax-optional-catch-binding@^7.2.0":
  version "7.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.2.0.tgz#a94013d6eda8908dfe6a477e7f9eda85656ecf5c"
  integrity sha1-qUAT1u2okI3+akd+f57ahWVuz1w=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-arrow-functions@^7.2.0":
  version "7.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.2.0.tgz#9aeafbe4d6ffc6563bf8f8372091628f00779550"
  integrity sha1-mur75Nb/xlY7+Pg3IJFijwB3lVA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-async-to-generator@^7.5.0":
  version "7.5.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.5.0.tgz#89a3848a0166623b5bc481164b5936ab947e887e"
  integrity sha1-iaOEigFmYjtbxIEWS1k2q5R+iH4=
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/helper-remap-async-to-generator" "^7.1.0"

"@babel/plugin-transform-block-scoped-functions@^7.2.0":
  version "7.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/plugin-transform-block-scoped-functions/-/plugin-transform-block-scoped-functions-7.2.0.tgz#5d3cc11e8d5ddd752aa64c9148d0db6cb79fd190"
  integrity sha1-XTzBHo1d3XUqpkyRSNDbbLef0ZA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-block-scoping@^7.5.5":
  version "7.5.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.5.5.tgz#a35f395e5402822f10d2119f6f8e045e3639a2ce"
  integrity sha1-o185XlQCgi8Q0hGfb44EXjY5os4=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    lodash "^4.17.13"

"@babel/plugin-transform-classes@^7.5.5":
  version "7.5.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/plugin-transform-classes/-/plugin-transform-classes-7.5.5.tgz#d094299d9bd680a14a2a0edae38305ad60fb4de9"
  integrity sha1-0JQpnZvWgKFKKg7a44MFrWD7Tek=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.0.0"
    "@babel/helper-define-map" "^7.5.5"
    "@babel/helper-function-name" "^7.1.0"
    "@babel/helper-optimise-call-expression" "^7.0.0"
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/helper-replace-supers" "^7.5.5"
    "@babel/helper-split-export-declaration" "^7.4.4"
    globals "^11.1.0"

"@babel/plugin-transform-computed-properties@^7.2.0":
  version "7.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/plugin-transform-computed-properties/-/plugin-transform-computed-properties-7.2.0.tgz#83a7df6a658865b1c8f641d510c6f3af220216da"
  integrity sha1-g6ffamWIZbHI9kHVEMbzryICFto=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-destructuring@^7.5.0":
  version "7.5.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/plugin-transform-destructuring/-/plugin-transform-destructuring-7.5.0.tgz#f6c09fdfe3f94516ff074fe877db7bc9ef05855a"
  integrity sha1-9sCf3+P5RRb/B0/od9t7ye8FhVo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-dotall-regex@^7.4.4":
  version "7.4.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.4.4.tgz#361a148bc951444312c69446d76ed1ea8e4450c3"
  integrity sha1-NhoUi8lRREMSxpRG127R6o5EUMM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/helper-regex" "^7.4.4"
    regexpu-core "^4.5.4"

"@babel/plugin-transform-duplicate-keys@^7.5.0":
  version "7.5.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.5.0.tgz#c5dbf5106bf84cdf691222c0974c12b1df931853"
  integrity sha1-xdv1EGv4TN9pEiLAl0wSsd+TGFM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-exponentiation-operator@^7.2.0":
  version "7.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.2.0.tgz#a63868289e5b4007f7054d46491af51435766008"
  integrity sha1-pjhoKJ5bQAf3BU1GSRr1FDV2YAg=
  dependencies:
    "@babel/helper-builder-binary-assignment-operator-visitor" "^7.1.0"
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-for-of@^7.4.4":
  version "7.4.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.4.4.tgz#0267fc735e24c808ba173866c6c4d1440fc3c556"
  integrity sha1-Amf8c14kyAi6FzhmxsTRRA/DxVY=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-function-name@^7.4.4":
  version "7.4.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.4.4.tgz#e1436116abb0610c2259094848754ac5230922ad"
  integrity sha1-4UNhFquwYQwiWQlISHVKxSMJIq0=
  dependencies:
    "@babel/helper-function-name" "^7.1.0"
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-literals@^7.2.0":
  version "7.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/plugin-transform-literals/-/plugin-transform-literals-7.2.0.tgz#690353e81f9267dad4fd8cfd77eafa86aba53ea1"
  integrity sha1-aQNT6B+SZ9rU/Yz9d+r6hqulPqE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-member-expression-literals@^7.2.0":
  version "7.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.2.0.tgz#fa10aa5c58a2cb6afcf2c9ffa8cb4d8b3d489a2d"
  integrity sha1-+hCqXFiiy2r88sn/qMtNiz1Imi0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-modules-amd@^7.5.0":
  version "7.5.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.5.0.tgz#ef00435d46da0a5961aa728a1d2ecff063e4fb91"
  integrity sha1-7wBDXUbaCllhqnKKHS7P8GPk+5E=
  dependencies:
    "@babel/helper-module-transforms" "^7.1.0"
    "@babel/helper-plugin-utils" "^7.0.0"
    babel-plugin-dynamic-import-node "^2.3.0"

"@babel/plugin-transform-modules-commonjs@^7.5.0":
  version "7.5.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/plugin-transform-modules-commonjs/-/plugin-transform-modules-commonjs-7.5.0.tgz#425127e6045231360858eeaa47a71d75eded7a74"
  integrity sha1-QlEn5gRSMTYIWO6qR6cdde3tenQ=
  dependencies:
    "@babel/helper-module-transforms" "^7.4.4"
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/helper-simple-access" "^7.1.0"
    babel-plugin-dynamic-import-node "^2.3.0"

"@babel/plugin-transform-modules-systemjs@^7.5.0":
  version "7.5.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.5.0.tgz#e75266a13ef94202db2a0620977756f51d52d249"
  integrity sha1-51JmoT75QgLbKgYgl3dW9R1S0kk=
  dependencies:
    "@babel/helper-hoist-variables" "^7.4.4"
    "@babel/helper-plugin-utils" "^7.0.0"
    babel-plugin-dynamic-import-node "^2.3.0"

"@babel/plugin-transform-modules-umd@^7.2.0":
  version "7.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.2.0.tgz#7678ce75169f0877b8eb2235538c074268dd01ae"
  integrity sha1-dnjOdRafCHe46yI1U4wHQmjdAa4=
  dependencies:
    "@babel/helper-module-transforms" "^7.1.0"
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-named-capturing-groups-regex@^7.4.5":
  version "7.4.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.4.5.tgz#9d269fd28a370258199b4294736813a60bbdd106"
  integrity sha1-nSaf0oo3AlgZm0KUc2gTpgu90QY=
  dependencies:
    regexp-tree "^0.1.6"

"@babel/plugin-transform-new-target@^7.4.4":
  version "7.4.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.4.4.tgz#18d120438b0cc9ee95a47f2c72bc9768fbed60a5"
  integrity sha1-GNEgQ4sMye6VpH8scryXaPvtYKU=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-object-super@^7.5.5":
  version "7.5.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.5.5.tgz#c70021df834073c65eb613b8679cc4a381d1a9f9"
  integrity sha1-xwAh34NAc8ZethO4Z5zEo4HRqfk=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/helper-replace-supers" "^7.5.5"

"@babel/plugin-transform-parameters@^7.4.4":
  version "7.4.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.4.4.tgz#7556cf03f318bd2719fe4c922d2d808be5571e16"
  integrity sha1-dVbPA/MYvScZ/kySLS2Ai+VXHhY=
  dependencies:
    "@babel/helper-call-delegate" "^7.4.4"
    "@babel/helper-get-function-arity" "^7.0.0"
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-property-literals@^7.2.0":
  version "7.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.2.0.tgz#03e33f653f5b25c4eb572c98b9485055b389e905"
  integrity sha1-A+M/ZT9bJcTrVyyYuUhQVbOJ6QU=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-react-display-name@^7.0.0":
  version "7.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/plugin-transform-react-display-name/-/plugin-transform-react-display-name-7.2.0.tgz#ebfaed87834ce8dc4279609a4f0c324c156e3eb0"
  integrity sha1-6/rth4NM6NxCeWCaTwwyTBVuPrA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-react-jsx-self@^7.0.0":
  version "7.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.2.0.tgz#461e21ad9478f1031dd5e276108d027f1b5240ba"
  integrity sha1-Rh4hrZR48QMd1eJ2EI0CfxtSQLo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@babel/plugin-transform-react-jsx-source@^7.0.0":
  version "7.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.2.0.tgz#20c8c60f0140f5dd3cd63418d452801cf3f7180f"
  integrity sha1-IMjGDwFA9d081jQY1FKAHPP3GA8=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@babel/plugin-transform-react-jsx@^7.0.0":
  version "7.3.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.3.0.tgz#f2cab99026631c767e2745a5368b331cfe8f5290"
  integrity sha1-8sq5kCZjHHZ+J0WlNoszHP6PUpA=
  dependencies:
    "@babel/helper-builder-react-jsx" "^7.3.0"
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@babel/plugin-transform-regenerator@^7.4.5":
  version "7.4.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.4.5.tgz#629dc82512c55cee01341fb27bdfcb210354680f"
  integrity sha1-Yp3IJRLFXO4BNB+ye9/LIQNUaA8=
  dependencies:
    regenerator-transform "^0.14.0"

"@babel/plugin-transform-reserved-words@^7.2.0":
  version "7.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.2.0.tgz#4792af87c998a49367597d07fedf02636d2e1634"
  integrity sha1-R5Kvh8mYpJNnWX0H/t8CY20uFjQ=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-shorthand-properties@^7.2.0":
  version "7.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.2.0.tgz#6333aee2f8d6ee7e28615457298934a3b46198f0"
  integrity sha1-YzOu4vjW7n4oYVRXKYk0o7RhmPA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-spread@^7.2.0":
  version "7.2.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/plugin-transform-spread/-/plugin-transform-spread-7.2.2.tgz#3103a9abe22f742b6d406ecd3cd49b774919b406"
  integrity sha1-MQOpq+IvdCttQG7NPNSbd0kZtAY=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-sticky-regex@^7.2.0":
  version "7.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.2.0.tgz#a1e454b5995560a9c1e0d537dfc15061fd2687e1"
  integrity sha1-oeRUtZlVYKnB4NU338FQYf0mh+E=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/helper-regex" "^7.0.0"

"@babel/plugin-transform-template-literals@^7.4.4":
  version "7.4.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/plugin-transform-template-literals/-/plugin-transform-template-literals-7.4.4.tgz#9d28fea7bbce637fb7612a0750989d8321d4bcb0"
  integrity sha1-nSj+p7vOY3+3YSoHUJidgyHUvLA=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.0.0"
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-typeof-symbol@^7.2.0":
  version "7.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.2.0.tgz#117d2bcec2fbf64b4b59d1f9819894682d29f2b2"
  integrity sha1-EX0rzsL79ktLWdH5gZiUaC0p8rI=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-unicode-regex@^7.4.4":
  version "7.4.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.4.4.tgz#ab4634bb4f14d36728bf5978322b35587787970f"
  integrity sha1-q0Y0u08U02cov1l4Mis1WHeHlw8=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/helper-regex" "^7.4.4"
    regexpu-core "^4.5.4"

"@babel/preset-env@^7.0.0":
  version "7.5.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/preset-env/-/preset-env-7.5.5.tgz#bc470b53acaa48df4b8db24a570d6da1fef53c9a"
  integrity sha1-vEcLU6yqSN9LjbJKVw1tof71PJo=
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-proposal-async-generator-functions" "^7.2.0"
    "@babel/plugin-proposal-dynamic-import" "^7.5.0"
    "@babel/plugin-proposal-json-strings" "^7.2.0"
    "@babel/plugin-proposal-object-rest-spread" "^7.5.5"
    "@babel/plugin-proposal-optional-catch-binding" "^7.2.0"
    "@babel/plugin-proposal-unicode-property-regex" "^7.4.4"
    "@babel/plugin-syntax-async-generators" "^7.2.0"
    "@babel/plugin-syntax-dynamic-import" "^7.2.0"
    "@babel/plugin-syntax-json-strings" "^7.2.0"
    "@babel/plugin-syntax-object-rest-spread" "^7.2.0"
    "@babel/plugin-syntax-optional-catch-binding" "^7.2.0"
    "@babel/plugin-transform-arrow-functions" "^7.2.0"
    "@babel/plugin-transform-async-to-generator" "^7.5.0"
    "@babel/plugin-transform-block-scoped-functions" "^7.2.0"
    "@babel/plugin-transform-block-scoping" "^7.5.5"
    "@babel/plugin-transform-classes" "^7.5.5"
    "@babel/plugin-transform-computed-properties" "^7.2.0"
    "@babel/plugin-transform-destructuring" "^7.5.0"
    "@babel/plugin-transform-dotall-regex" "^7.4.4"
    "@babel/plugin-transform-duplicate-keys" "^7.5.0"
    "@babel/plugin-transform-exponentiation-operator" "^7.2.0"
    "@babel/plugin-transform-for-of" "^7.4.4"
    "@babel/plugin-transform-function-name" "^7.4.4"
    "@babel/plugin-transform-literals" "^7.2.0"
    "@babel/plugin-transform-member-expression-literals" "^7.2.0"
    "@babel/plugin-transform-modules-amd" "^7.5.0"
    "@babel/plugin-transform-modules-commonjs" "^7.5.0"
    "@babel/plugin-transform-modules-systemjs" "^7.5.0"
    "@babel/plugin-transform-modules-umd" "^7.2.0"
    "@babel/plugin-transform-named-capturing-groups-regex" "^7.4.5"
    "@babel/plugin-transform-new-target" "^7.4.4"
    "@babel/plugin-transform-object-super" "^7.5.5"
    "@babel/plugin-transform-parameters" "^7.4.4"
    "@babel/plugin-transform-property-literals" "^7.2.0"
    "@babel/plugin-transform-regenerator" "^7.4.5"
    "@babel/plugin-transform-reserved-words" "^7.2.0"
    "@babel/plugin-transform-shorthand-properties" "^7.2.0"
    "@babel/plugin-transform-spread" "^7.2.0"
    "@babel/plugin-transform-sticky-regex" "^7.2.0"
    "@babel/plugin-transform-template-literals" "^7.4.4"
    "@babel/plugin-transform-typeof-symbol" "^7.2.0"
    "@babel/plugin-transform-unicode-regex" "^7.4.4"
    "@babel/types" "^7.5.5"
    browserslist "^4.6.0"
    core-js-compat "^3.1.1"
    invariant "^2.2.2"
    js-levenshtein "^1.1.3"
    semver "^5.5.0"

"@babel/preset-react@^7.0.0":
  version "7.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/preset-react/-/preset-react-7.0.0.tgz#e86b4b3d99433c7b3e9e91747e2653958bc6b3c0"
  integrity sha1-6GtLPZlDPHs+npF0fiZTlYvGs8A=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-transform-react-display-name" "^7.0.0"
    "@babel/plugin-transform-react-jsx" "^7.0.0"
    "@babel/plugin-transform-react-jsx-self" "^7.0.0"
    "@babel/plugin-transform-react-jsx-source" "^7.0.0"

"@babel/runtime@^7.0.0":
  version "7.5.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/runtime/-/runtime-7.5.5.tgz#74fba56d35efbeca444091c7850ccd494fd2f132"
  integrity sha1-dPulbTXvvspEQJHHhQzNSU/S8TI=
  dependencies:
    regenerator-runtime "^0.13.2"

"@babel/runtime@^7.1.2":
  version "7.5.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/runtime/-/runtime-7.5.4.tgz#cb7d1ad7c6d65676e66b47186577930465b5271b"
  integrity sha1-y30a18bWVnbma0cYZXeTBGW1Jxs=
  dependencies:
    regenerator-runtime "^0.13.2"

"@babel/runtime@^7.11.2", "@babel/runtime@^7.12.5":
  version "7.14.0"
  resolved "https://r.cnpmjs.org/@babel/runtime/download/@babel/runtime-7.14.0.tgz#46794bc20b612c5f75e62dd071e24dfd95f1cbe6"
  integrity sha1-RnlLwgthLF915i3QceJN/ZXxy+Y=
  dependencies:
    regenerator-runtime "^0.13.4"

"@babel/template@^7.1.0", "@babel/template@^7.4.4":
  version "7.4.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/template/-/template-7.4.4.tgz#f4b88d1225689a08f5bc3a17483545be9e4ed237"
  integrity sha1-9LiNEiVomgj1vDoXSDVFvp5O0jc=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "@babel/parser" "^7.4.4"
    "@babel/types" "^7.4.4"

"@babel/traverse@^7.0.0", "@babel/traverse@^7.1.0", "@babel/traverse@^7.4.4", "@babel/traverse@^7.5.5":
  version "7.5.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/traverse/-/traverse-7.5.5.tgz#f664f8f368ed32988cd648da9f72d5ca70f165bb"
  integrity sha1-9mT482jtMpiM1kjan3LVynDxZbs=
  dependencies:
    "@babel/code-frame" "^7.5.5"
    "@babel/generator" "^7.5.5"
    "@babel/helper-function-name" "^7.1.0"
    "@babel/helper-split-export-declaration" "^7.4.4"
    "@babel/parser" "^7.5.5"
    "@babel/types" "^7.5.5"
    debug "^4.1.0"
    globals "^11.1.0"
    lodash "^4.17.13"

"@babel/types@^7.0.0", "@babel/types@^7.2.0", "@babel/types@^7.3.0", "@babel/types@^7.4.4", "@babel/types@^7.5.5":
  version "7.5.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/@babel/types/-/types-7.5.5.tgz#97b9f728e182785909aa4ab56264f090a028d18a"
  integrity sha1-l7n3KOGCeFkJqkq1YmTwkKAo0Yo=
  dependencies:
    esutils "^2.0.2"
    lodash "^4.17.13"
    to-fast-properties "^2.0.0"

"@ctrl/tinycolor@^3.4.0":
  version "3.4.0"
  resolved "https://r.cnpmjs.org/@ctrl/tinycolor/download/@ctrl/tinycolor-3.4.0.tgz#c3c5ae543c897caa9c2a68630bed355be5f9990f"
  integrity sha1-w8WuVDyJfKqcKmhjC+01W+X5mQ8=

"@types/events@*":
  version "3.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/@types/events/-/events-3.0.0.tgz#2862f3f58a9a7f7c3e78d79f130dd4d71c25c2a7"
  integrity sha1-KGLz9Yqaf3w+eNefEw3U1xwlwqc=

"@types/glob@^7.1.1":
  version "7.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/@types/glob/-/glob-7.1.1.tgz#aa59a1c6e3fbc421e07ccd31a944c30eba521575"
  integrity sha1-qlmhxuP7xCHgfM0xqUTDDrpSFXU=
  dependencies:
    "@types/events" "*"
    "@types/minimatch" "*"
    "@types/node" "*"

"@types/json-schema@^7.0.6":
  version "7.0.7"
  resolved "https://r.cnpmjs.org/@types/json-schema/download/@types/json-schema-7.0.7.tgz#98a993516c859eb0d5c4c8f098317a9ea68db9ad"
  integrity sha1-mKmTUWyFnrDVxMjwmDF6nqaNua0=

"@types/minimatch@*":
  version "3.0.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/@types/minimatch/-/minimatch-3.0.3.tgz#3dca0e3f33b200fc7d1139c0cd96c1268cadfd9d"
  integrity sha1-PcoOPzOyAPx9ETnAzZbBJoyt/Z0=

"@types/node@*":
  version "12.6.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/@types/node/-/node-12.6.2.tgz#a5ccec6abb6060d5f20d256fb03ed743e9774999"
  integrity sha1-pczsartgYNXyDSVvsD7XQ+l3SZk=

"@types/prop-types@*":
  version "15.7.3"
  resolved "https://r.cnpmjs.org/@types/prop-types/download/@types/prop-types-15.7.3.tgz#2ab0d5da2e5815f94b0b9d4b95d1e5f243ab2ca7"
  integrity sha1-KrDV2i5YFflLC51LldHl8kOrLKc=

"@types/react@^17.0.0":
  version "17.0.5"
  resolved "https://r.cnpmjs.org/@types/react/download/@types/react-17.0.5.tgz#3d887570c4489011f75a3fc8f965bf87d09a1bea"
  integrity sha1-PYh1cMRIkBH3Wj/I+WW/h9CaG+o=
  dependencies:
    "@types/prop-types" "*"
    "@types/scheduler" "*"
    csstype "^3.0.2"

"@types/scheduler@*":
  version "0.16.1"
  resolved "https://r.cnpmjs.org/@types/scheduler/download/@types/scheduler-0.16.1.tgz#18845205e86ff0038517aab7a18a62a6b9f71275"
  integrity sha1-GIRSBehv8AOFF6q3oYpiprn3EnU=

"@webassemblyjs/ast@1.8.5":
  version "1.8.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/@webassemblyjs/ast/-/ast-1.8.5.tgz#51b1c5fe6576a34953bf4b253df9f0d490d9e359"
  integrity sha1-UbHF/mV2o0lTv0slPfnw1JDZ41k=
  dependencies:
    "@webassemblyjs/helper-module-context" "1.8.5"
    "@webassemblyjs/helper-wasm-bytecode" "1.8.5"
    "@webassemblyjs/wast-parser" "1.8.5"

"@webassemblyjs/floating-point-hex-parser@1.8.5":
  version "1.8.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/@webassemblyjs/floating-point-hex-parser/-/floating-point-hex-parser-1.8.5.tgz#1ba926a2923613edce496fd5b02e8ce8a5f49721"
  integrity sha1-G6kmopI2E+3OSW/VsC6M6KX0lyE=

"@webassemblyjs/helper-api-error@1.8.5":
  version "1.8.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/@webassemblyjs/helper-api-error/-/helper-api-error-1.8.5.tgz#c49dad22f645227c5edb610bdb9697f1aab721f7"
  integrity sha1-xJ2tIvZFInxe22EL25aX8aq3Ifc=

"@webassemblyjs/helper-buffer@1.8.5":
  version "1.8.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/@webassemblyjs/helper-buffer/-/helper-buffer-1.8.5.tgz#fea93e429863dd5e4338555f42292385a653f204"
  integrity sha1-/qk+Qphj3V5DOFVfQikjhaZT8gQ=

"@webassemblyjs/helper-code-frame@1.8.5":
  version "1.8.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/@webassemblyjs/helper-code-frame/-/helper-code-frame-1.8.5.tgz#9a740ff48e3faa3022b1dff54423df9aa293c25e"
  integrity sha1-mnQP9I4/qjAisd/1RCPfmqKTwl4=
  dependencies:
    "@webassemblyjs/wast-printer" "1.8.5"

"@webassemblyjs/helper-fsm@1.8.5":
  version "1.8.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/@webassemblyjs/helper-fsm/-/helper-fsm-1.8.5.tgz#ba0b7d3b3f7e4733da6059c9332275d860702452"
  integrity sha1-ugt9Oz9+RzPaYFnJMyJ12GBwJFI=

"@webassemblyjs/helper-module-context@1.8.5":
  version "1.8.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/@webassemblyjs/helper-module-context/-/helper-module-context-1.8.5.tgz#def4b9927b0101dc8cbbd8d1edb5b7b9c82eb245"
  integrity sha1-3vS5knsBAdyMu9jR7bW3ucguskU=
  dependencies:
    "@webassemblyjs/ast" "1.8.5"
    mamacro "^0.0.3"

"@webassemblyjs/helper-wasm-bytecode@1.8.5":
  version "1.8.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/@webassemblyjs/helper-wasm-bytecode/-/helper-wasm-bytecode-1.8.5.tgz#537a750eddf5c1e932f3744206551c91c1b93e61"
  integrity sha1-U3p1Dt31weky83RCBlUckcG5PmE=

"@webassemblyjs/helper-wasm-section@1.8.5":
  version "1.8.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.8.5.tgz#74ca6a6bcbe19e50a3b6b462847e69503e6bfcbf"
  integrity sha1-dMpqa8vhnlCjtrRihH5pUD5r/L8=
  dependencies:
    "@webassemblyjs/ast" "1.8.5"
    "@webassemblyjs/helper-buffer" "1.8.5"
    "@webassemblyjs/helper-wasm-bytecode" "1.8.5"
    "@webassemblyjs/wasm-gen" "1.8.5"

"@webassemblyjs/ieee754@1.8.5":
  version "1.8.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/@webassemblyjs/ieee754/-/ieee754-1.8.5.tgz#712329dbef240f36bf57bd2f7b8fb9bf4154421e"
  integrity sha1-cSMp2+8kDza/V70ve4+5v0FUQh4=
  dependencies:
    "@xtuc/ieee754" "^1.2.0"

"@webassemblyjs/leb128@1.8.5":
  version "1.8.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/@webassemblyjs/leb128/-/leb128-1.8.5.tgz#044edeb34ea679f3e04cd4fd9824d5e35767ae10"
  integrity sha1-BE7es06mefPgTNT9mCTV41dnrhA=
  dependencies:
    "@xtuc/long" "4.2.2"

"@webassemblyjs/utf8@1.8.5":
  version "1.8.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/@webassemblyjs/utf8/-/utf8-1.8.5.tgz#a8bf3b5d8ffe986c7c1e373ccbdc2a0915f0cedc"
  integrity sha1-qL87XY/+mGx8Hjc8y9wqCRXwztw=

"@webassemblyjs/wasm-edit@1.8.5":
  version "1.8.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/@webassemblyjs/wasm-edit/-/wasm-edit-1.8.5.tgz#962da12aa5acc1c131c81c4232991c82ce56e01a"
  integrity sha1-li2hKqWswcExyBxCMpkcgs5W4Bo=
  dependencies:
    "@webassemblyjs/ast" "1.8.5"
    "@webassemblyjs/helper-buffer" "1.8.5"
    "@webassemblyjs/helper-wasm-bytecode" "1.8.5"
    "@webassemblyjs/helper-wasm-section" "1.8.5"
    "@webassemblyjs/wasm-gen" "1.8.5"
    "@webassemblyjs/wasm-opt" "1.8.5"
    "@webassemblyjs/wasm-parser" "1.8.5"
    "@webassemblyjs/wast-printer" "1.8.5"

"@webassemblyjs/wasm-gen@1.8.5":
  version "1.8.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/@webassemblyjs/wasm-gen/-/wasm-gen-1.8.5.tgz#54840766c2c1002eb64ed1abe720aded714f98bc"
  integrity sha1-VIQHZsLBAC62TtGr5yCt7XFPmLw=
  dependencies:
    "@webassemblyjs/ast" "1.8.5"
    "@webassemblyjs/helper-wasm-bytecode" "1.8.5"
    "@webassemblyjs/ieee754" "1.8.5"
    "@webassemblyjs/leb128" "1.8.5"
    "@webassemblyjs/utf8" "1.8.5"

"@webassemblyjs/wasm-opt@1.8.5":
  version "1.8.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/@webassemblyjs/wasm-opt/-/wasm-opt-1.8.5.tgz#b24d9f6ba50394af1349f510afa8ffcb8a63d264"
  integrity sha1-sk2fa6UDlK8TSfUQr6j/y4pj0mQ=
  dependencies:
    "@webassemblyjs/ast" "1.8.5"
    "@webassemblyjs/helper-buffer" "1.8.5"
    "@webassemblyjs/wasm-gen" "1.8.5"
    "@webassemblyjs/wasm-parser" "1.8.5"

"@webassemblyjs/wasm-parser@1.8.5":
  version "1.8.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/@webassemblyjs/wasm-parser/-/wasm-parser-1.8.5.tgz#21576f0ec88b91427357b8536383668ef7c66b8d"
  integrity sha1-IVdvDsiLkUJzV7hTY4NmjvfGa40=
  dependencies:
    "@webassemblyjs/ast" "1.8.5"
    "@webassemblyjs/helper-api-error" "1.8.5"
    "@webassemblyjs/helper-wasm-bytecode" "1.8.5"
    "@webassemblyjs/ieee754" "1.8.5"
    "@webassemblyjs/leb128" "1.8.5"
    "@webassemblyjs/utf8" "1.8.5"

"@webassemblyjs/wast-parser@1.8.5":
  version "1.8.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/@webassemblyjs/wast-parser/-/wast-parser-1.8.5.tgz#e10eecd542d0e7bd394f6827c49f3df6d4eefb8c"
  integrity sha1-4Q7s1ULQ5705T2gnxJ899tTu+4w=
  dependencies:
    "@webassemblyjs/ast" "1.8.5"
    "@webassemblyjs/floating-point-hex-parser" "1.8.5"
    "@webassemblyjs/helper-api-error" "1.8.5"
    "@webassemblyjs/helper-code-frame" "1.8.5"
    "@webassemblyjs/helper-fsm" "1.8.5"
    "@xtuc/long" "4.2.2"

"@webassemblyjs/wast-printer@1.8.5":
  version "1.8.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/@webassemblyjs/wast-printer/-/wast-printer-1.8.5.tgz#114bbc481fd10ca0e23b3560fa812748b0bae5bc"
  integrity sha1-EUu8SB/RDKDiOzVg+oEnSLC65bw=
  dependencies:
    "@webassemblyjs/ast" "1.8.5"
    "@webassemblyjs/wast-parser" "1.8.5"
    "@xtuc/long" "4.2.2"

"@xtuc/ieee754@^1.2.0":
  version "1.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/@xtuc/ieee754/-/ieee754-1.2.0.tgz#eef014a3145ae477a1cbc00cd1e552336dceb790"
  integrity sha1-7vAUoxRa5Hehy8AM0eVSM23Ot5A=

"@xtuc/long@4.2.2":
  version "4.2.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/@xtuc/long/-/long-4.2.2.tgz#d291c6a4e97989b5c61d9acf396ae4fe133a718d"
  integrity sha1-0pHGpOl5ibXGHZrPOWrk/hM6cY0=

abab@^2.0.0:
  version "2.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/abab/-/abab-2.0.0.tgz#aba0ab4c5eee2d4c79d3487d85450fb2376ebb0f"
  integrity sha1-q6CrTF7uLUx500h9hUUPsjduuw8=

abbrev@1:
  version "1.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/abbrev/-/abbrev-1.1.1.tgz#f8f2c887ad10bf67f634f005b6987fed3179aac8"
  integrity sha1-+PLIh60Qv2f2NPAFtph/7TF5qsg=

accepts@~1.3.4, accepts@~1.3.5, accepts@~1.3.7:
  version "1.3.7"
  resolved "https://nexus.lanmaoly.com/repository/npm/accepts/-/accepts-1.3.7.tgz#531bc726517a3b2b41f850021c6cc15eaab507cd"
  integrity sha1-UxvHJlF6OytB+FACHGzBXqq1B80=
  dependencies:
    mime-types "~2.1.24"
    negotiator "0.6.2"

acorn-globals@^4.1.0:
  version "4.3.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/acorn-globals/-/acorn-globals-4.3.0.tgz#e3b6f8da3c1552a95ae627571f7dd6923bb54103"
  integrity sha1-47b42jwVUqla5idXH33Wkju1QQM=
  dependencies:
    acorn "^6.0.1"
    acorn-walk "^6.0.1"

acorn-jsx@^5.0.0:
  version "5.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/acorn-jsx/-/acorn-jsx-5.0.1.tgz#32a064fd925429216a09b141102bfdd185fae40e"
  integrity sha1-MqBk/ZJUKSFqCbFBECv90YX65A4=

acorn-walk@^6.0.1, acorn-walk@^6.1.1:
  version "6.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/acorn-walk/-/acorn-walk-6.2.0.tgz#123cb8f3b84c2171f1f7fb252615b1c78a6b1a8c"
  integrity sha1-Ejy487hMIXHx9/slJhWxx4prGow=

acorn@^5.5.3:
  version "5.7.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/acorn/-/acorn-5.7.3.tgz#67aa231bf8812974b85235a96771eb6bd07ea279"
  integrity sha1-Z6ojG/iBKXS4UjWpZ3Hra9B+onk=

acorn@^6.0.1, acorn@^6.0.7, acorn@^6.2.0:
  version "6.2.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/acorn/-/acorn-6.2.1.tgz#3ed8422d6dec09e6121cc7a843ca86a330a86b51"
  integrity sha1-PthCLW3sCeYSHMeoQ8qGozCoa1E=

add-dom-event-listener@^1.1.0:
  version "1.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/add-dom-event-listener/-/add-dom-event-listener-1.1.0.tgz#6a92db3a0dd0abc254e095c0f1dc14acbbaae310"
  integrity sha1-apLbOg3Qq8JU4JXA8dwUrLuq4xA=
  dependencies:
    object-assign "4.x"

ajv-errors@^1.0.0:
  version "1.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/ajv-errors/-/ajv-errors-1.0.1.tgz#f35986aceb91afadec4102fbd85014950cefa64d"
  integrity sha1-81mGrOuRr63sQQL72FAUlQzvpk0=

ajv-keywords@^3.1.0:
  version "3.4.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/ajv-keywords/-/ajv-keywords-3.4.1.tgz#ef916e271c64ac12171fd8384eaae6b2345854da"
  integrity sha1-75FuJxxkrBIXH9g4TqrmsjRYVNo=

ajv-keywords@^3.5.2:
  version "3.5.2"
  resolved "https://r.cnpmjs.org/ajv-keywords/download/ajv-keywords-3.5.2.tgz#31f29da5ab6e00d1c2d329acf7b5929614d5014d"
  integrity sha1-MfKdpatuANHC0yms97WSlhTVAU0=

ajv@^6.1.0, ajv@^6.10.2, ajv@^6.5.5, ajv@^6.9.1:
  version "6.10.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/ajv/-/ajv-6.10.2.tgz#d3cea04d6b017b2894ad69040fec8b623eb4bd52"
  integrity sha1-086gTWsBeyiUrWkED+yLYj60vVI=
  dependencies:
    fast-deep-equal "^2.0.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ajv@^6.12.5:
  version "6.12.6"
  resolved "https://r.cnpmjs.org/ajv/download/ajv-6.12.6.tgz#baf5a62e802b07d977034586f8c3baf5adf26df4"
  integrity sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

amdefine@>=0.0.4:
  version "1.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/amdefine/-/amdefine-1.0.1.tgz#4a5282ac164729e93619bcfd3ad151f817ce91f5"
  integrity sha1-SlKCrBZHKek2Gbz9OtFR+BfOkfU=

ansi-colors@^3.0.0:
  version "3.2.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/ansi-colors/-/ansi-colors-3.2.4.tgz#e3a3da4bfbae6c86a9c285625de124a234026fbf"
  integrity sha1-46PaS/uubIapwoViXeEkojQCb78=

ansi-escapes@^3.0.0, ansi-escapes@^3.2.0:
  version "3.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/ansi-escapes/-/ansi-escapes-3.2.0.tgz#8780b98ff9dbf5638152d1f1fe5c1d7b4442976b"
  integrity sha1-h4C5j/nb9WOBUtHx/lwde0RCl2s=

ansi-html@0.0.7:
  version "0.0.7"
  resolved "https://nexus.lanmaoly.com/repository/npm/ansi-html/-/ansi-html-0.0.7.tgz#813584021962a9e9e6fd039f940d12f56ca7859e"
  integrity sha1-gTWEAhliqenm/QOflA0S9WynhZ4=

ansi-regex@^2.0.0:
  version "2.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/ansi-regex/-/ansi-regex-2.1.1.tgz#c3b33ab5ee360d86e0e628f0468ae7ef27d654df"
  integrity sha1-w7M6te42DYbg5ijwRorn7yfWVN8=

ansi-regex@^3.0.0:
  version "3.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/ansi-regex/-/ansi-regex-3.0.0.tgz#ed0317c322064f79466c02966bddb605ab37d998"
  integrity sha1-7QMXwyIGT3lGbAKWa922Bas32Zg=

ansi-regex@^4.1.0:
  version "4.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/ansi-regex/-/ansi-regex-4.1.0.tgz#8b9f8f08cf1acb843756a839ca8c7e3168c51997"
  integrity sha1-i5+PCM8ay4Q3Vqg5yox+MWjFGZc=

ansi-styles@^2.2.1:
  version "2.2.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/ansi-styles/-/ansi-styles-2.2.1.tgz#b432dd3358b634cf75e1e4664368240533c1ddbe"
  integrity sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4=

ansi-styles@^3.2.0, ansi-styles@^3.2.1:
  version "3.2.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/ansi-styles/-/ansi-styles-3.2.1.tgz#41fbb20243e50b12be0f04b8dedbf07520ce841d"
  integrity sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=
  dependencies:
    color-convert "^1.9.0"

antd-mobile@^2.2.14:
  version "2.2.14"
  resolved "https://nexus.lanmaoly.com/repository/npm/antd-mobile/-/antd-mobile-2.2.14.tgz#0c3b4da1ef383799ec33f268786860e9cc4533d7"
  integrity sha1-DDtNoe84N5nsM/JoeGhg6cxFM9c=
  dependencies:
    array-tree-filter "~2.1.0"
    babel-runtime "6.x"
    classnames "^2.2.1"
    normalize.css "^7.0.0"
    rc-checkbox "~2.0.0"
    rc-collapse "~1.9.1"
    rc-slider "~8.2.0"
    rc-swipeout "~2.0.0"
    rmc-calendar "^1.0.0"
    rmc-cascader "~5.0.0"
    rmc-date-picker "^6.0.8"
    rmc-dialog "^1.0.1"
    rmc-drawer "^0.4.11"
    rmc-feedback "^2.0.0"
    rmc-input-number "^1.0.0"
    rmc-list-view "^0.11.0"
    rmc-notification "~1.0.0"
    rmc-nuka-carousel "~3.0.0"
    rmc-picker "~5.0.0"
    rmc-pull-to-refresh "~1.0.1"
    rmc-steps "~1.0.0"
    rmc-tabs "~1.2.0"
    rmc-tooltip "~1.0.0"

anymatch@^2.0.0:
  version "2.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/anymatch/-/anymatch-2.0.0.tgz#bcb24b4f37934d9aa7ac17b4adaf89e7c76ef2eb"
  integrity sha1-vLJLTzeTTZqnrBe0ra+J58du8us=
  dependencies:
    micromatch "^3.1.4"
    normalize-path "^2.1.1"

append-transform@^0.4.0:
  version "0.4.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/append-transform/-/append-transform-0.4.0.tgz#d76ebf8ca94d276e247a36bad44a4b74ab611991"
  integrity sha1-126/jKlNJ24keja61EpLdKthGZE=
  dependencies:
    default-require-extensions "^1.0.0"

aproba@^1.0.3, aproba@^1.1.1:
  version "1.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/aproba/-/aproba-1.2.0.tgz#6802e6264efd18c790a1b0d517f0f2627bf2c94a"
  integrity sha1-aALmJk79GMeQobDVF/DyYnvyyUo=

are-we-there-yet@~1.1.2:
  version "1.1.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/are-we-there-yet/-/are-we-there-yet-1.1.5.tgz#4b35c2944f062a8bfcda66410760350fe9ddfc21"
  integrity sha1-SzXClE8GKov82mZBB2A1D+nd/CE=
  dependencies:
    delegates "^1.0.0"
    readable-stream "^2.0.6"

argparse@^1.0.7:
  version "1.0.10"
  resolved "https://nexus.lanmaoly.com/repository/npm/argparse/-/argparse-1.0.10.tgz#bcd6791ea5ae09725e17e5ad988134cd40b3d911"
  integrity sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=
  dependencies:
    sprintf-js "~1.0.2"

aria-query@^3.0.0:
  version "3.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/aria-query/-/aria-query-3.0.0.tgz#65b3fcc1ca1155a8c9ae64d6eee297f15d5133cc"
  integrity sha1-ZbP8wcoRVajJrmTW7uKX8V1RM8w=
  dependencies:
    ast-types-flow "0.0.7"
    commander "^2.11.0"

arr-diff@^2.0.0:
  version "2.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/arr-diff/-/arr-diff-2.0.0.tgz#8f3b827f955a8bd669697e4a4256ac3ceae356cf"
  integrity sha1-jzuCf5Vai9ZpaX5KQlasPOrjVs8=
  dependencies:
    arr-flatten "^1.0.1"

arr-diff@^4.0.0:
  version "4.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/arr-diff/-/arr-diff-4.0.0.tgz#d6461074febfec71e7e15235761a329a5dc7c520"
  integrity sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA=

arr-flatten@^1.0.1, arr-flatten@^1.1.0:
  version "1.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/arr-flatten/-/arr-flatten-1.1.0.tgz#36048bbff4e7b47e136644316c99669ea5ae91f1"
  integrity sha1-NgSLv/TntH4TZkQxbJlmnqWukfE=

arr-union@^3.1.0:
  version "3.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/arr-union/-/arr-union-3.1.0.tgz#e39b09aea9def866a8f206e288af63919bae39c4"
  integrity sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ=

array-equal@^1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/array-equal/-/array-equal-1.0.0.tgz#8c2a5ef2472fd9ea742b04c77a75093ba2757c93"
  integrity sha1-jCpe8kcv2ep0KwTHenUJO6J1fJM=

array-filter@~0.0.0:
  version "0.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/array-filter/-/array-filter-0.0.1.tgz#7da8cf2e26628ed732803581fd21f67cacd2eeec"
  integrity sha1-fajPLiZijtcygDWB/SH2fKzS7uw=

array-find-index@^1.0.1:
  version "1.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/array-find-index/-/array-find-index-1.0.2.tgz#df010aa1287e164bbda6f9723b0a96a1ec4187a1"
  integrity sha1-3wEKoSh+Fku9pvlyOwqWoexBh6E=

array-flatten@1.1.1:
  version "1.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/array-flatten/-/array-flatten-1.1.1.tgz#9a5f699051b1e7073328f2a008968b64ea2955d2"
  integrity sha1-ml9pkFGx5wczKPKgCJaLZOopVdI=

array-flatten@^2.1.0:
  version "2.1.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/array-flatten/-/array-flatten-2.1.2.tgz#24ef80a28c1a893617e2149b0c6d0d788293b099"
  integrity sha1-JO+AoowaiTYX4hSbDG0NeIKTsJk=

array-includes@^3.0.3:
  version "3.0.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/array-includes/-/array-includes-3.0.3.tgz#184b48f62d92d7452bb31b323165c7f8bd02266d"
  integrity sha1-GEtI9i2S10UrsxsyMWXH+L0CJm0=
  dependencies:
    define-properties "^1.1.2"
    es-abstract "^1.7.0"

array-map@~0.0.0:
  version "0.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/array-map/-/array-map-0.0.0.tgz#88a2bab73d1cf7bcd5c1b118a003f66f665fa662"
  integrity sha1-iKK6tz0c97zVwbEYoAP2b2ZfpmI=

array-reduce@~0.0.0:
  version "0.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/array-reduce/-/array-reduce-0.0.0.tgz#173899d3ffd1c7d9383e4479525dbe278cab5f2b"
  integrity sha1-FziZ0//Rx9k4PkR5Ul2+J4yrXys=

array-tree-filter@2.1.x, array-tree-filter@~2.1.0:
  version "2.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/array-tree-filter/-/array-tree-filter-2.1.0.tgz#873ac00fec83749f255ac8dd083814b4f6329190"
  integrity sha1-hzrAD+yDdJ8lWsjdCDgUtPYykZA=

array-union@^1.0.1:
  version "1.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/array-union/-/array-union-1.0.2.tgz#9a34410e4f4e3da23dea375be5be70f24778ec39"
  integrity sha1-mjRBDk9OPaI96jdb5b5w8kd47Dk=
  dependencies:
    array-uniq "^1.0.1"

array-uniq@^1.0.1:
  version "1.0.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/array-uniq/-/array-uniq-1.0.3.tgz#af6ac877a25cc7f74e058894753858dfdb24fdb6"
  integrity sha1-r2rId6Jcx/dOBYiUdThY39sk/bY=

array-unique@^0.2.1:
  version "0.2.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/array-unique/-/array-unique-0.2.1.tgz#a1d97ccafcbc2625cc70fadceb36a50c58b01a53"
  integrity sha1-odl8yvy8JiXMcPrc6zalDFiwGlM=

array-unique@^0.3.2:
  version "0.3.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/array-unique/-/array-unique-0.3.2.tgz#a894b75d4bc4f6cd679ef3244a9fd8f46ae2d428"
  integrity sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg=

arrify@^1.0.1:
  version "1.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/arrify/-/arrify-1.0.1.tgz#898508da2226f380df904728456849c1501a4b0d"
  integrity sha1-iYUI2iIm84DfkEcoRWhJwVAaSw0=

asap@~2.0.3:
  version "2.0.6"
  resolved "https://nexus.lanmaoly.com/repository/npm/asap/-/asap-2.0.6.tgz#e50347611d7e690943208bbdafebcbc2fb866d46"
  integrity sha1-5QNHYR1+aQlDIIu9r+vLwvuGbUY=

asn1.js@^4.0.0:
  version "4.10.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/asn1.js/-/asn1.js-4.10.1.tgz#b9c2bf5805f1e64aadeed6df3a2bfafb5a73f5a0"
  integrity sha1-ucK/WAXx5kqt7tbfOiv6+1pz9aA=
  dependencies:
    bn.js "^4.0.0"
    inherits "^2.0.1"
    minimalistic-assert "^1.0.0"

asn1@~0.2.3:
  version "0.2.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/asn1/-/asn1-0.2.4.tgz#8d2475dfab553bb33e77b54e59e880bb8ce23136"
  integrity sha1-jSR136tVO7M+d7VOWeiAu4ziMTY=
  dependencies:
    safer-buffer "~2.1.0"

assert-plus@1.0.0, assert-plus@^1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/assert-plus/-/assert-plus-1.0.0.tgz#f12e0f3c5d77b0b1cdd9146942e4e96c1e4dd525"
  integrity sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=

assert@^1.1.1:
  version "1.5.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/assert/-/assert-1.5.0.tgz#55c109aaf6e0aefdb3dc4b71240c70bf574b18eb"
  integrity sha1-VcEJqvbgrv2z3EtxJAxwv1dLGOs=
  dependencies:
    object-assign "^4.1.1"
    util "0.10.3"

assign-symbols@^1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/assign-symbols/-/assign-symbols-1.0.0.tgz#59667f41fadd4f20ccbc2bb96b8d4f7f78ec0367"
  integrity sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c=

ast-types-flow@0.0.7, ast-types-flow@^0.0.7:
  version "0.0.7"
  resolved "https://nexus.lanmaoly.com/repository/npm/ast-types-flow/-/ast-types-flow-0.0.7.tgz#f70b735c6bca1a5c9c22d982c3e39e7feba3bdad"
  integrity sha1-9wtzXGvKGlycItmCw+Oef+ujva0=

astral-regex@^1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/astral-regex/-/astral-regex-1.0.0.tgz#6c8c3fb827dd43ee3918f27b82782ab7658a6fd9"
  integrity sha1-bIw/uCfdQ+45GPJ7gngqt2WKb9k=

async-each@^1.0.1:
  version "1.0.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/async-each/-/async-each-1.0.3.tgz#b727dbf87d7651602f06f4d4ac387f47d91b0cbf"
  integrity sha1-tyfb+H12UWAvBvTUrDh/R9kbDL8=

async-foreach@^0.1.3:
  version "0.1.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/async-foreach/-/async-foreach-0.1.3.tgz#36121f845c0578172de419a97dbeb1d16ec34542"
  integrity sha1-NhIfhFwFeBct5Bmpfb6x0W7DRUI=

async-limiter@~1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/async-limiter/-/async-limiter-1.0.0.tgz#78faed8c3d074ab81f22b4e985d79e8738f720f8"
  integrity sha1-ePrtjD0HSrgfIrTphdeehzj3IPg=

async-validator@~1.11.3:
  version "1.11.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/async-validator/-/async-validator-1.11.5.tgz#9d43cf49ef6bb76be5442388d19fb9a6e47597ea"
  integrity sha1-nUPPSe9rt2vlRCOI0Z+5puR1l+o=

async@^1.5.2:
  version "1.5.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/async/-/async-1.5.2.tgz#ec6a61ae56480c0c3cb241c95618e20892f9672a"
  integrity sha1-7GphrlZIDAw8skHJVhjiCJL5Zyo=

async@^2.1.4:
  version "2.6.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/async/-/async-2.6.3.tgz#d72625e2344a3656e3a3ad4fa749fa83299d82ff"
  integrity sha1-1yYl4jRKNlbjo61Pp0n6gymdgv8=
  dependencies:
    lodash "^4.17.14"

asynckit@^0.4.0:
  version "0.4.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/asynckit/-/asynckit-0.4.0.tgz#c79ed97f7f34cb8f2ba1bc9790bcc366474b4b79"
  integrity sha1-x57Zf380y48robyXkLzDZkdLS3k=

atob@^2.1.1:
  version "2.1.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/atob/-/atob-2.1.2.tgz#6d9517eb9e030d2436666651e86bd9f6f13533c9"
  integrity sha1-bZUX654DDSQ2ZmZR6GvZ9vE1M8k=

autoprefixer@9.1.3:
  version "9.1.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/autoprefixer/-/autoprefixer-9.1.3.tgz#bd5940ccb9d1bfa3508308659915f0a14394c8d5"
  integrity sha1-vVlAzLnRv6NQgwhlmRXwoUOUyNU=
  dependencies:
    browserslist "^4.0.2"
    caniuse-lite "^1.0.30000878"
    normalize-range "^0.1.2"
    num2fraction "^1.2.2"
    postcss "^7.0.2"
    postcss-value-parser "^3.2.3"

aws-sign2@~0.7.0:
  version "0.7.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/aws-sign2/-/aws-sign2-0.7.0.tgz#b46e890934a9591f2d2f6f86d7e6a9f1b3fe76a8"
  integrity sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg=

aws4@^1.8.0:
  version "1.8.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/aws4/-/aws4-1.8.0.tgz#f0e003d9ca9e7f59c7a508945d7b2ef9a04a542f"
  integrity sha1-8OAD2cqef1nHpQiUXXsu+aBKVC8=

axios@0.16.2:
  version "0.16.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/axios/-/axios-0.16.2.tgz#ba4f92f17167dfbab40983785454b9ac149c3c6d"
  integrity sha1-uk+S8XFn37q0CYN4VFS5rBScPG0=
  dependencies:
    follow-redirects "^1.2.3"
    is-buffer "^1.1.5"

axios@^0.18.0:
  version "0.18.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/axios/-/axios-0.18.1.tgz#ff3f0de2e7b5d180e757ad98000f1081b87bcea3"
  integrity sha1-/z8N4ue10YDnV62YAA8Qgbh7zqM=
  dependencies:
    follow-redirects "1.5.10"
    is-buffer "^2.0.2"

axobject-query@^2.0.2:
  version "2.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/axobject-query/-/axobject-query-2.0.2.tgz#ea187abe5b9002b377f925d8bf7d1c561adf38f9"
  integrity sha1-6hh6vluQArN3+SXYv30cVhrfOPk=
  dependencies:
    ast-types-flow "0.0.7"

babel-code-frame@^6.26.0:
  version "6.26.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/babel-code-frame/-/babel-code-frame-6.26.0.tgz#63fd43f7dc1e3bb7ce35947db8fe369a3f58c74b"
  integrity sha1-Y/1D99weO7fONZR9uP42mj9Yx0s=
  dependencies:
    chalk "^1.1.3"
    esutils "^2.0.2"
    js-tokens "^3.0.2"

babel-core@^6.0.0, babel-core@^6.26.0:
  version "6.26.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/babel-core/-/babel-core-6.26.3.tgz#b2e2f09e342d0f0c88e2f02e067794125e75c207"
  integrity sha1-suLwnjQtDwyI4vAuBneUEl51wgc=
  dependencies:
    babel-code-frame "^6.26.0"
    babel-generator "^6.26.0"
    babel-helpers "^6.24.1"
    babel-messages "^6.23.0"
    babel-register "^6.26.0"
    babel-runtime "^6.26.0"
    babel-template "^6.26.0"
    babel-traverse "^6.26.0"
    babel-types "^6.26.0"
    babylon "^6.18.0"
    convert-source-map "^1.5.1"
    debug "^2.6.9"
    json5 "^0.5.1"
    lodash "^4.17.4"
    minimatch "^3.0.4"
    path-is-absolute "^1.0.1"
    private "^0.1.8"
    slash "^1.0.0"
    source-map "^0.5.7"

babel-eslint@^9.0.0:
  version "9.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/babel-eslint/-/babel-eslint-9.0.0.tgz#7d9445f81ed9f60aff38115f838970df9f2b6220"
  integrity sha1-fZRF+B7Z9gr/OBFfg4lw358rYiA=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "@babel/parser" "^7.0.0"
    "@babel/traverse" "^7.0.0"
    "@babel/types" "^7.0.0"
    eslint-scope "3.7.1"
    eslint-visitor-keys "^1.0.0"

babel-generator@^6.18.0, babel-generator@^6.26.0:
  version "6.26.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/babel-generator/-/babel-generator-6.26.1.tgz#1844408d3b8f0d35a404ea7ac180f087a601bd90"
  integrity sha1-GERAjTuPDTWkBOp6wYDwh6YBvZA=
  dependencies:
    babel-messages "^6.23.0"
    babel-runtime "^6.26.0"
    babel-types "^6.26.0"
    detect-indent "^4.0.0"
    jsesc "^1.3.0"
    lodash "^4.17.4"
    source-map "^0.5.7"
    trim-right "^1.0.1"

babel-helper-bindify-decorators@^6.24.1:
  version "6.24.1"
  resolved "https://r.cnpmjs.org/babel-helper-bindify-decorators/download/babel-helper-bindify-decorators-6.24.1.tgz#14c19e5f142d7b47f19a52431e52b1ccbc40a330"
  integrity sha1-FMGeXxQte0fxmlJDHlKxzLxAozA=
  dependencies:
    babel-runtime "^6.22.0"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-builder-binary-assignment-operator-visitor@^6.24.1:
  version "6.24.1"
  resolved "https://r.cnpmjs.org/babel-helper-builder-binary-assignment-operator-visitor/download/babel-helper-builder-binary-assignment-operator-visitor-6.24.1.tgz#cce4517ada356f4220bcae8a02c2b346f9a56664"
  integrity sha1-zORReto1b0IgvK6KAsKzRvmlZmQ=
  dependencies:
    babel-helper-explode-assignable-expression "^6.24.1"
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-helper-explode-assignable-expression@^6.24.1:
  version "6.24.1"
  resolved "https://r.cnpmjs.org/babel-helper-explode-assignable-expression/download/babel-helper-explode-assignable-expression-6.24.1.tgz#f25b82cf7dc10433c55f70592d5746400ac22caa"
  integrity sha1-8luCz33BBDPFX3BZLVdGQArCLKo=
  dependencies:
    babel-runtime "^6.22.0"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-explode-class@^6.24.1:
  version "6.24.1"
  resolved "https://r.cnpmjs.org/babel-helper-explode-class/download/babel-helper-explode-class-6.24.1.tgz#7dc2a3910dee007056e1e31d640ced3d54eaa9eb"
  integrity sha1-fcKjkQ3uAHBW4eMdZAztPVTqqes=
  dependencies:
    babel-helper-bindify-decorators "^6.24.1"
    babel-runtime "^6.22.0"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-function-name@^6.24.1:
  version "6.24.1"
  resolved "https://r.cnpmjs.org/babel-helper-function-name/download/babel-helper-function-name-6.24.1.tgz#d3475b8c03ed98242a25b48351ab18399d3580a9"
  integrity sha1-00dbjAPtmCQqJbSDUasYOZ01gKk=
  dependencies:
    babel-helper-get-function-arity "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-get-function-arity@^6.24.1:
  version "6.24.1"
  resolved "https://r.cnpmjs.org/babel-helper-get-function-arity/download/babel-helper-get-function-arity-6.24.1.tgz#8f7782aa93407c41d3aa50908f89b031b1b6853d"
  integrity sha1-j3eCqpNAfEHTqlCQj4mwMbG2hT0=
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-helper-remap-async-to-generator@^6.24.1:
  version "6.24.1"
  resolved "https://r.cnpmjs.org/babel-helper-remap-async-to-generator/download/babel-helper-remap-async-to-generator-6.24.1.tgz#5ec581827ad723fecdd381f1c928390676e4551b"
  integrity sha1-XsWBgnrXI/7N04HxySg5BnbkVRs=
  dependencies:
    babel-helper-function-name "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helpers@^6.24.1:
  version "6.24.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/babel-helpers/-/babel-helpers-6.24.1.tgz#3471de9caec388e5c850e597e58a26ddf37602b2"
  integrity sha1-NHHenK7DiOXIUOWX5Yom3fN2ArI=
  dependencies:
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-jest@^23.6.0:
  version "23.6.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/babel-jest/-/babel-jest-23.6.0.tgz#a644232366557a2240a0c083da6b25786185a2f1"
  integrity sha1-pkQjI2ZVeiJAoMCD2msleGGFovE=
  dependencies:
    babel-plugin-istanbul "^4.1.6"
    babel-preset-jest "^23.2.0"

babel-loader@^8.0.1:
  version "8.0.6"
  resolved "https://nexus.lanmaoly.com/repository/npm/babel-loader/-/babel-loader-8.0.6.tgz#e33bdb6f362b03f4bb141a0c21ab87c501b70dfb"
  integrity sha1-4zvbbzYrA/S7FBoMIauHxQG3Dfs=
  dependencies:
    find-cache-dir "^2.0.0"
    loader-utils "^1.0.2"
    mkdirp "^0.5.1"
    pify "^4.0.1"

babel-messages@^6.23.0:
  version "6.23.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/babel-messages/-/babel-messages-6.23.0.tgz#f3cdf4703858035b2a2951c6ec5edf6c62f2630e"
  integrity sha1-8830cDhYA1sqKVHG7F7fbGLyYw4=
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-dynamic-import-node@^2.3.0:
  version "2.3.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/babel-plugin-dynamic-import-node/-/babel-plugin-dynamic-import-node-2.3.0.tgz#f00f507bdaa3c3e3ff6e7e5e98d90a7acab96f7f"
  integrity sha1-8A9Qe9qjw+P/bn5emNkKesq5b38=
  dependencies:
    object.assign "^4.1.0"

babel-plugin-import@^1.12.0:
  version "1.12.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/babel-plugin-import/-/babel-plugin-import-1.12.0.tgz#12d50950aecfd8de3f10880730221f0f0843a755"
  integrity sha1-EtUJUK7P2N4/EIgHMCIfDwhDp1U=
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"
    "@babel/runtime" "^7.0.0"

babel-plugin-istanbul@^4.1.6:
  version "4.1.6"
  resolved "https://nexus.lanmaoly.com/repository/npm/babel-plugin-istanbul/-/babel-plugin-istanbul-4.1.6.tgz#36c59b2192efce81c5b378321b74175add1c9a45"
  integrity sha1-NsWbIZLvzoHFs3gyG3QXWt0cmkU=
  dependencies:
    babel-plugin-syntax-object-rest-spread "^6.13.0"
    find-up "^2.1.0"
    istanbul-lib-instrument "^1.10.1"
    test-exclude "^4.2.1"

babel-plugin-jest-hoist@^23.2.0:
  version "23.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-23.2.0.tgz#e61fae05a1ca8801aadee57a6d66b8cefaf44167"
  integrity sha1-5h+uBaHKiAGq3uV6bWa4zvr0QWc=

babel-plugin-module-resolver@^3.1.1:
  version "3.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/babel-plugin-module-resolver/-/babel-plugin-module-resolver-3.2.0.tgz#ddfa5e301e3b9aa12d852a9979f18b37881ff5a7"
  integrity sha1-***************************=
  dependencies:
    find-babel-config "^1.1.0"
    glob "^7.1.2"
    pkg-up "^2.0.0"
    reselect "^3.0.1"
    resolve "^1.4.0"

babel-plugin-syntax-async-functions@^6.8.0:
  version "6.13.0"
  resolved "https://r.cnpmjs.org/babel-plugin-syntax-async-functions/download/babel-plugin-syntax-async-functions-6.13.0.tgz#cad9cad1191b5ad634bf30ae0872391e0647be95"
  integrity sha1-ytnK0RkbWtY0vzCuCHI5HgZHvpU=

babel-plugin-syntax-async-generators@^6.5.0:
  version "6.13.0"
  resolved "https://r.cnpmjs.org/babel-plugin-syntax-async-generators/download/babel-plugin-syntax-async-generators-6.13.0.tgz#6bc963ebb16eccbae6b92b596eb7f35c342a8b9a"
  integrity sha1-a8lj67FuzLrmuStZbrfzXDQqi5o=

babel-plugin-syntax-class-properties@^6.8.0:
  version "6.13.0"
  resolved "https://r.cnpmjs.org/babel-plugin-syntax-class-properties/download/babel-plugin-syntax-class-properties-6.13.0.tgz#d7eb23b79a317f8543962c505b827c7d6cac27de"
  integrity sha1-1+sjt5oxf4VDlixQW4J8fWysJ94=

babel-plugin-syntax-decorators@^6.13.0:
  version "6.13.0"
  resolved "https://r.cnpmjs.org/babel-plugin-syntax-decorators/download/babel-plugin-syntax-decorators-6.13.0.tgz#312563b4dbde3cc806cee3e416cceeaddd11ac0b"
  integrity sha1-MSVjtNvePMgGzuPkFszurd0RrAs=

babel-plugin-syntax-dynamic-import@^6.18.0:
  version "6.18.0"
  resolved "https://r.cnpmjs.org/babel-plugin-syntax-dynamic-import/download/babel-plugin-syntax-dynamic-import-6.18.0.tgz#8d6a26229c83745a9982a441051572caa179b1da"
  integrity sha1-jWomIpyDdFqZgqRBBRVyyqF5sdo=

babel-plugin-syntax-exponentiation-operator@^6.8.0:
  version "6.13.0"
  resolved "https://r.cnpmjs.org/babel-plugin-syntax-exponentiation-operator/download/babel-plugin-syntax-exponentiation-operator-6.13.0.tgz#9ee7e8337290da95288201a6a57f4170317830de"
  integrity sha1-nufoM3KQ2pUoggGmpX9BcDF4MN4=

babel-plugin-syntax-object-rest-spread@^6.13.0, babel-plugin-syntax-object-rest-spread@^6.8.0:
  version "6.13.0"
  resolved "https://r.cnpmjs.org/babel-plugin-syntax-object-rest-spread/download/babel-plugin-syntax-object-rest-spread-6.13.0.tgz#fd6536f2bce13836ffa3a5458c4903a597bb3bf5"
  integrity sha1-/WU28rzhODb/o6VFjEkDpZe7O/U=

babel-plugin-syntax-trailing-function-commas@^6.22.0:
  version "6.22.0"
  resolved "https://r.cnpmjs.org/babel-plugin-syntax-trailing-function-commas/download/babel-plugin-syntax-trailing-function-commas-6.22.0.tgz#ba0360937f8d06e40180a43fe0d5616fff532cf3"
  integrity sha1-ugNgk3+NBuQBgKQ/4NVhb/9TLPM=

babel-plugin-transform-async-generator-functions@^6.24.1:
  version "6.24.1"
  resolved "https://r.cnpmjs.org/babel-plugin-transform-async-generator-functions/download/babel-plugin-transform-async-generator-functions-6.24.1.tgz#f058900145fd3e9907a6ddf28da59f215258a5db"
  integrity sha1-8FiQAUX9PpkHpt3yjaWfIVJYpds=
  dependencies:
    babel-helper-remap-async-to-generator "^6.24.1"
    babel-plugin-syntax-async-generators "^6.5.0"
    babel-runtime "^6.22.0"

babel-plugin-transform-async-to-generator@^6.24.1:
  version "6.24.1"
  resolved "https://r.cnpmjs.org/babel-plugin-transform-async-to-generator/download/babel-plugin-transform-async-to-generator-6.24.1.tgz#6536e378aff6cb1d5517ac0e40eb3e9fc8d08761"
  integrity sha1-ZTbjeK/2yx1VF6wOQOs+n8jQh2E=
  dependencies:
    babel-helper-remap-async-to-generator "^6.24.1"
    babel-plugin-syntax-async-functions "^6.8.0"
    babel-runtime "^6.22.0"

babel-plugin-transform-class-properties@^6.24.1:
  version "6.24.1"
  resolved "https://r.cnpmjs.org/babel-plugin-transform-class-properties/download/babel-plugin-transform-class-properties-6.24.1.tgz#6a79763ea61d33d36f37b611aa9def81a81b46ac"
  integrity sha1-anl2PqYdM9NvN7YRqp3vgagbRqw=
  dependencies:
    babel-helper-function-name "^6.24.1"
    babel-plugin-syntax-class-properties "^6.8.0"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-plugin-transform-decorators@^6.24.1:
  version "6.24.1"
  resolved "https://r.cnpmjs.org/babel-plugin-transform-decorators/download/babel-plugin-transform-decorators-6.24.1.tgz#788013d8f8c6b5222bdf7b344390dfd77569e24d"
  integrity sha1-eIAT2PjGtSIr33s0Q5Df13Vp4k0=
  dependencies:
    babel-helper-explode-class "^6.24.1"
    babel-plugin-syntax-decorators "^6.13.0"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-types "^6.24.1"

babel-plugin-transform-exponentiation-operator@^6.24.1:
  version "6.24.1"
  resolved "https://r.cnpmjs.org/babel-plugin-transform-exponentiation-operator/download/babel-plugin-transform-exponentiation-operator-6.24.1.tgz#2ab0c9c7f3098fa48907772bb813fe41e8de3a0e"
  integrity sha1-KrDJx/MJj6SJB3cruBP+QejeOg4=
  dependencies:
    babel-helper-builder-binary-assignment-operator-visitor "^6.24.1"
    babel-plugin-syntax-exponentiation-operator "^6.8.0"
    babel-runtime "^6.22.0"

babel-plugin-transform-object-rest-spread@^6.22.0:
  version "6.26.0"
  resolved "https://r.cnpmjs.org/babel-plugin-transform-object-rest-spread/download/babel-plugin-transform-object-rest-spread-6.26.0.tgz#0f36692d50fef6b7e2d4b3ac1478137a963b7b06"
  integrity sha1-DzZpLVD+9rfi1LOsFHgTepY7ewY=
  dependencies:
    babel-plugin-syntax-object-rest-spread "^6.8.0"
    babel-runtime "^6.26.0"

babel-polyfill@^6.26.0:
  version "6.26.0"
  resolved "https://r.cnpmjs.org/babel-polyfill/download/babel-polyfill-6.26.0.tgz#379937abc67d7895970adc621f284cd966cf2153"
  integrity sha1-N5k3q8Z9eJWXCtxiHyhM2WbPIVM=
  dependencies:
    babel-runtime "^6.26.0"
    core-js "^2.5.0"
    regenerator-runtime "^0.10.5"

babel-preset-jest@^23.2.0:
  version "23.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/babel-preset-jest/-/babel-preset-jest-23.2.0.tgz#8ec7a03a138f001a1a8fb1e8113652bf1a55da46"
  integrity sha1-jsegOhOPABoaj7HoETZSvxpV2kY=
  dependencies:
    babel-plugin-jest-hoist "^23.2.0"
    babel-plugin-syntax-object-rest-spread "^6.13.0"

babel-preset-stage-2@^6.24.1:
  version "6.24.1"
  resolved "https://r.cnpmjs.org/babel-preset-stage-2/download/babel-preset-stage-2-6.24.1.tgz#d9e2960fb3d71187f0e64eec62bc07767219bdc1"
  integrity sha1-2eKWD7PXEYfw5k7sYrwHdnIZvcE=
  dependencies:
    babel-plugin-syntax-dynamic-import "^6.18.0"
    babel-plugin-transform-class-properties "^6.24.1"
    babel-plugin-transform-decorators "^6.24.1"
    babel-preset-stage-3 "^6.24.1"

babel-preset-stage-3@^6.24.1:
  version "6.24.1"
  resolved "https://r.cnpmjs.org/babel-preset-stage-3/download/babel-preset-stage-3-6.24.1.tgz#836ada0a9e7a7fa37cb138fb9326f87934a48395"
  integrity sha1-g2raCp56f6N8sTj7kyb4eTSkg5U=
  dependencies:
    babel-plugin-syntax-trailing-function-commas "^6.22.0"
    babel-plugin-transform-async-generator-functions "^6.24.1"
    babel-plugin-transform-async-to-generator "^6.24.1"
    babel-plugin-transform-exponentiation-operator "^6.24.1"
    babel-plugin-transform-object-rest-spread "^6.22.0"

babel-register@^6.26.0:
  version "6.26.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/babel-register/-/babel-register-6.26.0.tgz#6ed021173e2fcb486d7acb45c6009a856f647071"
  integrity sha1-btAhFz4vy0htestFxgCahW9kcHE=
  dependencies:
    babel-core "^6.26.0"
    babel-runtime "^6.26.0"
    core-js "^2.5.0"
    home-or-tmp "^2.0.0"
    lodash "^4.17.4"
    mkdirp "^0.5.1"
    source-map-support "^0.4.15"

babel-runtime@6.x, babel-runtime@^6.22.0, babel-runtime@^6.23.0, babel-runtime@^6.26.0:
  version "6.26.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/babel-runtime/-/babel-runtime-6.26.0.tgz#965c7058668e82b55d7bfe04ff2337bc8b5647fe"
  integrity sha1-llxwWGaOgrVde/4E/yM3vItWR/4=
  dependencies:
    core-js "^2.4.0"
    regenerator-runtime "^0.11.0"

babel-template@^6.16.0, babel-template@^6.24.1, babel-template@^6.26.0:
  version "6.26.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/babel-template/-/babel-template-6.26.0.tgz#de03e2d16396b069f46dd9fff8521fb1a0e35e02"
  integrity sha1-3gPi0WOWsGn0bdn/+FIfsaDjXgI=
  dependencies:
    babel-runtime "^6.26.0"
    babel-traverse "^6.26.0"
    babel-types "^6.26.0"
    babylon "^6.18.0"
    lodash "^4.17.4"

babel-traverse@^6.0.0, babel-traverse@^6.18.0, babel-traverse@^6.24.1, babel-traverse@^6.26.0:
  version "6.26.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/babel-traverse/-/babel-traverse-6.26.0.tgz#46a9cbd7edcc62c8e5c064e2d2d8d0f4035766ee"
  integrity sha1-RqnL1+3MYsjlwGTi0tjQ9ANXZu4=
  dependencies:
    babel-code-frame "^6.26.0"
    babel-messages "^6.23.0"
    babel-runtime "^6.26.0"
    babel-types "^6.26.0"
    babylon "^6.18.0"
    debug "^2.6.8"
    globals "^9.18.0"
    invariant "^2.2.2"
    lodash "^4.17.4"

babel-types@^6.0.0, babel-types@^6.18.0, babel-types@^6.24.1, babel-types@^6.26.0:
  version "6.26.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/babel-types/-/babel-types-6.26.0.tgz#a3b073f94ab49eb6fa55cd65227a334380632497"
  integrity sha1-o7Bz+Uq0nrb6Vc1lInozQ4BjJJc=
  dependencies:
    babel-runtime "^6.26.0"
    esutils "^2.0.2"
    lodash "^4.17.4"
    to-fast-properties "^1.0.3"

babylon@^6.18.0:
  version "6.18.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/babylon/-/babylon-6.18.0.tgz#af2f3b88fa6f5c1e4c634d1a0f8eac4f55b395e3"
  integrity sha1-ry87iPpvXB5MY00aD46sT1WzleM=

balanced-match@^1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/balanced-match/-/balanced-match-1.0.0.tgz#89b4d199ab2bee49de164ea02b89ce462d71b767"
  integrity sha1-ibTRmasr7kneFk6gK4nORi1xt2c=

base64-js@^1.0.2:
  version "1.3.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/base64-js/-/base64-js-1.3.0.tgz#cab1e6118f051095e58b5281aea8c1cd22bfc0e3"
  integrity sha1-yrHmEY8FEJXli1KBrqjBzSK/wOM=

base@^0.11.1:
  version "0.11.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/base/-/base-0.11.2.tgz#7bde5ced145b6d551a90db87f83c558b4eb48a8f"
  integrity sha1-e95c7RRbbVUakNuH+DxVi060io8=
  dependencies:
    cache-base "^1.0.1"
    class-utils "^0.3.5"
    component-emitter "^1.2.1"
    define-property "^1.0.0"
    isobject "^3.0.1"
    mixin-deep "^1.2.0"
    pascalcase "^0.1.1"

batch@0.6.1:
  version "0.6.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/batch/-/batch-0.6.1.tgz#dc34314f4e679318093fc760272525f94bf25c16"
  integrity sha1-3DQxT05nkxgJP8dgJyUl+UvyXBY=

bcrypt-pbkdf@^1.0.0:
  version "1.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/bcrypt-pbkdf/-/bcrypt-pbkdf-1.0.2.tgz#a4301d389b6a43f9b67ff3ca11a3f6637e360e9e"
  integrity sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4=
  dependencies:
    tweetnacl "^0.14.3"

bfj@^6.1.1:
  version "6.1.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/bfj/-/bfj-6.1.2.tgz#325c861a822bcb358a41c78a33b8e6e2086dde7f"
  integrity sha1-MlyGGoIryzWKQceKM7jm4ght3n8=
  dependencies:
    bluebird "^3.5.5"
    check-types "^8.0.3"
    hoopy "^0.1.4"
    tryer "^1.0.1"

big.js@^3.1.3:
  version "3.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/big.js/-/big.js-3.2.0.tgz#a5fc298b81b9e0dca2e458824784b65c52ba588e"
  integrity sha1-pfwpi4G54Nyi5FiCR4S2XFK6WI4=

big.js@^5.2.2:
  version "5.2.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/big.js/-/big.js-5.2.2.tgz#65f0af382f578bcdc742bd9c281e9cb2d7768328"
  integrity sha1-ZfCvOC9Xi83HQr2cKB6cstd2gyg=

binary-extensions@^1.0.0:
  version "1.13.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/binary-extensions/-/binary-extensions-1.13.1.tgz#598afe54755b2868a5330d2aff9d4ebb53209b65"
  integrity sha1-WYr+VHVbKGilMw0q/51Ou1Mgm2U=

block-stream@*:
  version "0.0.9"
  resolved "https://nexus.lanmaoly.com/repository/npm/block-stream/-/block-stream-0.0.9.tgz#13ebfe778a03205cfe03751481ebb4b3300c126a"
  integrity sha1-E+v+d4oDIFz+A3UUgeu0szAMEmo=
  dependencies:
    inherits "~2.0.0"

bluebird@^3.5.1, bluebird@^3.5.5:
  version "3.5.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/bluebird/-/bluebird-3.5.5.tgz#a8d0afd73251effbbd5fe384a77d73003c17a71f"
  integrity sha1-qNCv1zJR7/u9X+OEp31zADwXpx8=

bn.js@^4.0.0, bn.js@^4.1.0, bn.js@^4.1.1, bn.js@^4.4.0:
  version "4.11.8"
  resolved "https://nexus.lanmaoly.com/repository/npm/bn.js/-/bn.js-4.11.8.tgz#2cde09eb5ee341f484746bb0309b3253b1b1442f"
  integrity sha1-LN4J617jQfSEdGuwMJsyU7GxRC8=

body-parser@1.19.0:
  version "1.19.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/body-parser/-/body-parser-1.19.0.tgz#96b2709e57c9c4e09a6fd66a8fd979844f69f08a"
  integrity sha1-lrJwnlfJxOCab9Zqj9l5hE9p8Io=
  dependencies:
    bytes "3.1.0"
    content-type "~1.0.4"
    debug "2.6.9"
    depd "~1.1.2"
    http-errors "1.7.2"
    iconv-lite "0.4.24"
    on-finished "~2.3.0"
    qs "6.7.0"
    raw-body "2.4.0"
    type-is "~1.6.17"

bonjour@^3.5.0:
  version "3.5.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/bonjour/-/bonjour-3.5.0.tgz#8e890a183d8ee9a2393b3844c691a42bcf7bc9f5"
  integrity sha1-jokKGD2O6aI5OzhExpGkK897yfU=
  dependencies:
    array-flatten "^2.1.0"
    deep-equal "^1.0.1"
    dns-equal "^1.0.0"
    dns-txt "^2.0.2"
    multicast-dns "^6.0.1"
    multicast-dns-service-types "^1.1.0"

boolbase@~1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/boolbase/-/boolbase-1.0.0.tgz#68dff5fbe60c51eb37725ea9e3ed310dcc1e776e"
  integrity sha1-aN/1++YMUes3cl6p4+0xDcwed24=

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "https://nexus.lanmaoly.com/repository/npm/brace-expansion/-/brace-expansion-1.1.11.tgz#3c7fcbf529d87226f3d2f52b966ff5271eb441dd"
  integrity sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

braces@^1.8.2:
  version "1.8.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/braces/-/braces-1.8.5.tgz#ba77962e12dff969d6b76711e914b737857bf6a7"
  integrity sha1-uneWLhLf+WnWt2cR6RS3N4V79qc=
  dependencies:
    expand-range "^1.8.1"
    preserve "^0.2.0"
    repeat-element "^1.1.2"

braces@^2.3.1, braces@^2.3.2:
  version "2.3.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/braces/-/braces-2.3.2.tgz#5979fd3f14cd531565e5fa2df1abfff1dfaee729"
  integrity sha1-WXn9PxTNUxVl5fot8av/8d+u5yk=
  dependencies:
    arr-flatten "^1.1.0"
    array-unique "^0.3.2"
    extend-shallow "^2.0.1"
    fill-range "^4.0.0"
    isobject "^3.0.1"
    repeat-element "^1.1.2"
    snapdragon "^0.8.1"
    snapdragon-node "^2.0.1"
    split-string "^3.0.2"
    to-regex "^3.0.1"

brorand@^1.0.1:
  version "1.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/brorand/-/brorand-1.1.0.tgz#12c25efe40a45e3c323eb8675a0a0ce57b22371f"
  integrity sha1-EsJe/kCkXjwyPrhnWgoM5XsiNx8=

browser-process-hrtime@^0.1.2:
  version "0.1.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/browser-process-hrtime/-/browser-process-hrtime-0.1.3.tgz#616f00faef1df7ec1b5bf9cfe2bdc3170f26c7b4"
  integrity sha1-YW8A+u8d9+wbW/nP4r3DFw8mx7Q=

browser-resolve@^1.11.3:
  version "1.11.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/browser-resolve/-/browser-resolve-1.11.3.tgz#9b7cbb3d0f510e4cb86bdbd796124d28b5890af6"
  integrity sha1-m3y7PQ9RDky4a9vXlhJNKLWJCvY=
  dependencies:
    resolve "1.1.7"

browserify-aes@^1.0.0, browserify-aes@^1.0.4:
  version "1.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/browserify-aes/-/browserify-aes-1.2.0.tgz#326734642f403dabc3003209853bb70ad428ef48"
  integrity sha1-Mmc0ZC9APavDADIJhTu3CtQo70g=
  dependencies:
    buffer-xor "^1.0.3"
    cipher-base "^1.0.0"
    create-hash "^1.1.0"
    evp_bytestokey "^1.0.3"
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

browserify-cipher@^1.0.0:
  version "1.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/browserify-cipher/-/browserify-cipher-1.0.1.tgz#8d6474c1b870bfdabcd3bcfcc1934a10e94f15f0"
  integrity sha1-jWR0wbhwv9q807z8wZNKEOlPFfA=
  dependencies:
    browserify-aes "^1.0.4"
    browserify-des "^1.0.0"
    evp_bytestokey "^1.0.0"

browserify-des@^1.0.0:
  version "1.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/browserify-des/-/browserify-des-1.0.2.tgz#3af4f1f59839403572f1c66204375f7a7f703e9c"
  integrity sha1-OvTx9Zg5QDVy8cZiBDdfen9wPpw=
  dependencies:
    cipher-base "^1.0.1"
    des.js "^1.0.0"
    inherits "^2.0.1"
    safe-buffer "^5.1.2"

browserify-rsa@^4.0.0:
  version "4.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/browserify-rsa/-/browserify-rsa-4.0.1.tgz#21e0abfaf6f2029cf2fafb133567a701d4135524"
  integrity sha1-IeCr+vbyApzy+vsTNWenAdQTVSQ=
  dependencies:
    bn.js "^4.1.0"
    randombytes "^2.0.1"

browserify-sign@^4.0.0:
  version "4.0.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/browserify-sign/-/browserify-sign-4.0.4.tgz#aa4eb68e5d7b658baa6bf6a57e630cbd7a93d298"
  integrity sha1-qk62jl17ZYuqa/alfmMMvXqT0pg=
  dependencies:
    bn.js "^4.1.1"
    browserify-rsa "^4.0.0"
    create-hash "^1.1.0"
    create-hmac "^1.1.2"
    elliptic "^6.0.0"
    inherits "^2.0.1"
    parse-asn1 "^5.0.0"

browserify-zlib@^0.2.0:
  version "0.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/browserify-zlib/-/browserify-zlib-0.2.0.tgz#2869459d9aa3be245fe8fe2ca1f46e2e7f54d73f"
  integrity sha1-KGlFnZqjviRf6P4sofRuLn9U1z8=
  dependencies:
    pako "~1.0.5"

browserslist@^4.0.2, browserslist@^4.6.0, browserslist@^4.6.2:
  version "4.6.6"
  resolved "https://nexus.lanmaoly.com/repository/npm/browserslist/-/browserslist-4.6.6.tgz#6e4bf467cde520bc9dbdf3747dafa03531cec453"
  integrity sha1-bkv0Z83lILydvfN0fa+gNTHOxFM=
  dependencies:
    caniuse-lite "^1.0.30000984"
    electron-to-chromium "^1.3.191"
    node-releases "^1.1.25"

bser@^2.0.0:
  version "2.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/bser/-/bser-2.0.0.tgz#9ac78d3ed5d915804fd87acb158bc797147a1719"
  integrity sha1-mseNPtXZFYBP2HrLFYvHlxR6Fxk=
  dependencies:
    node-int64 "^0.4.0"

buffer-from@^1.0.0:
  version "1.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/buffer-from/-/buffer-from-1.1.1.tgz#32713bc028f75c02fdb710d7c7bcec1f2c6070ef"
  integrity sha1-MnE7wCj3XAL9txDXx7zsHyxgcO8=

buffer-indexof@^1.0.0:
  version "1.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/buffer-indexof/-/buffer-indexof-1.1.1.tgz#52fabcc6a606d1a00302802648ef68f639da268c"
  integrity sha1-Uvq8xqYG0aADAoAmSO9o9jnaJow=

buffer-xor@^1.0.3:
  version "1.0.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/buffer-xor/-/buffer-xor-1.0.3.tgz#26e61ed1422fb70dd42e6e36729ed51d855fe8d9"
  integrity sha1-JuYe0UIvtw3ULm42cp7VHYVf6Nk=

buffer@^4.3.0:
  version "4.9.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/buffer/-/buffer-4.9.1.tgz#6d1bb601b07a4efced97094132093027c95bc298"
  integrity sha1-bRu2AbB6TvztlwlBMgkwJ8lbwpg=
  dependencies:
    base64-js "^1.0.2"
    ieee754 "^1.1.4"
    isarray "^1.0.0"

builtin-status-codes@^3.0.0:
  version "3.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/builtin-status-codes/-/builtin-status-codes-3.0.0.tgz#85982878e21b98e1c66425e03d0174788f569ee8"
  integrity sha1-hZgoeOIbmOHGZCXgPQF0eI9Wnug=

bytes@3.0.0:
  version "3.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/bytes/-/bytes-3.0.0.tgz#d32815404d689699f85a4ea4fa8755dd13a96048"
  integrity sha1-0ygVQE1olpn4Wk6k+odV3ROpYEg=

bytes@3.1.0:
  version "3.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/bytes/-/bytes-3.1.0.tgz#f6cf7933a360e0588fa9fde85651cdc7f805d1f6"
  integrity sha1-9s95M6Ng4FiPqf3oVlHNx/gF0fY=

cacache@^10.0.4:
  version "10.0.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/cacache/-/cacache-10.0.4.tgz#6452367999eff9d4188aefd9a14e9d7c6a263460"
  integrity sha1-ZFI2eZnv+dQYiu/ZoU6dfGomNGA=
  dependencies:
    bluebird "^3.5.1"
    chownr "^1.0.1"
    glob "^7.1.2"
    graceful-fs "^4.1.11"
    lru-cache "^4.1.1"
    mississippi "^2.0.0"
    mkdirp "^0.5.1"
    move-concurrently "^1.0.1"
    promise-inflight "^1.0.1"
    rimraf "^2.6.2"
    ssri "^5.2.4"
    unique-filename "^1.1.0"
    y18n "^4.0.0"

cacache@^11.3.2:
  version "11.3.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/cacache/-/cacache-11.3.3.tgz#8bd29df8c6a718a6ebd2d010da4d7972ae3bbadc"
  integrity sha1-i9Kd+ManGKbr0tAQ2k15cq47utw=
  dependencies:
    bluebird "^3.5.5"
    chownr "^1.1.1"
    figgy-pudding "^3.5.1"
    glob "^7.1.4"
    graceful-fs "^4.1.15"
    lru-cache "^5.1.1"
    mississippi "^3.0.0"
    mkdirp "^0.5.1"
    move-concurrently "^1.0.1"
    promise-inflight "^1.0.1"
    rimraf "^2.6.3"
    ssri "^6.0.1"
    unique-filename "^1.1.1"
    y18n "^4.0.0"

cacache@^12.0.2:
  version "12.0.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/cacache/-/cacache-12.0.3.tgz#be99abba4e1bf5df461cd5a2c1071fc432573390"
  integrity sha1-vpmruk4b9d9GHNWiwQcfxDJXM5A=
  dependencies:
    bluebird "^3.5.5"
    chownr "^1.1.1"
    figgy-pudding "^3.5.1"
    glob "^7.1.4"
    graceful-fs "^4.1.15"
    infer-owner "^1.0.3"
    lru-cache "^5.1.1"
    mississippi "^3.0.0"
    mkdirp "^0.5.1"
    move-concurrently "^1.0.1"
    promise-inflight "^1.0.1"
    rimraf "^2.6.3"
    ssri "^6.0.1"
    unique-filename "^1.1.1"
    y18n "^4.0.0"

cache-base@^1.0.1:
  version "1.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/cache-base/-/cache-base-1.0.1.tgz#0a7f46416831c8b662ee36fe4e7c59d76f666ab2"
  integrity sha1-Cn9GQWgxyLZi7jb+TnxZ129marI=
  dependencies:
    collection-visit "^1.0.0"
    component-emitter "^1.2.1"
    get-value "^2.0.6"
    has-value "^1.0.0"
    isobject "^3.0.1"
    set-value "^2.0.0"
    to-object-path "^0.3.0"
    union-value "^1.0.0"
    unset-value "^1.0.0"

caller-callsite@^2.0.0:
  version "2.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/caller-callsite/-/caller-callsite-2.0.0.tgz#847e0fce0a223750a9a027c54b33731ad3154134"
  integrity sha1-hH4PzgoiN1CpoCfFSzNzGtMVQTQ=
  dependencies:
    callsites "^2.0.0"

caller-path@^2.0.0:
  version "2.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/caller-path/-/caller-path-2.0.0.tgz#468f83044e369ab2010fac5f06ceee15bb2cb1f4"
  integrity sha1-Ro+DBE42mrIBD6xfBs7uFbsssfQ=
  dependencies:
    caller-callsite "^2.0.0"

callsites@^2.0.0:
  version "2.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/callsites/-/callsites-2.0.0.tgz#06eb84f00eea413da86affefacbffb36093b3c50"
  integrity sha1-BuuE8A7qQT2oav/vrL/7Ngk7PFA=

callsites@^3.0.0:
  version "3.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/callsites/-/callsites-3.1.0.tgz#b3630abd8943432f54b3f0519238e33cd7df2f73"
  integrity sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=

camel-case@3.0.x:
  version "3.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/camel-case/-/camel-case-3.0.0.tgz#ca3c3688a4e9cf3a4cda777dc4dcbc713249cf73"
  integrity sha1-yjw2iKTpzzpM2nd9xNy8cTJJz3M=
  dependencies:
    no-case "^2.2.0"
    upper-case "^1.1.1"

camelcase-keys@^2.0.0:
  version "2.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/camelcase-keys/-/camelcase-keys-2.1.0.tgz#308beeaffdf28119051efa1d932213c91b8f92e7"
  integrity sha1-MIvur/3ygRkFHvodkyITyRuPkuc=
  dependencies:
    camelcase "^2.0.0"
    map-obj "^1.0.0"

camelcase@^2.0.0:
  version "2.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/camelcase/-/camelcase-2.1.1.tgz#7c1d16d679a1bbe59ca02cacecfb011e201f5a1f"
  integrity sha1-fB0W1nmhu+WcoCys7PsBHiAfWh8=

camelcase@^4.1.0:
  version "4.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/camelcase/-/camelcase-4.1.0.tgz#d545635be1e33c542649c69173e5de6acfae34dd"
  integrity sha1-1UVjW+HjPFQmScaRc+Xeas+uNN0=

camelcase@^5.0.0:
  version "5.3.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/camelcase/-/camelcase-5.3.1.tgz#e3c9b31569e106811df242f715725a1f4c494320"
  integrity sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA=

caniuse-lite@^1.0.30000878:
  version "1.0.30000989"
  resolved "https://nexus.lanmaoly.com/repository/npm/caniuse-lite/-/caniuse-lite-1.0.30000989.tgz#b9193e293ccf7e4426c5245134b8f2a56c0ac4b9"
  integrity sha1-uRk+KTzPfkQmxSRRNLjypWwKxLk=

caniuse-lite@^1.0.30000984:
  version "1.0.30000985"
  resolved "https://nexus.lanmaoly.com/repository/npm/caniuse-lite/-/caniuse-lite-1.0.30000985.tgz#0eb40f6c8a8c219155cbe43c4975c0efb4a0f77f"
  integrity sha1-DrQPbIqMIZFVy+Q8SXXA77Sg938=

capture-exit@^1.2.0:
  version "1.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/capture-exit/-/capture-exit-1.2.0.tgz#1c5fcc489fd0ab00d4f1ac7ae1072e3173fbab6f"
  integrity sha1-HF/MSJ/QqwDU8ax64QcuMXP7q28=
  dependencies:
    rsvp "^3.3.3"

caseless@~0.12.0:
  version "0.12.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/caseless/-/caseless-0.12.0.tgz#1b681c21ff84033c826543090689420d187151dc"
  integrity sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw=

chalk@2.4.2, chalk@^2.0.0, chalk@^2.0.1, chalk@^2.1.0, chalk@^2.3.0, chalk@^2.4.1, chalk@^2.4.2:
  version "2.4.2"
  resolved "https://r.cnpmjs.org/chalk/download/chalk-2.4.2.tgz#cd42541677a54333cf541a49108c1432b44c9424"
  integrity sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@^1.1.1, chalk@^1.1.3:
  version "1.1.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/chalk/-/chalk-1.1.3.tgz#a8115c55e4a702fe4d150abd3872822a7e09fc98"
  integrity sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg=
  dependencies:
    ansi-styles "^2.2.1"
    escape-string-regexp "^1.0.2"
    has-ansi "^2.0.0"
    strip-ansi "^3.0.0"
    supports-color "^2.0.0"

chardet@^0.7.0:
  version "0.7.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/chardet/-/chardet-0.7.0.tgz#90094849f0937f2eedc2425d0d28a9e5f0cbad9e"
  integrity sha1-kAlISfCTfy7twkJdDSip5fDLrZ4=

check-types@^8.0.3:
  version "8.0.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/check-types/-/check-types-8.0.3.tgz#3356cca19c889544f2d7a95ed49ce508a0ecf552"
  integrity sha1-M1bMoZyIlUTy16le1JzlCKDs9VI=

chokidar@^2.0.2, chokidar@^2.1.6:
  version "2.1.6"
  resolved "https://nexus.lanmaoly.com/repository/npm/chokidar/-/chokidar-2.1.6.tgz#b6cad653a929e244ce8a834244164d241fa954c5"
  integrity sha1-tsrWU6kp4kTOioNCRBZNJB+pVMU=
  dependencies:
    anymatch "^2.0.0"
    async-each "^1.0.1"
    braces "^2.3.2"
    glob-parent "^3.1.0"
    inherits "^2.0.3"
    is-binary-path "^1.0.0"
    is-glob "^4.0.0"
    normalize-path "^3.0.0"
    path-is-absolute "^1.0.0"
    readdirp "^2.2.1"
    upath "^1.1.1"
  optionalDependencies:
    fsevents "^1.2.7"

chownr@^1.0.1, chownr@^1.1.1:
  version "1.1.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/chownr/-/chownr-1.1.2.tgz#a18f1e0b269c8a6a5d3c86eb298beb14c3dd7bf6"
  integrity sha1-oY8eCyacimpdPIbrKYvrFMPde/Y=

chrome-trace-event@^1.0.0:
  version "1.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/chrome-trace-event/-/chrome-trace-event-1.0.2.tgz#234090ee97c7d4ad1a2c4beae27505deffc608a4"
  integrity sha1-I0CQ7pfH1K0aLEvq4nUF3v/GCKQ=
  dependencies:
    tslib "^1.9.0"

ci-info@^1.5.0:
  version "1.6.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/ci-info/-/ci-info-1.6.0.tgz#2ca20dbb9ceb32d4524a683303313f0304b1e497"
  integrity sha1-LKINu5zrMtRSSmgzAzE/AwSx5Jc=

cipher-base@^1.0.0, cipher-base@^1.0.1, cipher-base@^1.0.3:
  version "1.0.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/cipher-base/-/cipher-base-1.0.4.tgz#8760e4ecc272f4c363532f926d874aae2c1397de"
  integrity sha1-h2Dk7MJy9MNjUy+SbYdKriwTl94=
  dependencies:
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

class-utils@^0.3.5:
  version "0.3.6"
  resolved "https://nexus.lanmaoly.com/repository/npm/class-utils/-/class-utils-0.3.6.tgz#f93369ae8b9a7ce02fd41faad0ca83033190c463"
  integrity sha1-+TNprouafOAv1B+q0MqDAzGQxGM=
  dependencies:
    arr-union "^3.1.0"
    define-property "^0.2.5"
    isobject "^3.0.0"
    static-extend "^0.1.1"

classnames@2.x, classnames@^2.2.0, classnames@^2.2.1, classnames@^2.2.3, classnames@^2.2.4, classnames@^2.2.5, classnames@^2.2.6:
  version "2.2.6"
  resolved "https://nexus.lanmaoly.com/repository/npm/classnames/-/classnames-2.2.6.tgz#43935bffdd291f326dad0a205309b38d00f650ce"
  integrity sha1-Q5Nb/90pHzJtrQogUwmzjQD2UM4=

clean-css@4.2.x:
  version "4.2.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/clean-css/-/clean-css-4.2.1.tgz#2d411ef76b8569b6d0c84068dabe85b0aa5e5c17"
  integrity sha1-LUEe92uFabbQyEBo2r6FsKpeXBc=
  dependencies:
    source-map "~0.6.0"

cli-cursor@^2.1.0:
  version "2.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/cli-cursor/-/cli-cursor-2.1.0.tgz#b35dac376479facc3e94747d41d0d0f5238ffcb5"
  integrity sha1-s12sN2R5+sw+lHR9QdDQ9SOP/LU=
  dependencies:
    restore-cursor "^2.0.0"

cli-width@^2.0.0:
  version "2.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/cli-width/-/cli-width-2.2.0.tgz#ff19ede8a9a5e579324147b0c11f0fbcbabed639"
  integrity sha1-/xnt6Kml5XkyQUewwR8PvLq+1jk=

cliui@^4.0.0:
  version "4.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/cliui/-/cliui-4.1.0.tgz#348422dbe82d800b3022eef4f6ac10bf2e4d1b49"
  integrity sha1-NIQi2+gtgAswIu709qwQvy5NG0k=
  dependencies:
    string-width "^2.1.1"
    strip-ansi "^4.0.0"
    wrap-ansi "^2.0.0"

cliui@^5.0.0:
  version "5.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/cliui/-/cliui-5.0.0.tgz#deefcfdb2e800784aa34f46fa08e06851c7bbbc5"
  integrity sha1-3u/P2y6AB4SqNPRvoI4GhRx7u8U=
  dependencies:
    string-width "^3.1.0"
    strip-ansi "^5.2.0"
    wrap-ansi "^5.1.0"

clone-deep@^2.0.1:
  version "2.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/clone-deep/-/clone-deep-2.0.2.tgz#00db3a1e173656730d1188c3d6aced6d7ea97713"
  integrity sha1-ANs6Hhc2VnMNEYjD1qztbX6pdxM=
  dependencies:
    for-own "^1.0.0"
    is-plain-object "^2.0.4"
    kind-of "^6.0.0"
    shallow-clone "^1.0.0"

clone@^2.1.1, clone@^2.1.2:
  version "2.1.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/clone/-/clone-2.1.2.tgz#1b7f4b9f591f1e8f83670401600345a02887435f"
  integrity sha1-G39Ln1kfHo+DZwQBYANFoCiHQ18=

co@^4.6.0:
  version "4.6.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/co/-/co-4.6.0.tgz#6ea6bdf3d853ae54ccb8e47bfa0bf3f9031fb184"
  integrity sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ=

code-point-at@^1.0.0:
  version "1.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/code-point-at/-/code-point-at-1.1.0.tgz#0d070b4d043a5bea33a2f1a40e2edb3d9a4ccf77"
  integrity sha1-DQcLTQQ6W+ozovGkDi7bPZpMz3c=

collection-visit@^1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/collection-visit/-/collection-visit-1.0.0.tgz#4bc0373c164bc3291b4d368c829cf1a80a59dca0"
  integrity sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA=
  dependencies:
    map-visit "^1.0.0"
    object-visit "^1.0.0"

color-convert@^1.9.0:
  version "1.9.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/color-convert/-/color-convert-1.9.3.tgz#bb71850690e1f136567de629d2d5471deda4c1e8"
  integrity sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=
  dependencies:
    color-name "1.1.3"

color-name@1.1.3:
  version "1.1.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/color-name/-/color-name-1.1.3.tgz#a7d0558bd89c42f795dd42328f740831ca53bc25"
  integrity sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=

colour@0.7.1:
  version "0.7.1"
  resolved "https://r.cnpmjs.org/colour/download/colour-0.7.1.tgz#9cb169917ec5d12c0736d3e8685746df1cadf778"
  integrity sha1-nLFpkX7F0SwHNtPoaFdG3xyt93g=

combined-stream@^1.0.6, combined-stream@~1.0.6:
  version "1.0.8"
  resolved "https://nexus.lanmaoly.com/repository/npm/combined-stream/-/combined-stream-1.0.8.tgz#c3d45a8b34fd730631a110a8a2520682b31d5a7f"
  integrity sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=
  dependencies:
    delayed-stream "~1.0.0"

commander@2.17.x:
  version "2.17.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/commander/-/commander-2.17.1.tgz#bd77ab7de6de94205ceacc72f1716d29f20a77bf"
  integrity sha1-vXerfebelCBc6sxy8XFtKfIKd78=

commander@^2.11.0, commander@^2.18.0, commander@^2.20.0, commander@~2.20.0:
  version "2.20.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/commander/-/commander-2.20.0.tgz#d58bb2b5c1ee8f87b0d340027e9e94e222c5a422"
  integrity sha1-1YuytcHuj4ew00ACfp6U4iLFpCI=

commander@~2.19.0:
  version "2.19.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/commander/-/commander-2.19.0.tgz#f6198aa84e5b83c46054b94ddedbfed5ee9ff12a"
  integrity sha1-9hmKqE5bg8RgVLlN3tv+1e6f8So=

commondir@^1.0.1:
  version "1.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/commondir/-/commondir-1.0.1.tgz#ddd800da0c66127393cca5950ea968a3aaf1253b"
  integrity sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs=

component-classes@^1.2.5:
  version "1.2.6"
  resolved "https://nexus.lanmaoly.com/repository/npm/component-classes/-/component-classes-1.2.6.tgz#c642394c3618a4d8b0b8919efccbbd930e5cd691"
  integrity sha1-xkI5TDYYpNiwuJGe/Mu9kw5c1pE=
  dependencies:
    component-indexof "0.0.3"

component-emitter@^1.2.1:
  version "1.3.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/component-emitter/-/component-emitter-1.3.0.tgz#16e4070fba8ae29b679f2215853ee181ab2eabc0"
  integrity sha1-FuQHD7qK4ptnnyIVhT7hgasuq8A=

component-indexof@0.0.3:
  version "0.0.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/component-indexof/-/component-indexof-0.0.3.tgz#11d091312239eb8f32c8f25ae9cb002ffe8d3c24"
  integrity sha1-EdCRMSI5648yyPJa6csAL/6NPCQ=

compressible@~2.0.16:
  version "2.0.17"
  resolved "https://nexus.lanmaoly.com/repository/npm/compressible/-/compressible-2.0.17.tgz#6e8c108a16ad58384a977f3a482ca20bff2f38c1"
  integrity sha1-bowQihatWDhKl386SCyiC/8vOME=
  dependencies:
    mime-db ">= 1.40.0 < 2"

compression@^1.7.4:
  version "1.7.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/compression/-/compression-1.7.4.tgz#95523eff170ca57c29a0ca41e6fe131f41e5bb8f"
  integrity sha1-lVI+/xcMpXwpoMpB5v4TH0Hlu48=
  dependencies:
    accepts "~1.3.5"
    bytes "3.0.0"
    compressible "~2.0.16"
    debug "2.6.9"
    on-headers "~1.0.2"
    safe-buffer "5.1.2"
    vary "~1.1.2"

concat-map@0.0.1:
  version "0.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/concat-map/-/concat-map-0.0.1.tgz#d8a96bd77fd68df7793a73036a3ba0d5405d477b"
  integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=

concat-stream@^1.5.0:
  version "1.6.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/concat-stream/-/concat-stream-1.6.2.tgz#904bdf194cd3122fc675c77fc4ac3d4ff0fd1a34"
  integrity sha1-kEvfGUzTEi/Gdcd/xKw9T/D9GjQ=
  dependencies:
    buffer-from "^1.0.0"
    inherits "^2.0.3"
    readable-stream "^2.2.2"
    typedarray "^0.0.6"

confusing-browser-globals@^1.0.5:
  version "1.0.6"
  resolved "https://nexus.lanmaoly.com/repository/npm/confusing-browser-globals/-/confusing-browser-globals-1.0.6.tgz#5918188e8244492cdd46d6be1cab60edef3063ce"
  integrity sha1-WRgYjoJESSzdRta+HKtg7e8wY84=

connect-history-api-fallback@^1.6.0:
  version "1.6.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/connect-history-api-fallback/-/connect-history-api-fallback-1.6.0.tgz#8b32089359308d111115d81cad3fceab888f97bc"
  integrity sha1-izIIk1kwjRERFdgcrT/Oq4iPl7w=

connected-react-router@^4.4.1:
  version "4.5.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/connected-react-router/-/connected-react-router-4.5.0.tgz#b6f021cc284a244fbee70e16e5ff0f2a4613e3d3"
  integrity sha1-tvAhzChKJE++5w4W5f8PKkYT49M=
  dependencies:
    immutable "^3.8.1"
    redux-seamless-immutable "^0.4.0"
    seamless-immutable "^7.1.3"

console-browserify@^1.1.0:
  version "1.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/console-browserify/-/console-browserify-1.1.0.tgz#f0241c45730a9fc6323b206dbf38edc741d0bb10"
  integrity sha1-8CQcRXMKn8YyOyBtvzjtx0HQuxA=
  dependencies:
    date-now "^0.1.4"

console-control-strings@^1.0.0, console-control-strings@~1.1.0:
  version "1.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/console-control-strings/-/console-control-strings-1.1.0.tgz#3d7cf4464db6446ea644bf4b39507f9851008e8e"
  integrity sha1-PXz0Rk22RG6mRL9LOVB/mFEAjo4=

constants-browserify@^1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/constants-browserify/-/constants-browserify-1.0.0.tgz#c20b96d8c617748aaf1c16021760cd27fcb8cb75"
  integrity sha1-wguW2MYXdIqvHBYCF2DNJ/y4y3U=

contains-path@^0.1.0:
  version "0.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/contains-path/-/contains-path-0.1.0.tgz#fe8cf184ff6670b6baef01a9d4861a5cbec4120a"
  integrity sha1-/ozxhP9mcLa67wGp1IYaXL7EEgo=

content-disposition@0.5.3:
  version "0.5.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/content-disposition/-/content-disposition-0.5.3.tgz#e130caf7e7279087c5616c2007d0485698984fbd"
  integrity sha1-4TDK9+cnkIfFYWwgB9BIVpiYT70=
  dependencies:
    safe-buffer "5.1.2"

content-type@~1.0.4:
  version "1.0.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/content-type/-/content-type-1.0.4.tgz#e138cc75e040c727b1966fe5e5f8c9aee256fe3b"
  integrity sha1-4TjMdeBAxyexlm/l5fjJruJW/js=

convert-source-map@^1.1.0, convert-source-map@^1.4.0, convert-source-map@^1.5.1:
  version "1.6.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/convert-source-map/-/convert-source-map-1.6.0.tgz#51b537a8c43e0f04dec1993bffcdd504e758ac20"
  integrity sha1-UbU3qMQ+DwTewZk7/83VBOdYrCA=
  dependencies:
    safe-buffer "~5.1.1"

cookie-signature@1.0.6:
  version "1.0.6"
  resolved "https://nexus.lanmaoly.com/repository/npm/cookie-signature/-/cookie-signature-1.0.6.tgz#e303a882b342cc3ee8ca513a79999734dab3ae2c"
  integrity sha1-4wOogrNCzD7oylE6eZmXNNqzriw=

cookie@0.4.0:
  version "0.4.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/cookie/-/cookie-0.4.0.tgz#beb437e7022b3b6d49019d088665303ebe9c14ba"
  integrity sha1-vrQ35wIrO21JAZ0IhmUwPr6cFLo=

copy-anything@^2.0.1:
  version "2.0.3"
  resolved "https://r.cnpmjs.org/copy-anything/download/copy-anything-2.0.3.tgz#842407ba02466b0df844819bbe3baebbe5d45d87"
  integrity sha1-hCQHugJGaw34RIGbvjuuu+XUXYc=
  dependencies:
    is-what "^3.12.0"

copy-concurrently@^1.0.0:
  version "1.0.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/copy-concurrently/-/copy-concurrently-1.0.5.tgz#92297398cae34937fcafd6ec8139c18051f0b5e0"
  integrity sha1-kilzmMrjSTf8r9bsgTnBgFHwteA=
  dependencies:
    aproba "^1.1.1"
    fs-write-stream-atomic "^1.0.8"
    iferr "^0.1.5"
    mkdirp "^0.5.1"
    rimraf "^2.5.4"
    run-queue "^1.0.0"

copy-descriptor@^0.1.0:
  version "0.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/copy-descriptor/-/copy-descriptor-0.1.1.tgz#676f6eb3c39997c2ee1ac3a924fd6124748f578d"
  integrity sha1-Z29us8OZl8LuGsOpJP1hJHSPV40=

copy-to-clipboard@^3.3.1:
  version "3.3.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/copy-to-clipboard/-/copy-to-clipboard-3.3.1.tgz#115aa1a9998ffab6196f93076ad6da3b913662ae"
  integrity sha1-EVqhqZmP+rYZb5MHatbaO5E2Yq4=
  dependencies:
    toggle-selection "^1.0.6"

copy-webpack-plugin@^4.5.2:
  version "4.6.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/copy-webpack-plugin/-/copy-webpack-plugin-4.6.0.tgz#e7f40dd8a68477d405dd1b7a854aae324b158bae"
  integrity sha1-5/QN2KaEd9QF3Rt6hUquMksVi64=
  dependencies:
    cacache "^10.0.4"
    find-cache-dir "^1.0.0"
    globby "^7.1.1"
    is-glob "^4.0.0"
    loader-utils "^1.1.0"
    minimatch "^3.0.4"
    p-limit "^1.0.0"
    serialize-javascript "^1.4.0"

core-js-compat@^3.1.1:
  version "3.1.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/core-js-compat/-/core-js-compat-3.1.4.tgz#e4d0c40fbd01e65b1d457980fe4112d4358a7408"
  integrity sha1-5NDED70B5lsdRXmA/kES1DWKdAg=
  dependencies:
    browserslist "^4.6.2"
    core-js-pure "3.1.4"
    semver "^6.1.1"

core-js-pure@3.1.4:
  version "3.1.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/core-js-pure/-/core-js-pure-3.1.4.tgz#5fa17dc77002a169a3566cc48dc774d2e13e3769"
  integrity sha1-X6F9x3ACoWmjVmzEjcd00uE+N2k=

core-js@^1.0.0:
  version "1.2.7"
  resolved "https://nexus.lanmaoly.com/repository/npm/core-js/-/core-js-1.2.7.tgz#652294c14651db28fa93bd2d5ff2983a4f08c636"
  integrity sha1-ZSKUwUZR2yj6k70tX/KYOk8IxjY=

core-js@^2.4.0, core-js@^2.5.0:
  version "2.6.9"
  resolved "https://nexus.lanmaoly.com/repository/npm/core-js/-/core-js-2.6.9.tgz#6b4b214620c834152e179323727fc19741b084f2"
  integrity sha1-a0shRiDINBUuF5Mjcn/Bl0GwhPI=

core-util-is@1.0.2, core-util-is@~1.0.0:
  version "1.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/core-util-is/-/core-util-is-1.0.2.tgz#b5fd54220aa2bc5ab57aab7140c940754503c1a7"
  integrity sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=

cosmiconfig@^5.0.0:
  version "5.2.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/cosmiconfig/-/cosmiconfig-5.2.1.tgz#040f726809c591e77a17c0a3626ca45b4f168b1a"
  integrity sha1-BA9yaAnFked6F8CjYmykW08Wixo=
  dependencies:
    import-fresh "^2.0.0"
    is-directory "^0.3.1"
    js-yaml "^3.13.1"
    parse-json "^4.0.0"

create-ecdh@^4.0.0:
  version "4.0.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/create-ecdh/-/create-ecdh-4.0.3.tgz#c9111b6f33045c4697f144787f9254cdc77c45ff"
  integrity sha1-yREbbzMEXEaX8UR4f5JUzcd8Rf8=
  dependencies:
    bn.js "^4.1.0"
    elliptic "^6.0.0"

create-hash@^1.1.0, create-hash@^1.1.2:
  version "1.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/create-hash/-/create-hash-1.2.0.tgz#889078af11a63756bcfb59bd221996be3a9ef196"
  integrity sha1-iJB4rxGmN1a8+1m9IhmWvjqe8ZY=
  dependencies:
    cipher-base "^1.0.1"
    inherits "^2.0.1"
    md5.js "^1.3.4"
    ripemd160 "^2.0.1"
    sha.js "^2.4.0"

create-hmac@^1.1.0, create-hmac@^1.1.2, create-hmac@^1.1.4:
  version "1.1.7"
  resolved "https://nexus.lanmaoly.com/repository/npm/create-hmac/-/create-hmac-1.1.7.tgz#69170c78b3ab957147b2b8b04572e47ead2243ff"
  integrity sha1-aRcMeLOrlXFHsriwRXLkfq0iQ/8=
  dependencies:
    cipher-base "^1.0.3"
    create-hash "^1.1.0"
    inherits "^2.0.1"
    ripemd160 "^2.0.0"
    safe-buffer "^5.0.1"
    sha.js "^2.4.8"

create-react-class@^15.5.3:
  version "15.7.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/create-react-class/-/create-react-class-15.7.0.tgz#7499d7ca2e69bb51d13faf59bd04f0c65a1d6c1e"
  integrity sha1-dJnXyi5pu1HRP69ZvQTwxlodbB4=
  dependencies:
    loose-envify "^1.3.1"
    object-assign "^4.1.1"

create-react-class@^15.6.0:
  version "15.6.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/create-react-class/-/create-react-class-15.6.3.tgz#2d73237fb3f970ae6ebe011a9e66f46dbca80036"
  integrity sha1-LXMjf7P5cK5uvgEanmb0bbyoADY=
  dependencies:
    fbjs "^0.8.9"
    loose-envify "^1.3.1"
    object-assign "^4.1.1"

cross-env@^5.2.0:
  version "5.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/cross-env/-/cross-env-5.2.0.tgz#6ecd4c015d5773e614039ee529076669b9d126f2"
  integrity sha1-bs1MAV1Xc+YUA57lKQdmabnRJvI=
  dependencies:
    cross-spawn "^6.0.5"
    is-windows "^1.0.0"

cross-spawn@6.0.5, cross-spawn@^6.0.0, cross-spawn@^6.0.5:
  version "6.0.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/cross-spawn/-/cross-spawn-6.0.5.tgz#4a5ec7c64dfae22c3a14124dbacdee846d80cbc4"
  integrity sha1-Sl7Hxk364iw6FBJNus3uhG2Ay8Q=
  dependencies:
    nice-try "^1.0.4"
    path-key "^2.0.1"
    semver "^5.5.0"
    shebang-command "^1.2.0"
    which "^1.2.9"

cross-spawn@^3.0.0:
  version "3.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/cross-spawn/-/cross-spawn-3.0.1.tgz#1256037ecb9f0c5f79e3d6ef135e30770184b982"
  integrity sha1-ElYDfsufDF9549bvE14wdwGEuYI=
  dependencies:
    lru-cache "^4.0.1"
    which "^1.2.9"

cross-spawn@^5.0.1:
  version "5.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/cross-spawn/-/cross-spawn-5.1.0.tgz#e8bd0efee58fcff6f8f94510a0a554bbfa235449"
  integrity sha1-6L0O/uWPz/b4+UUQoKVUu/ojVEk=
  dependencies:
    lru-cache "^4.0.1"
    shebang-command "^1.2.0"
    which "^1.2.9"

crypto-browserify@^3.11.0:
  version "3.12.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/crypto-browserify/-/crypto-browserify-3.12.0.tgz#396cf9f3137f03e4b8e532c58f698254e00f80ec"
  integrity sha1-OWz58xN/A+S45TLFj2mCVOAPgOw=
  dependencies:
    browserify-cipher "^1.0.0"
    browserify-sign "^4.0.0"
    create-ecdh "^4.0.0"
    create-hash "^1.1.0"
    create-hmac "^1.1.0"
    diffie-hellman "^5.0.0"
    inherits "^2.0.1"
    pbkdf2 "^3.0.3"
    public-encrypt "^4.0.0"
    randombytes "^2.0.0"
    randomfill "^1.0.3"

css-animation@1.x, css-animation@^1.3.2:
  version "1.5.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/css-animation/-/css-animation-1.5.0.tgz#c96b9097a5ef74a7be8480b45cc44e4ec6ca2bf5"
  integrity sha1-yWuQl6XvdKe+hIC0XMROTsbKK/U=
  dependencies:
    babel-runtime "6.x"
    component-classes "^1.2.5"

css-loader@^1.0.0:
  version "1.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/css-loader/-/css-loader-1.0.1.tgz#6885bb5233b35ec47b006057da01cc640b6b79fe"
  integrity sha1-aIW7UjOzXsR7AGBX2gHMZAtref4=
  dependencies:
    babel-code-frame "^6.26.0"
    css-selector-tokenizer "^0.7.0"
    icss-utils "^2.1.0"
    loader-utils "^1.0.2"
    lodash "^4.17.11"
    postcss "^6.0.23"
    postcss-modules-extract-imports "^1.2.0"
    postcss-modules-local-by-default "^1.2.0"
    postcss-modules-scope "^1.1.0"
    postcss-modules-values "^1.3.0"
    postcss-value-parser "^3.3.0"
    source-list-map "^2.0.0"

css-select@^1.1.0:
  version "1.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/css-select/-/css-select-1.2.0.tgz#2b3a110539c5355f1cd8d314623e870b121ec858"
  integrity sha1-KzoRBTnFNV8c2NMUYj6HCxIeyFg=
  dependencies:
    boolbase "~1.0.0"
    css-what "2.1"
    domutils "1.5.1"
    nth-check "~1.0.1"

css-selector-tokenizer@^0.7.0:
  version "0.7.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/css-selector-tokenizer/-/css-selector-tokenizer-0.7.1.tgz#a177271a8bca5019172f4f891fc6eed9cbf68d5d"
  integrity sha1-oXcnGovKUBkXL0+JH8bu2cv2jV0=
  dependencies:
    cssesc "^0.1.0"
    fastparse "^1.1.1"
    regexpu-core "^1.0.0"

css-what@2.1:
  version "2.1.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/css-what/-/css-what-2.1.3.tgz#a6d7604573365fe74686c3f311c56513d88285f2"
  integrity sha1-ptdgRXM2X+dGhsPzEcVlE9iChfI=

cssesc@^0.1.0:
  version "0.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/cssesc/-/cssesc-0.1.0.tgz#c814903e45623371a0477b40109aaafbeeaddbb4"
  integrity sha1-yBSQPkViM3GgR3tAEJqq++6t27Q=

cssom@0.3.x, "cssom@>= 0.3.2 < 0.4.0":
  version "0.3.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/cssom/-/cssom-0.3.4.tgz#8cd52e8a3acfd68d3aed38ee0a640177d2f9d797"
  integrity sha1-jNUuijrP1o067TjuCmQBd9L515c=

cssstyle@^1.0.0:
  version "1.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/cssstyle/-/cssstyle-1.1.1.tgz#18b038a9c44d65f7a8e428a653b9f6fe42faf5fb"
  integrity sha1-GLA4qcRNZfeo5CimU7n2/kL69fs=
  dependencies:
    cssom "0.3.x"

csstype@^3.0.2:
  version "3.0.8"
  resolved "https://r.cnpmjs.org/csstype/download/csstype-3.0.8.tgz#d2266a792729fb227cd216fb572f43728e1ad340"
  integrity sha1-0iZqeScp+yJ80hb7Vy9Dco4a00A=

currently-unhandled@^0.4.1:
  version "0.4.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/currently-unhandled/-/currently-unhandled-0.4.1.tgz#988df33feab191ef799a61369dd76c17adf957ea"
  integrity sha1-mI3zP+qxke95mmE2nddsF635V+o=
  dependencies:
    array-find-index "^1.0.1"

cyclist@~0.2.2:
  version "0.2.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/cyclist/-/cyclist-0.2.2.tgz#1b33792e11e914a2fd6d6ed6447464444e5fa640"
  integrity sha1-GzN5LhHpFKL9bW7WRHRkRE5fpkA=

damerau-levenshtein@^1.0.4:
  version "1.0.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/damerau-levenshtein/-/damerau-levenshtein-1.0.4.tgz#03191c432cb6eea168bb77f3a55ffdccb8978514"
  integrity sha1-AxkcQyy27qFou3fzpV/9zLiXhRQ=

dashdash@^1.12.0:
  version "1.14.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/dashdash/-/dashdash-1.14.1.tgz#853cfa0f7cbe2fed5de20326b8dd581035f6e2f0"
  integrity sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA=
  dependencies:
    assert-plus "^1.0.0"

data-urls@^1.0.0:
  version "1.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/data-urls/-/data-urls-1.1.0.tgz#15ee0582baa5e22bb59c77140da8f9c76963bbfe"
  integrity sha1-Fe4Fgrql4iu1nHcUDaj5x2lju/4=
  dependencies:
    abab "^2.0.0"
    whatwg-mimetype "^2.2.0"
    whatwg-url "^7.0.0"

date-now@^0.1.4:
  version "0.1.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/date-now/-/date-now-0.1.4.tgz#eaf439fd4d4848ad74e5cc7dbef200672b9e345b"
  integrity sha1-6vQ5/U1ISK105cx9vvIAZyueNFs=

debug@2.6.9, debug@^2.2.0, debug@^2.3.3, debug@^2.6.8, debug@^2.6.9:
  version "2.6.9"
  resolved "https://nexus.lanmaoly.com/repository/npm/debug/-/debug-2.6.9.tgz#5d128515df134ff327e90a4c93f4e077a536341f"
  integrity sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=
  dependencies:
    ms "2.0.0"

debug@=3.1.0:
  version "3.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/debug/-/debug-3.1.0.tgz#5bb5a0672628b64149566ba16819e61518c67261"
  integrity sha1-W7WgZyYotkFJVmuhaBnmFRjGcmE=
  dependencies:
    ms "2.0.0"

debug@^3.0.0, debug@^3.1.0, debug@^3.2.5, debug@^3.2.6:
  version "3.2.6"
  resolved "https://nexus.lanmaoly.com/repository/npm/debug/-/debug-3.2.6.tgz#e83d17de16d8a7efb7717edbe5fb10135eee629b"
  integrity sha1-6D0X3hbYp++3cX7b5fsQE17uYps=
  dependencies:
    ms "^2.1.1"

debug@^4.0.1, debug@^4.1.0, debug@^4.1.1:
  version "4.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/debug/-/debug-4.1.1.tgz#3b72260255109c6b589cee050f1d516139664791"
  integrity sha1-O3ImAlUQnGtYnO4FDx1RYTlmR5E=
  dependencies:
    ms "^2.1.1"

decamelize@^1.1.1, decamelize@^1.1.2, decamelize@^1.2.0:
  version "1.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/decamelize/-/decamelize-1.2.0.tgz#f6534d15148269b20352e7bee26f501f9a191290"
  integrity sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=

decode-uri-component@^0.2.0:
  version "0.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/decode-uri-component/-/decode-uri-component-0.2.0.tgz#eb3913333458775cb84cd1a1fae062106bb87545"
  integrity sha1-6zkTMzRYd1y4TNGh+uBiEGu4dUU=

deep-diff@^0.3.5:
  version "0.3.8"
  resolved "https://nexus.lanmaoly.com/repository/npm/deep-diff/-/deep-diff-0.3.8.tgz#c01de63efb0eec9798801d40c7e0dae25b582c84"
  integrity sha1-wB3mPvsO7JeYgB1Ax+Da4ltYLIQ=

deep-equal@^1.0.1:
  version "1.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/deep-equal/-/deep-equal-1.0.1.tgz#f5d260292b660e084eff4cdbc9f08ad3247448b5"
  integrity sha1-9dJgKStmDghO/0zbyfCK0yR0SLU=

deep-extend@^0.6.0:
  version "0.6.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/deep-extend/-/deep-extend-0.6.0.tgz#c4fa7c95404a17a9c3e8ca7e1537312b736330ac"
  integrity sha1-xPp8lUBKF6nD6Mp+FTcxK3NjMKw=

deep-is@~0.1.3:
  version "0.1.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/deep-is/-/deep-is-0.1.3.tgz#b369d6fb5dbc13eecf524f91b070feedc357cf34"
  integrity sha1-s2nW+128E+7PUk+RsHD+7cNXzzQ=

default-gateway@^4.2.0:
  version "4.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/default-gateway/-/default-gateway-4.2.0.tgz#167104c7500c2115f6dd69b0a536bb8ed720552b"
  integrity sha1-FnEEx1AMIRX23WmwpTa7jtcgVSs=
  dependencies:
    execa "^1.0.0"
    ip-regex "^2.1.0"

default-require-extensions@^1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/default-require-extensions/-/default-require-extensions-1.0.0.tgz#f37ea15d3e13ffd9b437d33e1a75b5fb97874cb8"
  integrity sha1-836hXT4T/9m0N9M+GnW1+5eHTLg=
  dependencies:
    strip-bom "^2.0.0"

define-properties@^1.1.2, define-properties@^1.1.3:
  version "1.1.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/define-properties/-/define-properties-1.1.3.tgz#cf88da6cbee26fe6db7094f61d870cbd84cee9f1"
  integrity sha1-z4jabL7ib+bbcJT2HYcMvYTO6fE=
  dependencies:
    object-keys "^1.0.12"

define-property@^0.2.5:
  version "0.2.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/define-property/-/define-property-0.2.5.tgz#c35b1ef918ec3c990f9a5bc57be04aacec5c8116"
  integrity sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=
  dependencies:
    is-descriptor "^0.1.0"

define-property@^1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/define-property/-/define-property-1.0.0.tgz#769ebaaf3f4a63aad3af9e8d304c9bbe79bfb0e6"
  integrity sha1-dp66rz9KY6rTr56NMEybvnm/sOY=
  dependencies:
    is-descriptor "^1.0.0"

define-property@^2.0.2:
  version "2.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/define-property/-/define-property-2.0.2.tgz#d459689e8d654ba77e02a817f8710d702cb16e9d"
  integrity sha1-1Flono1lS6d+AqgX+HENcCyxbp0=
  dependencies:
    is-descriptor "^1.0.2"
    isobject "^3.0.1"

del@^4.1.1:
  version "4.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/del/-/del-4.1.1.tgz#9e8f117222ea44a31ff3a156c049b99052a9f0b4"
  integrity sha1-no8RciLqRKMf86FWwEm5kFKp8LQ=
  dependencies:
    "@types/glob" "^7.1.1"
    globby "^6.1.0"
    is-path-cwd "^2.0.0"
    is-path-in-cwd "^2.0.0"
    p-map "^2.0.0"
    pify "^4.0.1"
    rimraf "^2.6.3"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/delayed-stream/-/delayed-stream-1.0.0.tgz#df3ae199acadfb7d440aaae0b29e2272b24ec619"
  integrity sha1-3zrhmayt+31ECqrgsp4icrJOxhk=

delegates@^1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/delegates/-/delegates-1.0.0.tgz#84c6e159b81904fdca59a0ef44cd870d31250f9a"
  integrity sha1-hMbhWbgZBP3KWaDvRM2HDTElD5o=

depd@~1.1.2:
  version "1.1.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/depd/-/depd-1.1.2.tgz#9bcd52e14c097763e749b274c4346ed2e560b5a9"
  integrity sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak=

des.js@^1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/des.js/-/des.js-1.0.0.tgz#c074d2e2aa6a8a9a07dbd61f9a15c2cd83ec8ecc"
  integrity sha1-wHTS4qpqipoH29YfmhXCzYPsjsw=
  dependencies:
    inherits "^2.0.1"
    minimalistic-assert "^1.0.0"

destroy@~1.0.4:
  version "1.0.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/destroy/-/destroy-1.0.4.tgz#978857442c44749e4206613e37946205826abd80"
  integrity sha1-l4hXRCxEdJ5CBmE+N5RiBYJqvYA=

detect-file@^1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/detect-file/-/detect-file-1.0.0.tgz#f0d66d03672a825cb1b73bdb3fe62310c8e552b7"
  integrity sha1-8NZtA2cqglyxtzvbP+YjEMjlUrc=

detect-indent@^4.0.0:
  version "4.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/detect-indent/-/detect-indent-4.0.0.tgz#f76d064352cdf43a1cb6ce619c4ee3a9475de208"
  integrity sha1-920GQ1LN9Docts5hnE7jqUdd4gg=
  dependencies:
    repeating "^2.0.0"

detect-libc@^1.0.2:
  version "1.0.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/detect-libc/-/detect-libc-1.0.3.tgz#fa137c4bd698edf55cd5cd02ac559f91a4c4ba9b"
  integrity sha1-+hN8S9aY7fVc1c0CrFWfkaTEups=

detect-newline@^2.1.0:
  version "2.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/detect-newline/-/detect-newline-2.1.0.tgz#f41f1c10be4b00e87b5f13da680759f2c5bfd3e2"
  integrity sha1-9B8cEL5LAOh7XxPaaAdZ8sW/0+I=

detect-node@^2.0.4:
  version "2.0.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/detect-node/-/detect-node-2.0.4.tgz#014ee8f8f669c5c58023da64b8179c083a28c46c"
  integrity sha1-AU7o+PZpxcWAI9pkuBecCDooxGw=

diff@^3.2.0:
  version "3.5.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/diff/-/diff-3.5.0.tgz#800c0dd1e0a8bfbc95835c202ad220fe317e5a12"
  integrity sha1-gAwN0eCov7yVg1wgKtIg/jF+WhI=

diffie-hellman@^5.0.0:
  version "5.0.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/diffie-hellman/-/diffie-hellman-5.0.3.tgz#40e8ee98f55a2149607146921c63e1ae5f3d2875"
  integrity sha1-QOjumPVaIUlgcUaSHGPhrl89KHU=
  dependencies:
    bn.js "^4.1.0"
    miller-rabin "^4.0.0"
    randombytes "^2.0.0"

dir-glob@^2.0.0:
  version "2.2.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/dir-glob/-/dir-glob-2.2.2.tgz#fa09f0694153c8918b18ba0deafae94769fc50c4"
  integrity sha1-+gnwaUFTyJGLGLoN6vrpR2n8UMQ=
  dependencies:
    path-type "^3.0.0"

dns-equal@^1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/dns-equal/-/dns-equal-1.0.0.tgz#b39e7f1da6eb0a75ba9c17324b34753c47e0654d"
  integrity sha1-s55/HabrCnW6nBcySzR1PEfgZU0=

dns-packet@^1.3.1:
  version "1.3.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/dns-packet/-/dns-packet-1.3.1.tgz#12aa426981075be500b910eedcd0b47dd7deda5a"
  integrity sha1-EqpCaYEHW+UAuRDu3NC0fdfe2lo=
  dependencies:
    ip "^1.1.0"
    safe-buffer "^5.0.1"

dns-txt@^2.0.2:
  version "2.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/dns-txt/-/dns-txt-2.0.2.tgz#b91d806f5d27188e4ab3e7d107d881a1cc4642b6"
  integrity sha1-uR2Ab10nGI5Ks+fRB9iBocxGQrY=
  dependencies:
    buffer-indexof "^1.0.0"

doctrine@1.5.0:
  version "1.5.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/doctrine/-/doctrine-1.5.0.tgz#379dce730f6166f76cefa4e6707a159b02c5a6fa"
  integrity sha1-N53Ocw9hZvds76TmcHoVmwLFpvo=
  dependencies:
    esutils "^2.0.2"
    isarray "^1.0.0"

doctrine@^2.1.0:
  version "2.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/doctrine/-/doctrine-2.1.0.tgz#5cd01fc101621b42c4cd7f5d1a66243716d3f39d"
  integrity sha1-XNAfwQFiG0LEzX9dGmYkNxbT850=
  dependencies:
    esutils "^2.0.2"

doctrine@^3.0.0:
  version "3.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/doctrine/-/doctrine-3.0.0.tgz#addebead72a6574db783639dc87a121773973961"
  integrity sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=
  dependencies:
    esutils "^2.0.2"

dom-align@1.x, dom-align@^1.7.0:
  version "1.8.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/dom-align/-/dom-align-1.8.2.tgz#fdcd36bce25ba8d34fe3582efd57ac767df490bd"
  integrity sha1-/c02vOJbqNNP41gu/Vesdn30kL0=

dom-converter@^0.2:
  version "0.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/dom-converter/-/dom-converter-0.2.0.tgz#6721a9daee2e293682955b6afe416771627bb768"
  integrity sha1-ZyGp2u4uKTaClVtq/kFncWJ7t2g=
  dependencies:
    utila "~0.4"

dom-scroll-into-view@1.x:
  version "1.2.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/dom-scroll-into-view/-/dom-scroll-into-view-1.2.1.tgz#e8f36732dd089b0201a88d7815dc3f88e6d66c7e"
  integrity sha1-6PNnMt0ImwIBqI14Fdw/iObWbH4=

dom-serializer@0:
  version "0.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/dom-serializer/-/dom-serializer-0.1.1.tgz#1ec4059e284babed36eec2941d4a970a189ce7c0"
  integrity sha1-HsQFnihLq+027sKUHUqXChic58A=
  dependencies:
    domelementtype "^1.3.0"
    entities "^1.1.1"

dom-walk@^0.1.0:
  version "0.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/dom-walk/-/dom-walk-0.1.1.tgz#672226dc74c8f799ad35307df936aba11acd6018"
  integrity sha1-ZyIm3HTI95mtNTB9+TaroRrNYBg=

domain-browser@^1.1.1:
  version "1.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/domain-browser/-/domain-browser-1.2.0.tgz#3d31f50191a6749dd1375a7f522e823d42e54eda"
  integrity sha1-PTH1AZGmdJ3RN1p/Ui6CPULlTto=

domelementtype@1, domelementtype@^1.3.0, domelementtype@^1.3.1:
  version "1.3.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/domelementtype/-/domelementtype-1.3.1.tgz#d048c44b37b0d10a7f2a3d5fee3f4333d790481f"
  integrity sha1-0EjESzew0Qp/Kj1f7j9DM9eQSB8=

domexception@^1.0.1:
  version "1.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/domexception/-/domexception-1.0.1.tgz#937442644ca6a31261ef36e3ec677fe805582c90"
  integrity sha1-k3RCZEymoxJh7zbj7Gd/6AVYLJA=
  dependencies:
    webidl-conversions "^4.0.2"

domhandler@^2.3.0:
  version "2.4.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/domhandler/-/domhandler-2.4.2.tgz#8805097e933d65e85546f726d60f5eb88b44f803"
  integrity sha1-iAUJfpM9ZehVRvcm1g9euItE+AM=
  dependencies:
    domelementtype "1"

domutils@1.5.1:
  version "1.5.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/domutils/-/domutils-1.5.1.tgz#dcd8488a26f563d61079e48c9f7b7e32373682cf"
  integrity sha1-3NhIiib1Y9YQeeSMn3t+Mjc2gs8=
  dependencies:
    dom-serializer "0"
    domelementtype "1"

domutils@^1.5.1:
  version "1.7.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/domutils/-/domutils-1.7.0.tgz#56ea341e834e06e6748af7a1cb25da67ea9f8c2a"
  integrity sha1-Vuo0HoNOBuZ0ivehyyXaZ+qfjCo=
  dependencies:
    dom-serializer "0"
    domelementtype "1"

duplexer@^0.1.1:
  version "0.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/duplexer/-/duplexer-0.1.1.tgz#ace6ff808c1ce66b57d1ebf97977acb02334cfc1"
  integrity sha1-rOb/gIwc5mtX0ev5eXessCM0z8E=

duplexify@^3.4.2, duplexify@^3.6.0:
  version "3.7.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/duplexify/-/duplexify-3.7.1.tgz#2a4df5317f6ccfd91f86d6fd25d8d8a103b88309"
  integrity sha1-Kk31MX9sz9kfhtb9JdjYoQO4gwk=
  dependencies:
    end-of-stream "^1.0.0"
    inherits "^2.0.1"
    readable-stream "^2.0.0"
    stream-shift "^1.0.0"

ecc-jsbn@~0.1.1:
  version "0.1.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/ecc-jsbn/-/ecc-jsbn-0.1.2.tgz#3a83a904e54353287874c564b7549386849a98c9"
  integrity sha1-OoOpBOVDUyh4dMVkt1SThoSamMk=
  dependencies:
    jsbn "~0.1.0"
    safer-buffer "^2.1.0"

ee-first@1.1.1:
  version "1.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/ee-first/-/ee-first-1.1.1.tgz#590c61156b0ae2f4f0255732a158b266bc56b21d"
  integrity sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=

ejs@^2.6.1:
  version "2.6.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/ejs/-/ejs-2.6.2.tgz#3a32c63d1cd16d11266cd4703b14fec4e74ab4f6"
  integrity sha1-OjLGPRzRbREmbNRwOxT+xOdKtPY=

electron-to-chromium@^1.3.191:
  version "1.3.200"
  resolved "https://nexus.lanmaoly.com/repository/npm/electron-to-chromium/-/electron-to-chromium-1.3.200.tgz#78fb858b466269e8eb46d31a52562f00c865127f"
  integrity sha1-ePuFi0ZiaejrRtMaUlYvAMhlEn8=

elliptic@^6.0.0:
  version "6.5.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/elliptic/-/elliptic-6.5.0.tgz#2b8ed4c891b7de3200e14412a5b8248c7af505ca"
  integrity sha1-K47UyJG33jIA4UQSpbgkjHr1Bco=
  dependencies:
    bn.js "^4.4.0"
    brorand "^1.0.1"
    hash.js "^1.0.0"
    hmac-drbg "^1.0.0"
    inherits "^2.0.1"
    minimalistic-assert "^1.0.0"
    minimalistic-crypto-utils "^1.0.0"

emoji-regex@^7.0.1, emoji-regex@^7.0.2:
  version "7.0.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/emoji-regex/-/emoji-regex-7.0.3.tgz#933a04052860c85e83c122479c4748a8e4c72156"
  integrity sha1-kzoEBShgyF6DwSJHnEdIqOTHIVY=

emojis-list@^2.0.0:
  version "2.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/emojis-list/-/emojis-list-2.1.0.tgz#4daa4d9db00f9819880c79fa457ae5b09a1fd389"
  integrity sha1-TapNnbAPmBmIDHn6RXrlsJof04k=

emojis-list@^3.0.0:
  version "3.0.0"
  resolved "https://r.cnpmjs.org/emojis-list/download/emojis-list-3.0.0.tgz#5570662046ad29e2e916e71aae260abdff4f6a78"
  integrity sha1-VXBmIEatKeLpFucariYKvf9Pang=

encodeurl@~1.0.2:
  version "1.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/encodeurl/-/encodeurl-1.0.2.tgz#ad3ff4c86ec2d029322f5a02c3a9a606c95b3f59"
  integrity sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=

encoding@^0.1.11:
  version "0.1.12"
  resolved "https://nexus.lanmaoly.com/repository/npm/encoding/-/encoding-0.1.12.tgz#538b66f3ee62cd1ab51ec323829d1f9480c74beb"
  integrity sha1-U4tm8+5izRq1HsMjgp0flIDHS+s=
  dependencies:
    iconv-lite "~0.4.13"

end-of-stream@^1.0.0, end-of-stream@^1.1.0:
  version "1.4.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/end-of-stream/-/end-of-stream-1.4.1.tgz#ed29634d19baba463b6ce6b80a37213eab71ec43"
  integrity sha1-7SljTRm6ukY7bOa4CjchPqtx7EM=
  dependencies:
    once "^1.4.0"

enhanced-resolve@4.1.0, enhanced-resolve@^4.1.0:
  version "4.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/enhanced-resolve/-/enhanced-resolve-4.1.0.tgz#41c7e0bfdfe74ac1ffe1e57ad6a5c6c9f3742a7f"
  integrity sha1-Qcfgv9/nSsH/4eV61qXGyfN0Kn8=
  dependencies:
    graceful-fs "^4.1.2"
    memory-fs "^0.4.0"
    tapable "^1.0.0"

enhanced-resolve@^4.0.0:
  version "4.5.0"
  resolved "https://r.cnpmjs.org/enhanced-resolve/download/enhanced-resolve-4.5.0.tgz#2f3cfd84dbe3b487f18f2db2ef1e064a571ca5ec"
  integrity sha1-Lzz9hNvjtIfxjy2y7x4GSlccpew=
  dependencies:
    graceful-fs "^4.1.2"
    memory-fs "^0.5.0"
    tapable "^1.0.0"

entities@^1.1.1:
  version "1.1.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/entities/-/entities-1.1.2.tgz#bdfa735299664dfafd34529ed4f8522a275fea56"
  integrity sha1-vfpzUplmTfr9NFKe1PhSKidf6lY=

errno@^0.1.1, errno@^0.1.3, errno@~0.1.7:
  version "0.1.7"
  resolved "https://nexus.lanmaoly.com/repository/npm/errno/-/errno-0.1.7.tgz#4684d71779ad39af177e3f007996f7c67c852618"
  integrity sha1-RoTXF3mtOa8Xfj8AeZb3xnyFJhg=
  dependencies:
    prr "~1.0.1"

error-ex@^1.2.0, error-ex@^1.3.1:
  version "1.3.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/error-ex/-/error-ex-1.3.2.tgz#b4ac40648107fdcdcfae242f428bea8a14d4f1bf"
  integrity sha1-tKxAZIEH/c3PriQvQovqihTU8b8=
  dependencies:
    is-arrayish "^0.2.1"

error-stack-parser@^2.0.0:
  version "2.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/error-stack-parser/-/error-stack-parser-2.0.2.tgz#4ae8dbaa2bf90a8b450707b9149dcabca135520d"
  integrity sha1-Sujbqiv5CotFBwe5FJ3KvKE1Ug0=
  dependencies:
    stackframe "^1.0.4"

es-abstract@^1.11.0, es-abstract@^1.12.0, es-abstract@^1.4.3, es-abstract@^1.5.1, es-abstract@^1.7.0:
  version "1.13.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/es-abstract/-/es-abstract-1.13.0.tgz#ac86145fdd5099d8dd49558ccba2eaf9b88e24e9"
  integrity sha1-rIYUX91QmdjdSVWMy6Lq+biOJOk=
  dependencies:
    es-to-primitive "^1.2.0"
    function-bind "^1.1.1"
    has "^1.0.3"
    is-callable "^1.1.4"
    is-regex "^1.0.4"
    object-keys "^1.0.12"

es-to-primitive@^1.2.0:
  version "1.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/es-to-primitive/-/es-to-primitive-1.2.0.tgz#edf72478033456e8dda8ef09e00ad9650707f377"
  integrity sha1-7fckeAM0VujdqO8J4ArZZQcH83c=
  dependencies:
    is-callable "^1.1.4"
    is-date-object "^1.0.1"
    is-symbol "^1.0.2"

escape-html@~1.0.3:
  version "1.0.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/escape-html/-/escape-html-1.0.3.tgz#0258eae4d3d0c0974de1c169188ef0051d1d1988"
  integrity sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=

escape-string-regexp@^1.0.2, escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz#1b61c0562190a8dff6ae3bb2cf0200ca130b86d4"
  integrity sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=

escodegen@^1.9.1:
  version "1.11.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/escodegen/-/escodegen-1.11.1.tgz#c485ff8d6b4cdb89e27f4a856e91f118401ca510"
  integrity sha1-xIX/jWtM24nif0qFbpHxGEAcpRA=
  dependencies:
    esprima "^3.1.3"
    estraverse "^4.2.0"
    esutils "^2.0.2"
    optionator "^0.8.1"
  optionalDependencies:
    source-map "~0.6.1"

eslint-config-airbnb-base@^13.2.0:
  version "13.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/eslint-config-airbnb-base/-/eslint-config-airbnb-base-13.2.0.tgz#f6ea81459ff4dec2dda200c35f1d8f7419d57943"
  integrity sha1-9uqBRZ/03sLdogDDXx2PdBnVeUM=
  dependencies:
    confusing-browser-globals "^1.0.5"
    object.assign "^4.1.0"
    object.entries "^1.1.0"

eslint-config-airbnb@^17.1.0:
  version "17.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/eslint-config-airbnb/-/eslint-config-airbnb-17.1.1.tgz#2272e0b86bb1e2b138cdf88d07a3b6f4cda3d626"
  integrity sha1-InLguGux4rE4zfiNB6O29M2j1iY=
  dependencies:
    eslint-config-airbnb-base "^13.2.0"
    object.assign "^4.1.0"
    object.entries "^1.1.0"

eslint-import-resolver-babel-module@5.0.0-beta.1:
  version "5.0.0-beta.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/eslint-import-resolver-babel-module/-/eslint-import-resolver-babel-module-5.0.0-beta.1.tgz#3f7899c14039aab1b1ffa2f0e53463a6152ef121"
  integrity sha1-P3iZwUA5qrGx/6Lw5TRjphUu8SE=
  dependencies:
    pkg-up "^2.0.0"
    resolve "^1.8.1"

eslint-import-resolver-node@^0.3.2:
  version "0.3.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/eslint-import-resolver-node/-/eslint-import-resolver-node-0.3.2.tgz#58f15fb839b8d0576ca980413476aab2472db66a"
  integrity sha1-WPFfuDm40FdsqYBBNHaqskcttmo=
  dependencies:
    debug "^2.6.9"
    resolve "^1.5.0"

eslint-module-utils@^2.4.0:
  version "2.4.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/eslint-module-utils/-/eslint-module-utils-2.4.0.tgz#8b93499e9b00eab80ccb6614e69f03678e84e09a"
  integrity sha1-i5NJnpsA6rgMy2YU5p8DZ46E4Jo=
  dependencies:
    debug "^2.6.8"
    pkg-dir "^2.0.0"

eslint-plugin-import@^2.14.0:
  version "2.17.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/eslint-plugin-import/-/eslint-plugin-import-2.17.1.tgz#b888feb4d9b3ee155113c8dccdd4bec5db33bdf4"
  integrity sha1-uIj+tNmz7hVRE8jczdS+xdszvfQ=
  dependencies:
    array-includes "^3.0.3"
    contains-path "^0.1.0"
    debug "^2.6.9"
    doctrine "1.5.0"
    eslint-import-resolver-node "^0.3.2"
    eslint-module-utils "^2.4.0"
    has "^1.0.3"
    lodash "^4.17.11"
    minimatch "^3.0.4"
    read-pkg-up "^2.0.0"
    resolve "^1.10.0"

eslint-plugin-jsx-a11y@^6.1.1:
  version "6.2.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/eslint-plugin-jsx-a11y/-/eslint-plugin-jsx-a11y-6.2.1.tgz#4ebba9f339b600ff415ae4166e3e2e008831cf0c"
  integrity sha1-Trup8zm2AP9BWuQWbj4uAIgxzww=
  dependencies:
    aria-query "^3.0.0"
    array-includes "^3.0.3"
    ast-types-flow "^0.0.7"
    axobject-query "^2.0.2"
    damerau-levenshtein "^1.0.4"
    emoji-regex "^7.0.2"
    has "^1.0.3"
    jsx-ast-utils "^2.0.1"

eslint-plugin-react@^7.11.1:
  version "7.12.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/eslint-plugin-react/-/eslint-plugin-react-7.12.4.tgz#b1ecf26479d61aee650da612e425c53a99f48c8c"
  integrity sha1-sezyZHnWGu5lDaYS5CXFOpn0jIw=
  dependencies:
    array-includes "^3.0.3"
    doctrine "^2.1.0"
    has "^1.0.3"
    jsx-ast-utils "^2.0.1"
    object.fromentries "^2.0.0"
    prop-types "^15.6.2"
    resolve "^1.9.0"

eslint-scope@3.7.1:
  version "3.7.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/eslint-scope/-/eslint-scope-3.7.1.tgz#3d63c3edfda02e06e01a452ad88caacc7cdcb6e8"
  integrity sha1-PWPD7f2gLgbgGkUq2IyqzHzctug=
  dependencies:
    esrecurse "^4.1.0"
    estraverse "^4.1.1"

eslint-scope@^4.0.0, eslint-scope@^4.0.3:
  version "4.0.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/eslint-scope/-/eslint-scope-4.0.3.tgz#ca03833310f6889a3264781aa82e63eb9cfe7848"
  integrity sha1-ygODMxD2iJoyZHgaqC5j65z+eEg=
  dependencies:
    esrecurse "^4.1.0"
    estraverse "^4.1.1"

eslint-utils@^1.3.1:
  version "1.4.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/eslint-utils/-/eslint-utils-1.4.0.tgz#e2c3c8dba768425f897cf0f9e51fe2e241485d4c"
  integrity sha1-4sPI26doQl+JfPD55R/i4kFIXUw=
  dependencies:
    eslint-visitor-keys "^1.0.0"

eslint-visitor-keys@^1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/eslint-visitor-keys/-/eslint-visitor-keys-1.0.0.tgz#3f3180fb2e291017716acb4c9d6d5b5c34a6a81d"
  integrity sha1-PzGA+y4pEBdxastMnW1bXDSmqB0=

eslint@^5.5.0:
  version "5.16.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/eslint/-/eslint-5.16.0.tgz#a1e3ac1aae4a3fbd8296fcf8f7ab7314cbb6abea"
  integrity sha1-oeOsGq5KP72Clvz496tzFMu2q+o=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    ajv "^6.9.1"
    chalk "^2.1.0"
    cross-spawn "^6.0.5"
    debug "^4.0.1"
    doctrine "^3.0.0"
    eslint-scope "^4.0.3"
    eslint-utils "^1.3.1"
    eslint-visitor-keys "^1.0.0"
    espree "^5.0.1"
    esquery "^1.0.1"
    esutils "^2.0.2"
    file-entry-cache "^5.0.1"
    functional-red-black-tree "^1.0.1"
    glob "^7.1.2"
    globals "^11.7.0"
    ignore "^4.0.6"
    import-fresh "^3.0.0"
    imurmurhash "^0.1.4"
    inquirer "^6.2.2"
    js-yaml "^3.13.0"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.3.0"
    lodash "^4.17.11"
    minimatch "^3.0.4"
    mkdirp "^0.5.1"
    natural-compare "^1.4.0"
    optionator "^0.8.2"
    path-is-inside "^1.0.2"
    progress "^2.0.0"
    regexpp "^2.0.1"
    semver "^5.5.1"
    strip-ansi "^4.0.0"
    strip-json-comments "^2.0.1"
    table "^5.2.3"
    text-table "^0.2.0"

espree@^5.0.1:
  version "5.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/espree/-/espree-5.0.1.tgz#5d6526fa4fc7f0788a5cf75b15f30323e2f81f7a"
  integrity sha1-XWUm+k/H8HiKXPdbFfMDI+L4H3o=
  dependencies:
    acorn "^6.0.7"
    acorn-jsx "^5.0.0"
    eslint-visitor-keys "^1.0.0"

esprima@^3.1.3:
  version "3.1.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/esprima/-/esprima-3.1.3.tgz#fdca51cee6133895e3c88d535ce49dbff62a4633"
  integrity sha1-/cpRzuYTOJXjyI1TXOSdv/YqRjM=

esprima@^4.0.0:
  version "4.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/esprima/-/esprima-4.0.1.tgz#13b04cdb3e6c5d19df91ab6987a8695619b0aa71"
  integrity sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=

esquery@^1.0.1:
  version "1.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/esquery/-/esquery-1.0.1.tgz#406c51658b1f5991a5f9b62b1dc25b00e3e5c708"
  integrity sha1-QGxRZYsfWZGl+bYrHcJbAOPlxwg=
  dependencies:
    estraverse "^4.0.0"

esrecurse@^4.1.0:
  version "4.2.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/esrecurse/-/esrecurse-4.2.1.tgz#007a3b9fdbc2b3bb87e4879ea19c92fdbd3942cf"
  integrity sha1-AHo7n9vCs7uH5IeeoZyS/b05Qs8=
  dependencies:
    estraverse "^4.1.0"

estraverse@^4.0.0, estraverse@^4.1.0, estraverse@^4.1.1, estraverse@^4.2.0:
  version "4.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/estraverse/-/estraverse-4.2.0.tgz#0dee3fed31fcd469618ce7342099fc1afa0bdb13"
  integrity sha1-De4/7TH81GlhjOc0IJn8GvoL2xM=

esutils@^2.0.0, esutils@^2.0.2:
  version "2.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/esutils/-/esutils-2.0.2.tgz#0abf4f1caa5bcb1f7a9d8acc6dea4faaa04bac9b"
  integrity sha1-Cr9PHKpbyx96nYrMbepPqqBLrJs=

etag@~1.8.1:
  version "1.8.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/etag/-/etag-1.8.1.tgz#41ae2eeb65efa62268aebfea83ac7d79299b0887"
  integrity sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=

eventemitter3@^3.0.0:
  version "3.1.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/eventemitter3/-/eventemitter3-3.1.2.tgz#2d3d48f9c346698fce83a85d7d664e98535df6e7"
  integrity sha1-LT1I+cNGaY/Og6hdfWZOmFNd9uc=

events@^3.0.0:
  version "3.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/events/-/events-3.0.0.tgz#9a0a0dfaf62893d92b875b8f2698ca4114973e88"
  integrity sha1-mgoN+vYok9krh1uPJpjKQRSXPog=

eventsource@^1.0.7:
  version "1.0.7"
  resolved "https://nexus.lanmaoly.com/repository/npm/eventsource/-/eventsource-1.0.7.tgz#8fbc72c93fcd34088090bc0a4e64f4b5cee6d8d0"
  integrity sha1-j7xyyT/NNAiAkLwKTmT0tc7m2NA=
  dependencies:
    original "^1.0.0"

evp_bytestokey@^1.0.0, evp_bytestokey@^1.0.3:
  version "1.0.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/evp_bytestokey/-/evp_bytestokey-1.0.3.tgz#7fcbdb198dc71959432efe13842684e0525acb02"
  integrity sha1-f8vbGY3HGVlDLv4ThCaE4FJaywI=
  dependencies:
    md5.js "^1.3.4"
    safe-buffer "^5.1.1"

exec-sh@^0.2.0:
  version "0.2.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/exec-sh/-/exec-sh-0.2.2.tgz#2a5e7ffcbd7d0ba2755bdecb16e5a427dfbdec36"
  integrity sha1-Kl5//L19C6J1W97LFuWkJ9+97DY=
  dependencies:
    merge "^1.2.0"

execa@^0.7.0:
  version "0.7.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/execa/-/execa-0.7.0.tgz#944becd34cc41ee32a63a9faf27ad5a65fc59777"
  integrity sha1-lEvs00zEHuMqY6n68nrVpl/Fl3c=
  dependencies:
    cross-spawn "^5.0.1"
    get-stream "^3.0.0"
    is-stream "^1.1.0"
    npm-run-path "^2.0.0"
    p-finally "^1.0.0"
    signal-exit "^3.0.0"
    strip-eof "^1.0.0"

execa@^1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/execa/-/execa-1.0.0.tgz#c6236a5bb4df6d6f15e88e7f017798216749ddd8"
  integrity sha1-xiNqW7TfbW8V6I5/AXeYIWdJ3dg=
  dependencies:
    cross-spawn "^6.0.0"
    get-stream "^4.0.0"
    is-stream "^1.1.0"
    npm-run-path "^2.0.0"
    p-finally "^1.0.0"
    signal-exit "^3.0.0"
    strip-eof "^1.0.0"

exenv@^1.2.0:
  version "1.2.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/exenv/-/exenv-1.2.2.tgz#2ae78e85d9894158670b03d47bec1f03bd91bb9d"
  integrity sha1-KueOhdmJQVhnCwPUe+wfA72Ru50=

exit@^0.1.2:
  version "0.1.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/exit/-/exit-0.1.2.tgz#0632638f8d877cc82107d30a0fff1a17cba1cd0c"
  integrity sha1-BjJjj42HfMghB9MKD/8aF8uhzQw=

expand-brackets@^0.1.4:
  version "0.1.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/expand-brackets/-/expand-brackets-0.1.5.tgz#df07284e342a807cd733ac5af72411e581d1177b"
  integrity sha1-3wcoTjQqgHzXM6xa9yQR5YHRF3s=
  dependencies:
    is-posix-bracket "^0.1.0"

expand-brackets@^2.1.4:
  version "2.1.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/expand-brackets/-/expand-brackets-2.1.4.tgz#b77735e315ce30f6b6eff0f83b04151a22449622"
  integrity sha1-t3c14xXOMPa27/D4OwQVGiJEliI=
  dependencies:
    debug "^2.3.3"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    posix-character-classes "^0.1.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

expand-range@^1.8.1:
  version "1.8.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/expand-range/-/expand-range-1.8.2.tgz#a299effd335fe2721ebae8e257ec79644fc85337"
  integrity sha1-opnv/TNf4nIeuujiV+x5ZE/IUzc=
  dependencies:
    fill-range "^2.1.0"

expand-tilde@^2.0.0, expand-tilde@^2.0.2:
  version "2.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/expand-tilde/-/expand-tilde-2.0.2.tgz#97e801aa052df02454de46b02bf621642cdc8502"
  integrity sha1-l+gBqgUt8CRU3kawK/YhZCzchQI=
  dependencies:
    homedir-polyfill "^1.0.1"

expect@^23.6.0:
  version "23.6.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/expect/-/expect-23.6.0.tgz#1e0c8d3ba9a581c87bd71fb9bc8862d443425f98"
  integrity sha1-HgyNO6mlgch71x+5vIhi1ENCX5g=
  dependencies:
    ansi-styles "^3.2.0"
    jest-diff "^23.6.0"
    jest-get-type "^22.1.0"
    jest-matcher-utils "^23.6.0"
    jest-message-util "^23.4.0"
    jest-regex-util "^23.3.0"

express@^4.16.3, express@^4.17.1:
  version "4.17.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/express/-/express-4.17.1.tgz#4491fc38605cf51f8629d39c2b5d026f98a4c134"
  integrity sha1-RJH8OGBc9R+GKdOcK10Cb5ikwTQ=
  dependencies:
    accepts "~1.3.7"
    array-flatten "1.1.1"
    body-parser "1.19.0"
    content-disposition "0.5.3"
    content-type "~1.0.4"
    cookie "0.4.0"
    cookie-signature "1.0.6"
    debug "2.6.9"
    depd "~1.1.2"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    finalhandler "~1.1.2"
    fresh "0.5.2"
    merge-descriptors "1.0.1"
    methods "~1.1.2"
    on-finished "~2.3.0"
    parseurl "~1.3.3"
    path-to-regexp "0.1.7"
    proxy-addr "~2.0.5"
    qs "6.7.0"
    range-parser "~1.2.1"
    safe-buffer "5.1.2"
    send "0.17.1"
    serve-static "1.14.1"
    setprototypeof "1.1.1"
    statuses "~1.5.0"
    type-is "~1.6.18"
    utils-merge "1.0.1"
    vary "~1.1.2"

extend-shallow@^2.0.1:
  version "2.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/extend-shallow/-/extend-shallow-2.0.1.tgz#51af7d614ad9a9f610ea1bafbb989d6b1c56890f"
  integrity sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=
  dependencies:
    is-extendable "^0.1.0"

extend-shallow@^3.0.0, extend-shallow@^3.0.2:
  version "3.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/extend-shallow/-/extend-shallow-3.0.2.tgz#26a71aaf073b39fb2127172746131c2704028db8"
  integrity sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg=
  dependencies:
    assign-symbols "^1.0.0"
    is-extendable "^1.0.1"

extend@~3.0.2:
  version "3.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/extend/-/extend-3.0.2.tgz#f8b1136b4071fbd8eb140aff858b1019ec2915fa"
  integrity sha1-+LETa0Bx+9jrFAr/hYsQGewpFfo=

external-editor@^3.0.3:
  version "3.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/external-editor/-/external-editor-3.1.0.tgz#cb03f740befae03ea4d283caed2741a83f335495"
  integrity sha1-ywP3QL764D6k0oPK7SdBqD8zVJU=
  dependencies:
    chardet "^0.7.0"
    iconv-lite "^0.4.24"
    tmp "^0.0.33"

extglob@^0.3.1:
  version "0.3.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/extglob/-/extglob-0.3.2.tgz#2e18ff3d2f49ab2765cec9023f011daa8d8349a1"
  integrity sha1-Lhj/PS9JqydlzskCPwEdqo2DSaE=
  dependencies:
    is-extglob "^1.0.0"

extglob@^2.0.4:
  version "2.0.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/extglob/-/extglob-2.0.4.tgz#ad00fe4dc612a9232e8718711dc5cb5ab0285543"
  integrity sha1-rQD+TcYSqSMuhxhxHcXLWrAoVUM=
  dependencies:
    array-unique "^0.3.2"
    define-property "^1.0.0"
    expand-brackets "^2.1.4"
    extend-shallow "^2.0.1"
    fragment-cache "^0.2.1"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

extsprintf@1.3.0:
  version "1.3.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/extsprintf/-/extsprintf-1.3.0.tgz#96918440e3041a7a414f8c52e3c574eb3c3e1e05"
  integrity sha1-lpGEQOMEGnpBT4xS48V06zw+HgU=

extsprintf@^1.2.0:
  version "1.4.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/extsprintf/-/extsprintf-1.4.0.tgz#e2689f8f356fad62cca65a3a91c5df5f9551692f"
  integrity sha1-4mifjzVvrWLMplo6kcXfX5VRaS8=

fast-deep-equal@^2.0.1:
  version "2.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/fast-deep-equal/-/fast-deep-equal-2.0.1.tgz#7b05218ddf9667bf7f370bf7fdb2cb15fdd0aa49"
  integrity sha1-ewUhjd+WZ79/Nwv3/bLLFf3Qqkk=

fast-deep-equal@^3.1.1:
  version "3.1.3"
  resolved "https://r.cnpmjs.org/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz#3a7d56b559d6cbc3eb512325244e619a65c6c525"
  integrity sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=

fast-json-stable-stringify@^2.0.0:
  version "2.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/fast-json-stable-stringify/-/fast-json-stable-stringify-2.0.0.tgz#d5142c0caee6b1189f87d3a76111064f86c8bbf2"
  integrity sha1-1RQsDK7msRifh9OnYREGT4bIu/I=

fast-levenshtein@^2.0.6, fast-levenshtein@~2.0.4:
  version "2.0.6"
  resolved "https://nexus.lanmaoly.com/repository/npm/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz#3d8a5c66883a16a30ca8643e851f19baa7797917"
  integrity sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=

fastparse@^1.1.1:
  version "1.1.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/fastparse/-/fastparse-1.1.2.tgz#91728c5a5942eced8531283c79441ee4122c35a9"
  integrity sha1-kXKMWllC7O2FMSg8eUQe5BIsNak=

faye-websocket@^0.10.0:
  version "0.10.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/faye-websocket/-/faye-websocket-0.10.0.tgz#4e492f8d04dfb6f89003507f6edbf2d501e7c6f4"
  integrity sha1-TkkvjQTftviQA1B/btvy1QHnxvQ=
  dependencies:
    websocket-driver ">=0.5.1"

faye-websocket@~0.11.1:
  version "0.11.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/faye-websocket/-/faye-websocket-0.11.3.tgz#5c0e9a8968e8912c286639fde977a8b209f2508e"
  integrity sha1-XA6aiWjokSwoZjn96XeosgnyUI4=
  dependencies:
    websocket-driver ">=0.5.1"

fb-watchman@^2.0.0:
  version "2.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/fb-watchman/-/fb-watchman-2.0.0.tgz#54e9abf7dfa2f26cd9b1636c588c1afc05de5d58"
  integrity sha1-VOmr99+i8mzZsWNsWIwa/AXeXVg=
  dependencies:
    bser "^2.0.0"

fbjs@^0.8.3, fbjs@^0.8.9:
  version "0.8.17"
  resolved "https://nexus.lanmaoly.com/repository/npm/fbjs/-/fbjs-0.8.17.tgz#c4d598ead6949112653d6588b01a5cdcd9f90fdd"
  integrity sha1-xNWY6taUkRJlPWWIsBpc3Nn5D90=
  dependencies:
    core-js "^1.0.0"
    isomorphic-fetch "^2.1.1"
    loose-envify "^1.0.0"
    object-assign "^4.1.0"
    promise "^7.1.1"
    setimmediate "^1.0.5"
    ua-parser-js "^0.7.18"

figgy-pudding@^3.5.1:
  version "3.5.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/figgy-pudding/-/figgy-pudding-3.5.1.tgz#862470112901c727a0e495a80744bd5baa1d6790"
  integrity sha1-hiRwESkBxyeg5JWoB0S9W6odZ5A=

figures@^2.0.0:
  version "2.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/figures/-/figures-2.0.0.tgz#3ab1a2d2a62c8bfb431a0c94cb797a2fce27c962"
  integrity sha1-OrGi0qYsi/tDGgyUy3l6L84nyWI=
  dependencies:
    escape-string-regexp "^1.0.5"

file-entry-cache@^5.0.1:
  version "5.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/file-entry-cache/-/file-entry-cache-5.0.1.tgz#ca0f6efa6dd3d561333fb14515065c2fafdf439c"
  integrity sha1-yg9u+m3T1WEzP7FFFQZcL6/fQ5w=
  dependencies:
    flat-cache "^2.0.1"

file-loader@^2.0.0:
  version "2.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/file-loader/-/file-loader-2.0.0.tgz#39749c82f020b9e85901dcff98e8004e6401cfde"
  integrity sha1-OXScgvAguehZAdz/mOgATmQBz94=
  dependencies:
    loader-utils "^1.0.2"
    schema-utils "^1.0.0"

file-loader@^6.0.0:
  version "6.2.0"
  resolved "https://r.cnpmjs.org/file-loader/download/file-loader-6.2.0.tgz#baef7cf8e1840df325e4390b4484879480eebe4d"
  integrity sha1-uu98+OGEDfMl5DkLRISHlIDuvk0=
  dependencies:
    loader-utils "^2.0.0"
    schema-utils "^3.0.0"

filename-regex@^2.0.0:
  version "2.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/filename-regex/-/filename-regex-2.0.1.tgz#c1c4b9bee3e09725ddb106b75c1e301fe2f18b26"
  integrity sha1-wcS5vuPglyXdsQa3XB4wH+LxiyY=

fileset@^2.0.2:
  version "2.0.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/fileset/-/fileset-2.0.3.tgz#8e7548a96d3cc2327ee5e674168723a333bba2a0"
  integrity sha1-jnVIqW08wjJ+5eZ0FocjozO7oqA=
  dependencies:
    glob "^7.0.3"
    minimatch "^3.0.3"

filesize@^3.6.1:
  version "3.6.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/filesize/-/filesize-3.6.1.tgz#090bb3ee01b6f801a8a8be99d31710b3422bb317"
  integrity sha1-CQuz7gG2+AGoqL6Z0xcQs0Irsxc=

fill-range@^2.1.0:
  version "2.2.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/fill-range/-/fill-range-2.2.4.tgz#eb1e773abb056dcd8df2bfdf6af59b8b3a936565"
  integrity sha1-6x53OrsFbc2N8r/favWbizqTZWU=
  dependencies:
    is-number "^2.1.0"
    isobject "^2.0.0"
    randomatic "^3.0.0"
    repeat-element "^1.1.2"
    repeat-string "^1.5.2"

fill-range@^4.0.0:
  version "4.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/fill-range/-/fill-range-4.0.0.tgz#d544811d428f98eb06a63dc402d2403c328c38f7"
  integrity sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc=
  dependencies:
    extend-shallow "^2.0.1"
    is-number "^3.0.0"
    repeat-string "^1.6.1"
    to-regex-range "^2.1.0"

finalhandler@~1.1.2:
  version "1.1.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/finalhandler/-/finalhandler-1.1.2.tgz#b7e7d000ffd11938d0fdb053506f6ebabe9f587d"
  integrity sha1-t+fQAP/RGTjQ/bBTUG9uur6fWH0=
  dependencies:
    debug "2.6.9"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    on-finished "~2.3.0"
    parseurl "~1.3.3"
    statuses "~1.5.0"
    unpipe "~1.0.0"

find-babel-config@^1.1.0:
  version "1.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/find-babel-config/-/find-babel-config-1.2.0.tgz#a9b7b317eb5b9860cda9d54740a8c8337a2283a2"
  integrity sha1-qbezF+tbmGDNqdVHQKjIM3oig6I=
  dependencies:
    json5 "^0.5.1"
    path-exists "^3.0.0"

find-cache-dir@^1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/find-cache-dir/-/find-cache-dir-1.0.0.tgz#9288e3e9e3cc3748717d39eade17cf71fc30ee6f"
  integrity sha1-kojj6ePMN0hxfTnq3hfPcfww7m8=
  dependencies:
    commondir "^1.0.1"
    make-dir "^1.0.0"
    pkg-dir "^2.0.0"

find-cache-dir@^2.0.0, find-cache-dir@^2.1.0:
  version "2.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/find-cache-dir/-/find-cache-dir-2.1.0.tgz#8d0f94cd13fe43c6c7c261a0d86115ca918c05f7"
  integrity sha1-jQ+UzRP+Q8bHwmGg2GEVypGMBfc=
  dependencies:
    commondir "^1.0.1"
    make-dir "^2.0.0"
    pkg-dir "^3.0.0"

find-up@^1.0.0:
  version "1.1.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/find-up/-/find-up-1.1.2.tgz#6b2e9822b1a2ce0a60ab64d610eccad53cb24d0f"
  integrity sha1-ay6YIrGizgpgq2TWEOzK1TyyTQ8=
  dependencies:
    path-exists "^2.0.0"
    pinkie-promise "^2.0.0"

find-up@^2.0.0, find-up@^2.1.0:
  version "2.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/find-up/-/find-up-2.1.0.tgz#45d1b7e506c717ddd482775a2b77920a3c0c57a7"
  integrity sha1-RdG35QbHF93UgndaK3eSCjwMV6c=
  dependencies:
    locate-path "^2.0.0"

find-up@^3.0.0:
  version "3.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/find-up/-/find-up-3.0.0.tgz#49169f1d7993430646da61ecc5ae355c21c97b73"
  integrity sha1-SRafHXmTQwZG2mHsxa41XCHJe3M=
  dependencies:
    locate-path "^3.0.0"

findup-sync@3.0.0:
  version "3.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/findup-sync/-/findup-sync-3.0.0.tgz#17b108f9ee512dfb7a5c7f3c8b27ea9e1a9c08d1"
  integrity sha1-F7EI+e5RLft6XH88iyfqnhqcCNE=
  dependencies:
    detect-file "^1.0.0"
    is-glob "^4.0.0"
    micromatch "^3.0.4"
    resolve-dir "^1.0.1"

flat-cache@^2.0.1:
  version "2.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/flat-cache/-/flat-cache-2.0.1.tgz#5d296d6f04bda44a4630a301413bdbc2ec085ec0"
  integrity sha1-XSltbwS9pEpGMKMBQTvbwuwIXsA=
  dependencies:
    flatted "^2.0.0"
    rimraf "2.6.3"
    write "1.0.3"

flatted@^2.0.0:
  version "2.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/flatted/-/flatted-2.0.1.tgz#69e57caa8f0eacbc281d2e2cb458d46fdb449e08"
  integrity sha1-aeV8qo8OrLwoHS4stFjUb9tEngg=

flush-write-stream@^1.0.0:
  version "1.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/flush-write-stream/-/flush-write-stream-1.1.1.tgz#8dd7d873a1babc207d94ead0c2e0e44276ebf2e8"
  integrity sha1-jdfYc6G6vCB9lOrQwuDkQnbr8ug=
  dependencies:
    inherits "^2.0.3"
    readable-stream "^2.3.6"

follow-redirects@1.5.10:
  version "1.5.10"
  resolved "https://nexus.lanmaoly.com/repository/npm/follow-redirects/-/follow-redirects-1.5.10.tgz#7b7a9f9aea2fdff36786a94ff643ed07f4ff5e2a"
  integrity sha1-e3qfmuov3/NnhqlP9kPtB/T/Xio=
  dependencies:
    debug "=3.1.0"

follow-redirects@^1.0.0:
  version "1.7.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/follow-redirects/-/follow-redirects-1.7.0.tgz#489ebc198dc0e7f64167bd23b03c4c19b5784c76"
  integrity sha1-SJ68GY3A5/ZBZ70jsDxMGbV4THY=
  dependencies:
    debug "^3.2.6"

follow-redirects@^1.2.3:
  version "1.10.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/follow-redirects/-/follow-redirects-1.10.0.tgz#01f5263aee921c6a54fb91667f08f4155ce169eb"
  integrity sha1-AfUmOu6SHGpU+5Fmfwj0FVzhaes=
  dependencies:
    debug "^3.0.0"

for-in@^0.1.3:
  version "0.1.8"
  resolved "https://nexus.lanmaoly.com/repository/npm/for-in/-/for-in-0.1.8.tgz#d8773908e31256109952b1fdb9b3fa867d2775e1"
  integrity sha1-2Hc5COMSVhCZUrH9ubP6hn0ndeE=

for-in@^1.0.1, for-in@^1.0.2:
  version "1.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/for-in/-/for-in-1.0.2.tgz#81068d295a8142ec0ac726c6e2200c30fb6d5e80"
  integrity sha1-gQaNKVqBQuwKxybG4iAMMPttXoA=

for-own@^0.1.4:
  version "0.1.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/for-own/-/for-own-0.1.5.tgz#5265c681a4f294dabbf17c9509b6763aa84510ce"
  integrity sha1-UmXGgaTylNq78XyVCbZ2OqhFEM4=
  dependencies:
    for-in "^1.0.1"

for-own@^1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/for-own/-/for-own-1.0.0.tgz#c63332f415cedc4b04dbfe70cf836494c53cb44b"
  integrity sha1-xjMy9BXO3EsE2/5wz4NklMU8tEs=
  dependencies:
    for-in "^1.0.1"

forever-agent@~0.6.1:
  version "0.6.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/forever-agent/-/forever-agent-0.6.1.tgz#fbc71f0c41adeb37f96c577ad1ed42d8fdacca91"
  integrity sha1-+8cfDEGt6zf5bFd60e1C2P2sypE=

form-data@~2.3.2:
  version "2.3.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/form-data/-/form-data-2.3.3.tgz#dcce52c05f644f298c6a7ab936bd724ceffbf3a6"
  integrity sha1-3M5SwF9kTymManq5Nr1yTO/786Y=
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.6"
    mime-types "^2.1.12"

forwarded@~0.1.2:
  version "0.1.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/forwarded/-/forwarded-0.1.2.tgz#98c23dab1175657b8c0573e8ceccd91b0ff18c84"
  integrity sha1-mMI9qxF1ZXuMBXPozszZGw/xjIQ=

fragment-cache@^0.2.1:
  version "0.2.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/fragment-cache/-/fragment-cache-0.2.1.tgz#4290fad27f13e89be7f33799c6bc5a0abfff0d19"
  integrity sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk=
  dependencies:
    map-cache "^0.2.2"

fresh@0.5.2:
  version "0.5.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/fresh/-/fresh-0.5.2.tgz#3d8cadd90d976569fa835ab1f8e4b23a105605a7"
  integrity sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=

friendly-errors-webpack-plugin@^1.7.0:
  version "1.7.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/friendly-errors-webpack-plugin/-/friendly-errors-webpack-plugin-1.7.0.tgz#efc86cbb816224565861a1be7a9d84d0aafea136"
  integrity sha1-78hsu4FiJFZYYaG+ep2E0Kr+oTY=
  dependencies:
    chalk "^1.1.3"
    error-stack-parser "^2.0.0"
    string-width "^2.0.0"

from2@^2.1.0:
  version "2.3.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/from2/-/from2-2.3.0.tgz#8bfb5502bde4a4d36cfdeea007fcca21d7e382af"
  integrity sha1-i/tVAr3kpNNs/e6gB/zKIdfjgq8=
  dependencies:
    inherits "^2.0.1"
    readable-stream "^2.0.0"

fs-extra@^7.0.0:
  version "7.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/fs-extra/-/fs-extra-7.0.1.tgz#4f189c44aa123b895f722804f55ea23eadc348e9"
  integrity sha1-TxicRKoSO4lfcigE9V6iPq3DSOk=
  dependencies:
    graceful-fs "^4.1.2"
    jsonfile "^4.0.0"
    universalify "^0.1.0"

fs-minipass@^1.2.5:
  version "1.2.6"
  resolved "https://nexus.lanmaoly.com/repository/npm/fs-minipass/-/fs-minipass-1.2.6.tgz#2c5cc30ded81282bfe8a0d7c7c1853ddeb102c07"
  integrity sha1-LFzDDe2BKCv+ig18fBhT3esQLAc=
  dependencies:
    minipass "^2.2.1"

fs-write-stream-atomic@^1.0.8:
  version "1.0.10"
  resolved "https://nexus.lanmaoly.com/repository/npm/fs-write-stream-atomic/-/fs-write-stream-atomic-1.0.10.tgz#b47df53493ef911df75731e70a9ded0189db40c9"
  integrity sha1-tH31NJPvkR33VzHnCp3tAYnbQMk=
  dependencies:
    graceful-fs "^4.1.2"
    iferr "^0.1.5"
    imurmurhash "^0.1.4"
    readable-stream "1 || 2"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/fs.realpath/-/fs.realpath-1.0.0.tgz#1504ad2523158caa40db4a2787cb01411994ea4f"
  integrity sha1-FQStJSMVjKpA20onh8sBQRmU6k8=

fsevents@^1.2.3, fsevents@^1.2.7:
  version "1.2.9"
  resolved "https://nexus.lanmaoly.com/repository/npm/fsevents/-/fsevents-1.2.9.tgz#3f5ed66583ccd6f400b5a00db6f7e861363e388f"
  integrity sha1-P17WZYPM1vQAtaANtvfoYTY+OI8=
  dependencies:
    nan "^2.12.1"
    node-pre-gyp "^0.12.0"

fstream@^1.0.0, fstream@^1.0.12:
  version "1.0.12"
  resolved "https://nexus.lanmaoly.com/repository/npm/fstream/-/fstream-1.0.12.tgz#4e8ba8ee2d48be4f7d0de505455548eae5932045"
  integrity sha1-Touo7i1Ivk99DeUFRVVI6uWTIEU=
  dependencies:
    graceful-fs "^4.1.2"
    inherits "~2.0.0"
    mkdirp ">=0.5 0"
    rimraf "2"

function-bind@^1.0.2, function-bind@^1.1.1:
  version "1.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/function-bind/-/function-bind-1.1.1.tgz#a56899d3ea3c9bab874bb9773b7c5ede92f4895d"
  integrity sha1-pWiZ0+o8m6uHS7l3O3xe3pL0iV0=

functional-red-black-tree@^1.0.1:
  version "1.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/functional-red-black-tree/-/functional-red-black-tree-1.0.1.tgz#1b0ab3bd553b2a0d6399d29c0e3ea0b252078327"
  integrity sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc=

fundebug-javascript@^1.9.0:
  version "1.9.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/fundebug-javascript/-/fundebug-javascript-1.9.0.tgz#b7f70945089902125a19c0d9c2d2e75b9262b6ee"
  integrity sha1-t/cJRQiZAhJaGcDZwtLnW5Jitu4=

gauge@~2.7.3:
  version "2.7.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/gauge/-/gauge-2.7.4.tgz#2c03405c7538c39d7eb37b317022e325fb018bf7"
  integrity sha1-LANAXHU4w51+s3sxcCLjJfsBi/c=
  dependencies:
    aproba "^1.0.3"
    console-control-strings "^1.0.0"
    has-unicode "^2.0.0"
    object-assign "^4.1.0"
    signal-exit "^3.0.0"
    string-width "^1.0.1"
    strip-ansi "^3.0.1"
    wide-align "^1.1.0"

gaze@^1.0.0:
  version "1.1.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/gaze/-/gaze-1.1.3.tgz#c441733e13b927ac8c0ff0b4c3b033f28812924a"
  integrity sha1-xEFzPhO5J6yMD/C0w7Az8ogSkko=
  dependencies:
    globule "^1.0.0"

get-caller-file@^1.0.1:
  version "1.0.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/get-caller-file/-/get-caller-file-1.0.3.tgz#f978fa4c90d1dfe7ff2d6beda2a515e713bdcf4a"
  integrity sha1-+Xj6TJDR3+f/LWvtoqUV5xO9z0o=

get-caller-file@^2.0.1:
  version "2.0.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/get-caller-file/-/get-caller-file-2.0.5.tgz#4f94412a82db32f36e3b0b9741f8a97feb031f7e"
  integrity sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=

get-stdin@^4.0.1:
  version "4.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/get-stdin/-/get-stdin-4.0.1.tgz#b968c6b0a04384324902e8bf1a5df32579a450fe"
  integrity sha1-uWjGsKBDhDJJAui/Gl3zJXmkUP4=

get-stream@^3.0.0:
  version "3.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/get-stream/-/get-stream-3.0.0.tgz#8e943d1358dc37555054ecbe2edb05aa174ede14"
  integrity sha1-jpQ9E1jcN1VQVOy+LtsFqhdO3hQ=

get-stream@^4.0.0:
  version "4.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/get-stream/-/get-stream-4.1.0.tgz#c1b255575f3dc21d59bfc79cd3d2b46b1c3a54b5"
  integrity sha1-wbJVV189wh1Zv8ec09K0axw6VLU=
  dependencies:
    pump "^3.0.0"

get-value@^2.0.3, get-value@^2.0.6:
  version "2.0.6"
  resolved "https://nexus.lanmaoly.com/repository/npm/get-value/-/get-value-2.0.6.tgz#dc15ca1c672387ca76bd37ac0a395ba2042a2c28"
  integrity sha1-3BXKHGcjh8p2vTesCjlbogQqLCg=

getpass@^0.1.1:
  version "0.1.7"
  resolved "https://nexus.lanmaoly.com/repository/npm/getpass/-/getpass-0.1.7.tgz#5eff8e3e684d569ae4cb2b1282604e8ba62149fa"
  integrity sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo=
  dependencies:
    assert-plus "^1.0.0"

glob-base@^0.3.0:
  version "0.3.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/glob-base/-/glob-base-0.3.0.tgz#dbb164f6221b1c0b1ccf82aea328b497df0ea3c4"
  integrity sha1-27Fk9iIbHAscz4Kuoyi0l98Oo8Q=
  dependencies:
    glob-parent "^2.0.0"
    is-glob "^2.0.0"

glob-parent@^2.0.0:
  version "2.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/glob-parent/-/glob-parent-2.0.0.tgz#81383d72db054fcccf5336daa902f182f6edbb28"
  integrity sha1-gTg9ctsFT8zPUzbaqQLxgvbtuyg=
  dependencies:
    is-glob "^2.0.0"

glob-parent@^3.1.0:
  version "3.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/glob-parent/-/glob-parent-3.1.0.tgz#9e6af6299d8d3bd2bd40430832bd113df906c5ae"
  integrity sha1-nmr2KZ2NO9K9QEMIMr0RPfkGxa4=
  dependencies:
    is-glob "^3.1.0"
    path-dirname "^1.0.0"

glob@^7.0.0, glob@^7.0.3, glob@^7.1.1, glob@^7.1.2, glob@^7.1.3, glob@^7.1.4, glob@~7.1.1:
  version "7.1.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/glob/-/glob-7.1.4.tgz#aa608a2f6c577ad357e1ae5a5c26d9a8d1969255"
  integrity sha1-qmCKL2xXetNX4a5aXCbZqNGWklU=
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.4"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

global-modules@2.0.0:
  version "2.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/global-modules/-/global-modules-2.0.0.tgz#997605ad2345f27f51539bea26574421215c7780"
  integrity sha1-mXYFrSNF8n9RU5vqJldEISFcd4A=
  dependencies:
    global-prefix "^3.0.0"

global-modules@^1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/global-modules/-/global-modules-1.0.0.tgz#6d770f0eb523ac78164d72b5e71a8877265cc3ea"
  integrity sha1-bXcPDrUjrHgWTXK15xqIdyZcw+o=
  dependencies:
    global-prefix "^1.0.1"
    is-windows "^1.0.1"
    resolve-dir "^1.0.0"

global-prefix@^1.0.1:
  version "1.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/global-prefix/-/global-prefix-1.0.2.tgz#dbf743c6c14992593c655568cb66ed32c0122ebe"
  integrity sha1-2/dDxsFJklk8ZVVoy2btMsASLr4=
  dependencies:
    expand-tilde "^2.0.2"
    homedir-polyfill "^1.0.1"
    ini "^1.3.4"
    is-windows "^1.0.1"
    which "^1.2.14"

global-prefix@^3.0.0:
  version "3.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/global-prefix/-/global-prefix-3.0.0.tgz#fc85f73064df69f50421f47f883fe5b913ba9b97"
  integrity sha1-/IX3MGTfafUEIfR/iD/luRO6m5c=
  dependencies:
    ini "^1.3.5"
    kind-of "^6.0.2"
    which "^1.3.1"

global@^4.3.0:
  version "4.4.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/global/-/global-4.4.0.tgz#3e7b105179006a323ed71aafca3e9c57a5cc6406"
  integrity sha1-PnsQUXkAajI+1xqvyj6cV6XMZAY=
  dependencies:
    min-document "^2.19.0"
    process "^0.11.10"

globals@^11.1.0, globals@^11.7.0:
  version "11.12.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/globals/-/globals-11.12.0.tgz#ab8795338868a0babd8525758018c2a7eb95c42e"
  integrity sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=

globals@^9.18.0:
  version "9.18.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/globals/-/globals-9.18.0.tgz#aa3896b3e69b487f17e31ed2143d69a8e30c2d8a"
  integrity sha1-qjiWs+abSH8X4x7SFD1pqOMMLYo=

globby@^6.1.0:
  version "6.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/globby/-/globby-6.1.0.tgz#f5a6d70e8395e21c858fb0489d64df02424d506c"
  integrity sha1-9abXDoOV4hyFj7BInWTfAkJNUGw=
  dependencies:
    array-union "^1.0.1"
    glob "^7.0.3"
    object-assign "^4.0.1"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"

globby@^7.1.1:
  version "7.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/globby/-/globby-7.1.1.tgz#fb2ccff9401f8600945dfada97440cca972b8680"
  integrity sha1-+yzP+UAfhgCUXfral0QMypcrhoA=
  dependencies:
    array-union "^1.0.1"
    dir-glob "^2.0.0"
    glob "^7.1.2"
    ignore "^3.3.5"
    pify "^3.0.0"
    slash "^1.0.0"

globule@^1.0.0:
  version "1.2.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/globule/-/globule-1.2.1.tgz#5dffb1b191f22d20797a9369b49eab4e9839696d"
  integrity sha1-Xf+xsZHyLSB5epNptJ6rTpg5aW0=
  dependencies:
    glob "~7.1.1"
    lodash "~4.17.10"
    minimatch "~3.0.2"

graceful-fs@4.1.4:
  version "4.1.4"
  resolved "https://r.cnpmjs.org/graceful-fs/download/graceful-fs-4.1.4.tgz#ef089d2880f033b011823ce5c8fae798da775dbd"
  integrity sha1-7widKIDwM7ARgjzlyPrnmNp3Xb0=

graceful-fs@^4.1.11, graceful-fs@^4.1.15, graceful-fs@^4.1.2, graceful-fs@^4.1.6:
  version "4.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/graceful-fs/-/graceful-fs-4.2.0.tgz#8d8fdc73977cb04104721cb53666c1ca64cd328b"
  integrity sha1-jY/cc5d8sEEEchy1NmbBymTNMos=

growly@^1.3.0:
  version "1.3.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/growly/-/growly-1.3.0.tgz#f10748cbe76af964b7c96c93c6bcc28af120c081"
  integrity sha1-8QdIy+dq+WS3yWyTxrzCivEgwIE=

gzip-size@^5.0.0:
  version "5.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/gzip-size/-/gzip-size-5.1.1.tgz#cb9bee692f87c0612b232840a873904e4c135274"
  integrity sha1-y5vuaS+HwGErIyhAqHOQTkwTUnQ=
  dependencies:
    duplexer "^0.1.1"
    pify "^4.0.1"

handle-thing@^2.0.0:
  version "2.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/handle-thing/-/handle-thing-2.0.0.tgz#0e039695ff50c93fc288557d696f3c1dc6776754"
  integrity sha1-DgOWlf9QyT/CiFV9aW88HcZ3Z1Q=

handlebars@^4.0.3:
  version "4.1.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/handlebars/-/handlebars-4.1.2.tgz#b6b37c1ced0306b221e094fc7aca3ec23b131b67"
  integrity sha1-trN8HO0DBrIh4JT8eso+wjsTG2c=
  dependencies:
    neo-async "^2.6.0"
    optimist "^0.6.1"
    source-map "^0.6.1"
  optionalDependencies:
    uglify-js "^3.1.4"

har-schema@^2.0.0:
  version "2.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/har-schema/-/har-schema-2.0.0.tgz#a94c2224ebcac04782a0d9035521f24735b7ec92"
  integrity sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI=

har-validator@~5.1.0:
  version "5.1.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/har-validator/-/har-validator-5.1.3.tgz#1ef89ebd3e4996557675eed9893110dc350fa080"
  integrity sha1-HvievT5JllV2de7ZiTEQ3DUPoIA=
  dependencies:
    ajv "^6.5.5"
    har-schema "^2.0.0"

has-ansi@^2.0.0:
  version "2.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/has-ansi/-/has-ansi-2.0.0.tgz#34f5049ce1ecdf2b0649af3ef24e45ed35416d91"
  integrity sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE=
  dependencies:
    ansi-regex "^2.0.0"

has-flag@^1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/has-flag/-/has-flag-1.0.0.tgz#9d9e793165ce017a00f00418c43f942a7b1d11fa"
  integrity sha1-nZ55MWXOAXoA8AQYxD+UKnsdEfo=

has-flag@^3.0.0:
  version "3.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/has-flag/-/has-flag-3.0.0.tgz#b5d454dc2199ae225699f3467e5a07f3b955bafd"
  integrity sha1-tdRU3CGZriJWmfNGfloH87lVuv0=

has-symbols@^1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/has-symbols/-/has-symbols-1.0.0.tgz#ba1a8f1af2a0fc39650f5c850367704122063b44"
  integrity sha1-uhqPGvKg/DllD1yFA2dwQSIGO0Q=

has-unicode@^2.0.0:
  version "2.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/has-unicode/-/has-unicode-2.0.1.tgz#e0e6fe6a28cf51138855e086d1691e771de2a8b9"
  integrity sha1-4Ob+aijPUROIVeCG0Wkedx3iqLk=

has-value@^0.3.1:
  version "0.3.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/has-value/-/has-value-0.3.1.tgz#7b1f58bada62ca827ec0a2078025654845995e1f"
  integrity sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=
  dependencies:
    get-value "^2.0.3"
    has-values "^0.1.4"
    isobject "^2.0.0"

has-value@^1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/has-value/-/has-value-1.0.0.tgz#18b281da585b1c5c51def24c930ed29a0be6b177"
  integrity sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc=
  dependencies:
    get-value "^2.0.6"
    has-values "^1.0.0"
    isobject "^3.0.0"

has-values@^0.1.4:
  version "0.1.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/has-values/-/has-values-0.1.4.tgz#6d61de95d91dfca9b9a02089ad384bff8f62b771"
  integrity sha1-bWHeldkd/Km5oCCJrThL/49it3E=

has-values@^1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/has-values/-/has-values-1.0.0.tgz#95b0b63fec2146619a6fe57fe75628d5a39efe4f"
  integrity sha1-lbC2P+whRmGab+V/51Yo1aOe/k8=
  dependencies:
    is-number "^3.0.0"
    kind-of "^4.0.0"

has@^1.0.1, has@^1.0.3:
  version "1.0.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/has/-/has-1.0.3.tgz#722d7cbfc1f6aa8241f16dd814e011e1f41e8796"
  integrity sha1-ci18v8H2qoJB8W3YFOAR4fQeh5Y=
  dependencies:
    function-bind "^1.1.1"

hash-base@^3.0.0:
  version "3.0.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/hash-base/-/hash-base-3.0.4.tgz#5fc8686847ecd73499403319a6b0a3f3f6ae4918"
  integrity sha1-X8hoaEfs1zSZQDMZprCj8/auSRg=
  dependencies:
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

hash.js@^1.0.0, hash.js@^1.0.3:
  version "1.1.7"
  resolved "https://nexus.lanmaoly.com/repository/npm/hash.js/-/hash.js-1.1.7.tgz#0babca538e8d4ee4a0f8988d68866537a003cf42"
  integrity sha1-C6vKU46NTuSg+JiNaIZlN6ADz0I=
  dependencies:
    inherits "^2.0.3"
    minimalistic-assert "^1.0.1"

he@1.2.x:
  version "1.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/he/-/he-1.2.0.tgz#84ae65fa7eafb165fddb61566ae14baf05664f0f"
  integrity sha1-hK5l+n6vsWX922FWauFLrwVmTw8=

history@^4.6.3, history@^4.7.2:
  version "4.9.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/history/-/history-4.9.0.tgz#84587c2068039ead8af769e9d6a6860a14fa1bca"
  integrity sha1-hFh8IGgDnq2K92np1qaGChT6G8o=
  dependencies:
    "@babel/runtime" "^7.1.2"
    loose-envify "^1.2.0"
    resolve-pathname "^2.2.0"
    tiny-invariant "^1.0.2"
    tiny-warning "^1.0.0"
    value-equal "^0.4.0"

hmac-drbg@^1.0.0:
  version "1.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/hmac-drbg/-/hmac-drbg-1.0.1.tgz#d2745701025a6c775a6c545793ed502fc0c649a1"
  integrity sha1-0nRXAQJabHdabFRXk+1QL8DGSaE=
  dependencies:
    hash.js "^1.0.3"
    minimalistic-assert "^1.0.0"
    minimalistic-crypto-utils "^1.0.1"

hoist-non-react-statics@^2.5.0, hoist-non-react-statics@^2.5.5:
  version "2.5.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/hoist-non-react-statics/-/hoist-non-react-statics-2.5.5.tgz#c5903cf409c0dfd908f388e619d86b9c1174cb47"
  integrity sha1-xZA89AnA39kI84jmGdhrnBF0y0c=

hoist-non-react-statics@^3.1.0:
  version "3.3.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/hoist-non-react-statics/-/hoist-non-react-statics-3.3.0.tgz#b09178f0122184fb95acf525daaecb4d8f45958b"
  integrity sha1-sJF48BIhhPuVrPUl2q7LTY9FlYs=
  dependencies:
    react-is "^16.7.0"

hoist-non-react-statics@^3.3.0:
  version "3.3.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/hoist-non-react-statics/-/hoist-non-react-statics-3.3.2.tgz#ece0acaf71d62c2969c2ec59feff42a4b1a85b45"
  integrity sha1-7OCsr3HWLClpwuxZ/v9CpLGoW0U=
  dependencies:
    react-is "^16.7.0"

home-or-tmp@^2.0.0:
  version "2.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/home-or-tmp/-/home-or-tmp-2.0.0.tgz#e36c3f2d2cae7d746a857e38d18d5f32a7882db8"
  integrity sha1-42w/LSyufXRqhX440Y1fMqeILbg=
  dependencies:
    os-homedir "^1.0.0"
    os-tmpdir "^1.0.1"

homedir-polyfill@^1.0.1:
  version "1.0.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/homedir-polyfill/-/homedir-polyfill-1.0.3.tgz#743298cef4e5af3e194161fbadcc2151d3a058e8"
  integrity sha1-dDKYzvTlrz4ZQWH7rcwhUdOgWOg=
  dependencies:
    parse-passwd "^1.0.0"

hoopy@^0.1.4:
  version "0.1.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/hoopy/-/hoopy-0.1.4.tgz#609207d661100033a9a9402ad3dea677381c1b1d"
  integrity sha1-YJIH1mEQADOpqUAq096mdzgcGx0=

hosted-git-info@^2.1.4:
  version "2.7.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/hosted-git-info/-/hosted-git-info-2.7.1.tgz#97f236977bd6e125408930ff6de3eec6281ec047"
  integrity sha1-l/I2l3vW4SVAiTD/bePuxigewEc=

hpack.js@^2.1.6:
  version "2.1.6"
  resolved "https://nexus.lanmaoly.com/repository/npm/hpack.js/-/hpack.js-2.1.6.tgz#87774c0949e513f42e84575b3c45681fade2a0b2"
  integrity sha1-h3dMCUnlE/QuhFdbPEVoH63ioLI=
  dependencies:
    inherits "^2.0.1"
    obuf "^1.0.0"
    readable-stream "^2.0.1"
    wbuf "^1.1.0"

html-encoding-sniffer@^1.0.2:
  version "1.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/html-encoding-sniffer/-/html-encoding-sniffer-1.0.2.tgz#e70d84b94da53aa375e11fe3a351be6642ca46f8"
  integrity sha1-5w2EuU2lOqN14R/jo1G+ZkLKRvg=
  dependencies:
    whatwg-encoding "^1.0.1"

html-entities@^1.2.1:
  version "1.2.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/html-entities/-/html-entities-1.2.1.tgz#0df29351f0721163515dfb9e5543e5f6eed5162f"
  integrity sha1-DfKTUfByEWNRXfueVUPl9u7VFi8=

html-minifier@^3.2.3:
  version "3.5.21"
  resolved "https://nexus.lanmaoly.com/repository/npm/html-minifier/-/html-minifier-3.5.21.tgz#d0040e054730e354db008463593194015212d20c"
  integrity sha1-0AQOBUcw41TbAIRjWTGUAVIS0gw=
  dependencies:
    camel-case "3.0.x"
    clean-css "4.2.x"
    commander "2.17.x"
    he "1.2.x"
    param-case "2.1.x"
    relateurl "0.2.x"
    uglify-js "3.4.x"

html-webpack-plugin@^3.2.0:
  version "3.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/html-webpack-plugin/-/html-webpack-plugin-3.2.0.tgz#b01abbd723acaaa7b37b6af4492ebda03d9dd37b"
  integrity sha1-sBq71yOsqqeze2r0SS69oD2d03s=
  dependencies:
    html-minifier "^3.2.3"
    loader-utils "^0.2.16"
    lodash "^4.17.3"
    pretty-error "^2.0.2"
    tapable "^1.0.0"
    toposort "^1.0.0"
    util.promisify "1.0.0"

htmlparser2@^3.3.0:
  version "3.10.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/htmlparser2/-/htmlparser2-3.10.1.tgz#bd679dc3f59897b6a34bb10749c855bb53a9392f"
  integrity sha1-vWedw/WYl7ajS7EHSchVu1OpOS8=
  dependencies:
    domelementtype "^1.3.1"
    domhandler "^2.3.0"
    domutils "^1.5.1"
    entities "^1.1.1"
    inherits "^2.0.1"
    readable-stream "^3.1.1"

http-deceiver@^1.2.7:
  version "1.2.7"
  resolved "https://nexus.lanmaoly.com/repository/npm/http-deceiver/-/http-deceiver-1.2.7.tgz#fa7168944ab9a519d337cb0bec7284dc3e723d87"
  integrity sha1-+nFolEq5pRnTN8sL7HKE3D5yPYc=

http-errors@1.7.2:
  version "1.7.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/http-errors/-/http-errors-1.7.2.tgz#4f5029cf13239f31036e5b2e55292bcfbcc85c8f"
  integrity sha1-T1ApzxMjnzEDblsuVSkrz7zIXI8=
  dependencies:
    depd "~1.1.2"
    inherits "2.0.3"
    setprototypeof "1.1.1"
    statuses ">= 1.5.0 < 2"
    toidentifier "1.0.0"

http-errors@~1.6.2:
  version "1.6.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/http-errors/-/http-errors-1.6.3.tgz#8b55680bb4be283a0b5bf4ea2e38580be1d9320d"
  integrity sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0=
  dependencies:
    depd "~1.1.2"
    inherits "2.0.3"
    setprototypeof "1.1.0"
    statuses ">= 1.4.0 < 2"

http-errors@~1.7.2:
  version "1.7.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/http-errors/-/http-errors-1.7.3.tgz#6c619e4f9c60308c38519498c14fbb10aacebb06"
  integrity sha1-bGGeT5xgMIw4UZSYwU+7EKrOuwY=
  dependencies:
    depd "~1.1.2"
    inherits "2.0.4"
    setprototypeof "1.1.1"
    statuses ">= 1.5.0 < 2"
    toidentifier "1.0.0"

"http-parser-js@>=0.4.0 <0.4.11":
  version "0.4.10"
  resolved "https://nexus.lanmaoly.com/repository/npm/http-parser-js/-/http-parser-js-0.4.10.tgz#92c9c1374c35085f75db359ec56cc257cbb93fa4"
  integrity sha1-ksnBN0w1CF912zWexWzCV8u5P6Q=

http-proxy-middleware@^0.19.1:
  version "0.19.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/http-proxy-middleware/-/http-proxy-middleware-0.19.1.tgz#183c7dc4aa1479150306498c210cdaf96080a43a"
  integrity sha1-GDx9xKoUeRUDBkmMIQza+WCApDo=
  dependencies:
    http-proxy "^1.17.0"
    is-glob "^4.0.0"
    lodash "^4.17.11"
    micromatch "^3.1.10"

http-proxy@^1.17.0:
  version "1.17.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/http-proxy/-/http-proxy-1.17.0.tgz#7ad38494658f84605e2f6db4436df410f4e5be9a"
  integrity sha1-etOElGWPhGBeL220Q230EPTlvpo=
  dependencies:
    eventemitter3 "^3.0.0"
    follow-redirects "^1.0.0"
    requires-port "^1.0.0"

http-signature@~1.2.0:
  version "1.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/http-signature/-/http-signature-1.2.0.tgz#9aecd925114772f3d95b65a60abb8f7c18fbace1"
  integrity sha1-muzZJRFHcvPZW2WmCruPfBj7rOE=
  dependencies:
    assert-plus "^1.0.0"
    jsprim "^1.2.2"
    sshpk "^1.7.0"

https-browserify@^1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/https-browserify/-/https-browserify-1.0.0.tgz#ec06c10e0a34c0f2faf199f7fd7fc78fffd03c73"
  integrity sha1-7AbBDgo0wPL68Zn3/X/Hj//QPHM=

iconv-lite@0.4.24, iconv-lite@^0.4.24, iconv-lite@^0.4.4, iconv-lite@~0.4.13:
  version "0.4.24"
  resolved "https://nexus.lanmaoly.com/repository/npm/iconv-lite/-/iconv-lite-0.4.24.tgz#2022b4b25fbddc21d2f524974a474aafe733908b"
  integrity sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

icss-replace-symbols@^1.1.0:
  version "1.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/icss-replace-symbols/-/icss-replace-symbols-1.1.0.tgz#06ea6f83679a7749e386cfe1fe812ae5db223ded"
  integrity sha1-Bupvg2ead0njhs/h/oEq5dsiPe0=

icss-utils@^2.1.0:
  version "2.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/icss-utils/-/icss-utils-2.1.0.tgz#83f0a0ec378bf3246178b6c2ad9136f135b1c962"
  integrity sha1-g/Cg7DeL8yRheLbCrZE28TWxyWI=
  dependencies:
    postcss "^6.0.1"

ieee754@^1.1.4:
  version "1.1.13"
  resolved "https://nexus.lanmaoly.com/repository/npm/ieee754/-/ieee754-1.1.13.tgz#ec168558e95aa181fd87d37f55c32bbcb6708b84"
  integrity sha1-7BaFWOlaoYH9h9N/VcMrvLZwi4Q=

iferr@^0.1.5:
  version "0.1.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/iferr/-/iferr-0.1.5.tgz#c60eed69e6d8fdb6b3104a1fcbca1c192dc5b501"
  integrity sha1-xg7taebY/bazEEofy8ocGS3FtQE=

ignore-walk@^3.0.1:
  version "3.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/ignore-walk/-/ignore-walk-3.0.1.tgz#a83e62e7d272ac0e3b551aaa82831a19b69f82f8"
  integrity sha1-qD5i59JyrA47VRqqgoMaGbafgvg=
  dependencies:
    minimatch "^3.0.4"

ignore@^3.3.5:
  version "3.3.10"
  resolved "https://nexus.lanmaoly.com/repository/npm/ignore/-/ignore-3.3.10.tgz#0a97fb876986e8081c631160f8f9f389157f0043"
  integrity sha1-Cpf7h2mG6AgcYxFg+PnziRV/AEM=

ignore@^4.0.6:
  version "4.0.6"
  resolved "https://nexus.lanmaoly.com/repository/npm/ignore/-/ignore-4.0.6.tgz#750e3db5862087b4737ebac8207ffd1ef27b25fc"
  integrity sha1-dQ49tYYgh7RzfrrIIH/9HvJ7Jfw=

image-size@~0.5.0:
  version "0.5.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/image-size/-/image-size-0.5.5.tgz#09dfd4ab9d20e29eb1c3e80b8990378df9e3cb9c"
  integrity sha1-Cd/Uq50g4p6xw+gLiZA3jfnjy5w=

immutable@^3.8.1:
  version "3.8.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/immutable/-/immutable-3.8.2.tgz#c2439951455bb39913daf281376f1530e104adf3"
  integrity sha1-wkOZUUVbs5kT2vKBN28VMOEErfM=

import-cwd@^2.0.0:
  version "2.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/import-cwd/-/import-cwd-2.1.0.tgz#aa6cf36e722761285cb371ec6519f53e2435b0a9"
  integrity sha1-qmzzbnInYShcs3HsZRn1PiQ1sKk=
  dependencies:
    import-from "^2.1.0"

import-fresh@^2.0.0:
  version "2.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/import-fresh/-/import-fresh-2.0.0.tgz#d81355c15612d386c61f9ddd3922d4304822a546"
  integrity sha1-2BNVwVYS04bGH53dOSLUMEgipUY=
  dependencies:
    caller-path "^2.0.0"
    resolve-from "^3.0.0"

import-fresh@^3.0.0:
  version "3.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/import-fresh/-/import-fresh-3.1.0.tgz#6d33fa1dcef6df930fae003446f33415af905118"
  integrity sha1-bTP6Hc7235MPrgA0RvM0Fa+QURg=
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

import-from@^2.1.0:
  version "2.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/import-from/-/import-from-2.1.0.tgz#335db7f2a7affd53aaa471d4b8021dee36b7f3b1"
  integrity sha1-M1238qev/VOqpHHUuAId7ja387E=
  dependencies:
    resolve-from "^3.0.0"

import-local@2.0.0, import-local@^2.0.0:
  version "2.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/import-local/-/import-local-2.0.0.tgz#55070be38a5993cf18ef6db7e961f5bee5c5a09d"
  integrity sha1-VQcL44pZk88Y72236WH1vuXFoJ0=
  dependencies:
    pkg-dir "^3.0.0"
    resolve-cwd "^2.0.0"

import-local@^1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/import-local/-/import-local-1.0.0.tgz#5e4ffdc03f4fe6c009c6729beb29631c2f8227bc"
  integrity sha1-Xk/9wD9P5sAJxnKb6yljHC+CJ7w=
  dependencies:
    pkg-dir "^2.0.0"
    resolve-cwd "^2.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/imurmurhash/-/imurmurhash-0.1.4.tgz#9218b9b2b928a238b13dc4fb6b6d576f231453ea"
  integrity sha1-khi5srkoojixPcT7a21XbyMUU+o=

in-publish@^2.0.0:
  version "2.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/in-publish/-/in-publish-2.0.0.tgz#e20ff5e3a2afc2690320b6dc552682a9c7fadf51"
  integrity sha1-4g/146KvwmkDILbcVSaCqcf631E=

indent-string@^2.1.0:
  version "2.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/indent-string/-/indent-string-2.1.0.tgz#8e2d48348742121b4a8218b7a137e9a52049dc80"
  integrity sha1-ji1INIdCEhtKghi3oTfppSBJ3IA=
  dependencies:
    repeating "^2.0.0"

infer-owner@^1.0.3:
  version "1.0.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/infer-owner/-/infer-owner-1.0.4.tgz#c4cefcaa8e51051c2a40ba2ce8a3d27295af9467"
  integrity sha1-xM78qo5RBRwqQLos6KPScpWvlGc=

inflight@^1.0.4:
  version "1.0.6"
  resolved "https://nexus.lanmaoly.com/repository/npm/inflight/-/inflight-1.0.6.tgz#49bd6331d7d02d0c09bc910a1075ba8165b56df9"
  integrity sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@2.0.4, inherits@^2.0.1, inherits@^2.0.3, inherits@~2.0.0, inherits@~2.0.1, inherits@~2.0.3:
  version "2.0.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/inherits/-/inherits-2.0.4.tgz#0fa2c64f932917c3433a0ded55363aae37416b7c"
  integrity sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=

inherits@2.0.1:
  version "2.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/inherits/-/inherits-2.0.1.tgz#b17d08d326b4423e568eff719f91b0b1cbdf69f1"
  integrity sha1-sX0I0ya0Qj5Wjv9xn5GwscvfafE=

inherits@2.0.3:
  version "2.0.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/inherits/-/inherits-2.0.3.tgz#633c2c83e3da42a502f52466022480f4208261de"
  integrity sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=

ini@^1.3.4, ini@^1.3.5, ini@~1.3.0:
  version "1.3.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/ini/-/ini-1.3.5.tgz#eee25f56db1c9ec6085e0c22778083f596abf927"
  integrity sha1-7uJfVtscnsYIXgwid4CD9Zar+Sc=

inquirer@^6.2.2:
  version "6.5.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/inquirer/-/inquirer-6.5.0.tgz#2303317efc9a4ea7ec2e2df6f86569b734accf42"
  integrity sha1-IwMxfvyaTqfsLi32+GVptzSsz0I=
  dependencies:
    ansi-escapes "^3.2.0"
    chalk "^2.4.2"
    cli-cursor "^2.1.0"
    cli-width "^2.0.0"
    external-editor "^3.0.3"
    figures "^2.0.0"
    lodash "^4.17.12"
    mute-stream "0.0.7"
    run-async "^2.2.0"
    rxjs "^6.4.0"
    string-width "^2.1.0"
    strip-ansi "^5.1.0"
    through "^2.3.6"

internal-ip@^4.3.0:
  version "4.3.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/internal-ip/-/internal-ip-4.3.0.tgz#845452baad9d2ca3b69c635a137acb9a0dad0907"
  integrity sha1-hFRSuq2dLKO2nGNaE3rLmg2tCQc=
  dependencies:
    default-gateway "^4.2.0"
    ipaddr.js "^1.9.0"

interpret@1.2.0:
  version "1.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/interpret/-/interpret-1.2.0.tgz#d5061a6224be58e8083985f5014d844359576296"
  integrity sha1-1QYaYiS+WOgIOYX1AU2EQ1lXYpY=

invariant@^2.2.2, invariant@^2.2.4:
  version "2.2.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/invariant/-/invariant-2.2.4.tgz#610f3c92c9359ce1db616e538008d23ff35158e6"
  integrity sha1-YQ88ksk1nOHbYW5TgAjSP/NRWOY=
  dependencies:
    loose-envify "^1.0.0"

invert-kv@^1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/invert-kv/-/invert-kv-1.0.0.tgz#104a8e4aaca6d3d8cd157a8ef8bfab2d7a3ffdb6"
  integrity sha1-EEqOSqym09jNFXqO+L+rLXo//bY=

invert-kv@^2.0.0:
  version "2.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/invert-kv/-/invert-kv-2.0.0.tgz#7393f5afa59ec9ff5f67a27620d11c226e3eec02"
  integrity sha1-c5P1r6Weyf9fZ6J2INEcIm4+7AI=

ip-regex@^2.1.0:
  version "2.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/ip-regex/-/ip-regex-2.1.0.tgz#fa78bf5d2e6913c911ce9f819ee5146bb6d844e9"
  integrity sha1-+ni/XS5pE8kRzp+BnuUUa7bYROk=

ip@^1.1.0, ip@^1.1.5:
  version "1.1.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/ip/-/ip-1.1.5.tgz#bdded70114290828c0a039e72ef25f5aaec4354a"
  integrity sha1-vd7XARQpCCjAoDnnLvJfWq7ENUo=

ipaddr.js@1.9.0:
  version "1.9.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/ipaddr.js/-/ipaddr.js-1.9.0.tgz#37df74e430a0e47550fe54a2defe30d8acd95f65"
  integrity sha1-N9905DCg5HVQ/lSi3v4w2KzZX2U=

ipaddr.js@^1.9.0:
  version "1.9.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/ipaddr.js/-/ipaddr.js-1.9.1.tgz#bff38543eeb8984825079ff3a2a8e6cbd46781b3"
  integrity sha1-v/OFQ+64mEglB5/zoqjmy9RngbM=

is-accessor-descriptor@^0.1.6:
  version "0.1.6"
  resolved "https://nexus.lanmaoly.com/repository/npm/is-accessor-descriptor/-/is-accessor-descriptor-0.1.6.tgz#a9e12cb3ae8d876727eeef3843f8a0897b5c98d6"
  integrity sha1-qeEss66Nh2cn7u84Q/igiXtcmNY=
  dependencies:
    kind-of "^3.0.2"

is-accessor-descriptor@^1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/is-accessor-descriptor/-/is-accessor-descriptor-1.0.0.tgz#169c2f6d3df1f992618072365c9b0ea1f6878656"
  integrity sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=
  dependencies:
    kind-of "^6.0.0"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/is-arrayish/-/is-arrayish-0.2.1.tgz#77c99840527aa8ecb1a8ba697b80645a7a926a9d"
  integrity sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=

is-binary-path@^1.0.0:
  version "1.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/is-binary-path/-/is-binary-path-1.0.1.tgz#75f16642b480f187a711c814161fd3a4a7655898"
  integrity sha1-dfFmQrSA8YenEcgUFh/TpKdlWJg=
  dependencies:
    binary-extensions "^1.0.0"

is-buffer@^1.1.5:
  version "1.1.6"
  resolved "https://nexus.lanmaoly.com/repository/npm/is-buffer/-/is-buffer-1.1.6.tgz#efaa2ea9daa0d7ab2ea13a97b2b8ad51fefbe8be"
  integrity sha1-76ouqdqg16suoTqXsritUf776L4=

is-buffer@^2.0.2:
  version "2.0.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/is-buffer/-/is-buffer-2.0.3.tgz#4ecf3fcf749cbd1e472689e109ac66261a25e725"
  integrity sha1-Ts8/z3ScvR5HJonhCaxmJhol5yU=

is-callable@^1.1.4:
  version "1.1.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/is-callable/-/is-callable-1.1.4.tgz#1e1adf219e1eeb684d691f9d6a05ff0d30a24d75"
  integrity sha1-HhrfIZ4e62hNaR+dagX/DTCiTXU=

is-ci@^1.0.10:
  version "1.2.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/is-ci/-/is-ci-1.2.1.tgz#e3779c8ee17fccf428488f6e281187f2e632841c"
  integrity sha1-43ecjuF/zPQoSI9uKBGH8uYyhBw=
  dependencies:
    ci-info "^1.5.0"

is-data-descriptor@^0.1.4:
  version "0.1.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/is-data-descriptor/-/is-data-descriptor-0.1.4.tgz#0b5ee648388e2c860282e793f1856fec3f301b56"
  integrity sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y=
  dependencies:
    kind-of "^3.0.2"

is-data-descriptor@^1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz#d84876321d0e7add03990406abbbbd36ba9268c7"
  integrity sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=
  dependencies:
    kind-of "^6.0.0"

is-date-object@^1.0.1:
  version "1.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/is-date-object/-/is-date-object-1.0.1.tgz#9aa20eb6aeebbff77fbd33e74ca01b33581d3a16"
  integrity sha1-mqIOtq7rv/d/vTPnTKAbM1gdOhY=

is-descriptor@^0.1.0:
  version "0.1.6"
  resolved "https://nexus.lanmaoly.com/repository/npm/is-descriptor/-/is-descriptor-0.1.6.tgz#366d8240dde487ca51823b1ab9f07a10a78251ca"
  integrity sha1-Nm2CQN3kh8pRgjsaufB6EKeCUco=
  dependencies:
    is-accessor-descriptor "^0.1.6"
    is-data-descriptor "^0.1.4"
    kind-of "^5.0.0"

is-descriptor@^1.0.0, is-descriptor@^1.0.2:
  version "1.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/is-descriptor/-/is-descriptor-1.0.2.tgz#3b159746a66604b04f8c81524ba365c5f14d86ec"
  integrity sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=
  dependencies:
    is-accessor-descriptor "^1.0.0"
    is-data-descriptor "^1.0.0"
    kind-of "^6.0.2"

is-directory@^0.3.1:
  version "0.3.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/is-directory/-/is-directory-0.3.1.tgz#61339b6f2475fc772fd9c9d83f5c8575dc154ae1"
  integrity sha1-YTObbyR1/Hcv2cnYP1yFddwVSuE=

is-dotfile@^1.0.0:
  version "1.0.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/is-dotfile/-/is-dotfile-1.0.3.tgz#a6a2f32ffd2dfb04f5ca25ecd0f6b83cf798a1e1"
  integrity sha1-pqLzL/0t+wT1yiXs0Pa4PPeYoeE=

is-equal-shallow@^0.1.3:
  version "0.1.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/is-equal-shallow/-/is-equal-shallow-0.1.3.tgz#2238098fc221de0bcfa5d9eac4c45d638aa1c534"
  integrity sha1-IjgJj8Ih3gvPpdnqxMRdY4qhxTQ=
  dependencies:
    is-primitive "^2.0.0"

is-extendable@^0.1.0, is-extendable@^0.1.1:
  version "0.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/is-extendable/-/is-extendable-0.1.1.tgz#62b110e289a471418e3ec36a617d472e301dfc89"
  integrity sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=

is-extendable@^1.0.1:
  version "1.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/is-extendable/-/is-extendable-1.0.1.tgz#a7470f9e426733d81bd81e1155264e3a3507cab4"
  integrity sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ=
  dependencies:
    is-plain-object "^2.0.4"

is-extglob@^1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/is-extglob/-/is-extglob-1.0.0.tgz#ac468177c4943405a092fc8f29760c6ffc6206c0"
  integrity sha1-rEaBd8SUNAWgkvyPKXYMb/xiBsA=

is-extglob@^2.1.0, is-extglob@^2.1.1:
  version "2.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/is-extglob/-/is-extglob-2.1.1.tgz#a88c02535791f02ed37c76a1b9ea9773c833f8c2"
  integrity sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=

is-finite@^1.0.0:
  version "1.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/is-finite/-/is-finite-1.0.2.tgz#cc6677695602be550ef11e8b4aa6305342b6d0aa"
  integrity sha1-zGZ3aVYCvlUO8R6LSqYwU0K20Ko=
  dependencies:
    number-is-nan "^1.0.0"

is-fullwidth-code-point@^1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/is-fullwidth-code-point/-/is-fullwidth-code-point-1.0.0.tgz#ef9e31386f031a7f0d643af82fde50c457ef00cb"
  integrity sha1-754xOG8DGn8NZDr4L95QxFfvAMs=
  dependencies:
    number-is-nan "^1.0.0"

is-fullwidth-code-point@^2.0.0:
  version "2.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/is-fullwidth-code-point/-/is-fullwidth-code-point-2.0.0.tgz#a3b30a5c4f199183167aaab93beefae3ddfb654f"
  integrity sha1-o7MKXE8ZkYMWeqq5O+764937ZU8=

is-generator-fn@^1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/is-generator-fn/-/is-generator-fn-1.0.0.tgz#969d49e1bb3329f6bb7f09089be26578b2ddd46a"
  integrity sha1-lp1J4bszKfa7fwkIm+JleLLd1Go=

is-glob@^2.0.0, is-glob@^2.0.1:
  version "2.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/is-glob/-/is-glob-2.0.1.tgz#d096f926a3ded5600f3fdfd91198cb0888c2d863"
  integrity sha1-0Jb5JqPe1WAPP9/ZEZjLCIjC2GM=
  dependencies:
    is-extglob "^1.0.0"

is-glob@^3.1.0:
  version "3.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/is-glob/-/is-glob-3.1.0.tgz#7ba5ae24217804ac70707b96922567486cc3e84a"
  integrity sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo=
  dependencies:
    is-extglob "^2.1.0"

is-glob@^4.0.0:
  version "4.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/is-glob/-/is-glob-4.0.1.tgz#7567dbe9f2f5e2467bc77ab83c4a29482407a5dc"
  integrity sha1-dWfb6fL14kZ7x3q4PEopSCQHpdw=
  dependencies:
    is-extglob "^2.1.1"

is-number@^2.1.0:
  version "2.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/is-number/-/is-number-2.1.0.tgz#01fcbbb393463a548f2f466cce16dece49db908f"
  integrity sha1-Afy7s5NGOlSPL0ZszhbezknbkI8=
  dependencies:
    kind-of "^3.0.2"

is-number@^3.0.0:
  version "3.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/is-number/-/is-number-3.0.0.tgz#24fd6201a4782cf50561c810276afc7d12d71195"
  integrity sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU=
  dependencies:
    kind-of "^3.0.2"

is-number@^4.0.0:
  version "4.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/is-number/-/is-number-4.0.0.tgz#0026e37f5454d73e356dfe6564699867c6a7f0ff"
  integrity sha1-ACbjf1RU1z41bf5lZGmYZ8an8P8=

is-path-cwd@^2.0.0:
  version "2.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/is-path-cwd/-/is-path-cwd-2.2.0.tgz#67d43b82664a7b5191fd9119127eb300048a9fdb"
  integrity sha1-Z9Q7gmZKe1GR/ZEZEn6zAASKn9s=

is-path-in-cwd@^2.0.0:
  version "2.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/is-path-in-cwd/-/is-path-in-cwd-2.1.0.tgz#bfe2dca26c69f397265a4009963602935a053acb"
  integrity sha1-v+Lcomxp85cmWkAJljYCk1oFOss=
  dependencies:
    is-path-inside "^2.1.0"

is-path-inside@^2.1.0:
  version "2.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/is-path-inside/-/is-path-inside-2.1.0.tgz#7c9810587d659a40d27bcdb4d5616eab059494b2"
  integrity sha1-fJgQWH1lmkDSe8201WFuqwWUlLI=
  dependencies:
    path-is-inside "^1.0.2"

is-plain-object@^2.0.3, is-plain-object@^2.0.4:
  version "2.0.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/is-plain-object/-/is-plain-object-2.0.4.tgz#2c163b3fafb1b606d9d17928f05c2a1c38e07677"
  integrity sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc=
  dependencies:
    isobject "^3.0.1"

is-posix-bracket@^0.1.0:
  version "0.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/is-posix-bracket/-/is-posix-bracket-0.1.1.tgz#3334dc79774368e92f016e6fbc0a88f5cd6e6bc4"
  integrity sha1-MzTceXdDaOkvAW5vvAqI9c1ua8Q=

is-primitive@^2.0.0:
  version "2.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/is-primitive/-/is-primitive-2.0.0.tgz#207bab91638499c07b2adf240a41a87210034575"
  integrity sha1-IHurkWOEmcB7Kt8kCkGochADRXU=

is-promise@^2.1.0:
  version "2.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/is-promise/-/is-promise-2.1.0.tgz#79a2a9ece7f096e80f36d2b2f3bc16c1ff4bf3fa"
  integrity sha1-eaKp7OfwlugPNtKy87wWwf9L8/o=

is-regex@^1.0.4:
  version "1.0.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/is-regex/-/is-regex-1.0.4.tgz#5517489b547091b0930e095654ced25ee97e9491"
  integrity sha1-VRdIm1RwkbCTDglWVM7SXul+lJE=
  dependencies:
    has "^1.0.1"

is-stream@^1.0.1, is-stream@^1.1.0:
  version "1.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/is-stream/-/is-stream-1.1.0.tgz#12d4a3dd4e68e0b79ceb8dbc84173ae80d91ca44"
  integrity sha1-EtSj3U5o4Lec6428hBc66A2RykQ=

is-symbol@^1.0.2:
  version "1.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/is-symbol/-/is-symbol-1.0.2.tgz#a055f6ae57192caee329e7a860118b497a950f38"
  integrity sha1-oFX2rlcZLK7jKeeoYBGLSXqVDzg=
  dependencies:
    has-symbols "^1.0.0"

is-typedarray@~1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/is-typedarray/-/is-typedarray-1.0.0.tgz#e479c80858df0c1b11ddda6940f96011fcda4a9a"
  integrity sha1-5HnICFjfDBsR3dppQPlgEfzaSpo=

is-utf8@^0.2.0:
  version "0.2.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/is-utf8/-/is-utf8-0.2.1.tgz#4b0da1442104d1b336340e80797e865cf39f7d72"
  integrity sha1-Sw2hRCEE0bM2NA6AeX6GXPOffXI=

is-what@^3.12.0:
  version "3.14.1"
  resolved "https://r.cnpmjs.org/is-what/download/is-what-3.14.1.tgz#e1222f46ddda85dead0fd1c9df131760e77755c1"
  integrity sha1-4SIvRt3ahd6tD9HJ3xMXYOd3VcE=

is-windows@^1.0.0, is-windows@^1.0.1, is-windows@^1.0.2:
  version "1.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/is-windows/-/is-windows-1.0.2.tgz#d1850eb9791ecd18e6182ce12a30f396634bb19d"
  integrity sha1-0YUOuXkezRjmGCzhKjDzlmNLsZ0=

is-wsl@^1.1.0:
  version "1.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/is-wsl/-/is-wsl-1.1.0.tgz#1f16e4aa22b04d1336b66188a66af3c600c3a66d"
  integrity sha1-HxbkqiKwTRM2tmGIpmrzxgDDpm0=

isarray@0.0.1:
  version "0.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/isarray/-/isarray-0.0.1.tgz#8a18acfca9a8f4177e09abfc6038939b05d1eedf"
  integrity sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8=

isarray@1.0.0, isarray@^1.0.0, isarray@~1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/isarray/-/isarray-1.0.0.tgz#bb935d48582cba168c06834957a54a3e07124f11"
  integrity sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=

isexe@^2.0.0:
  version "2.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/isexe/-/isexe-2.0.0.tgz#e8fbf374dc556ff8947a10dcb0572d633f2cfa10"
  integrity sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=

isobject@^2.0.0:
  version "2.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/isobject/-/isobject-2.1.0.tgz#f065561096a3f1da2ef46272f815c840d87e0c89"
  integrity sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=
  dependencies:
    isarray "1.0.0"

isobject@^3.0.0, isobject@^3.0.1:
  version "3.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/isobject/-/isobject-3.0.1.tgz#4e431e92b11a9731636aa1f9c8d1ccbcfdab78df"
  integrity sha1-TkMekrEalzFjaqH5yNHMvP2reN8=

isomorphic-fetch@^2.1.1:
  version "2.2.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/isomorphic-fetch/-/isomorphic-fetch-2.2.1.tgz#611ae1acf14f5e81f729507472819fe9733558a9"
  integrity sha1-YRrhrPFPXoH3KVB0coGf6XM1WKk=
  dependencies:
    node-fetch "^1.0.1"
    whatwg-fetch ">=0.10.0"

isstream@~0.1.2:
  version "0.1.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/isstream/-/isstream-0.1.2.tgz#47e63f7af55afa6f92e1500e690eb8b8529c099a"
  integrity sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo=

istanbul-api@^1.3.1:
  version "1.3.7"
  resolved "https://nexus.lanmaoly.com/repository/npm/istanbul-api/-/istanbul-api-1.3.7.tgz#a86c770d2b03e11e3f778cd7aedd82d2722092aa"
  integrity sha1-qGx3DSsD4R4/d4zXrt2C0nIgkqo=
  dependencies:
    async "^2.1.4"
    fileset "^2.0.2"
    istanbul-lib-coverage "^1.2.1"
    istanbul-lib-hook "^1.2.2"
    istanbul-lib-instrument "^1.10.2"
    istanbul-lib-report "^1.1.5"
    istanbul-lib-source-maps "^1.2.6"
    istanbul-reports "^1.5.1"
    js-yaml "^3.7.0"
    mkdirp "^0.5.1"
    once "^1.4.0"

istanbul-lib-coverage@^1.2.0, istanbul-lib-coverage@^1.2.1:
  version "1.2.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/istanbul-lib-coverage/-/istanbul-lib-coverage-1.2.1.tgz#ccf7edcd0a0bb9b8f729feeb0930470f9af664f0"
  integrity sha1-zPftzQoLubj3Kf7rCTBHD5r2ZPA=

istanbul-lib-hook@^1.2.2:
  version "1.2.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/istanbul-lib-hook/-/istanbul-lib-hook-1.2.2.tgz#bc6bf07f12a641fbf1c85391d0daa8f0aea6bf86"
  integrity sha1-vGvwfxKmQfvxyFOR0Nqo8K6mv4Y=
  dependencies:
    append-transform "^0.4.0"

istanbul-lib-instrument@^1.10.1, istanbul-lib-instrument@^1.10.2:
  version "1.10.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/istanbul-lib-instrument/-/istanbul-lib-instrument-1.10.2.tgz#1f55ed10ac3c47f2bdddd5307935126754d0a9ca"
  integrity sha1-H1XtEKw8R/K93dUweTUSZ1TQqco=
  dependencies:
    babel-generator "^6.18.0"
    babel-template "^6.16.0"
    babel-traverse "^6.18.0"
    babel-types "^6.18.0"
    babylon "^6.18.0"
    istanbul-lib-coverage "^1.2.1"
    semver "^5.3.0"

istanbul-lib-report@^1.1.5:
  version "1.1.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/istanbul-lib-report/-/istanbul-lib-report-1.1.5.tgz#f2a657fc6282f96170aaf281eb30a458f7f4170c"
  integrity sha1-8qZX/GKC+WFwqvKB6zCkWPf0Fww=
  dependencies:
    istanbul-lib-coverage "^1.2.1"
    mkdirp "^0.5.1"
    path-parse "^1.0.5"
    supports-color "^3.1.2"

istanbul-lib-source-maps@^1.2.4, istanbul-lib-source-maps@^1.2.6:
  version "1.2.6"
  resolved "https://nexus.lanmaoly.com/repository/npm/istanbul-lib-source-maps/-/istanbul-lib-source-maps-1.2.6.tgz#37b9ff661580f8fca11232752ee42e08c6675d8f"
  integrity sha1-N7n/ZhWA+PyhEjJ1LuQuCMZnXY8=
  dependencies:
    debug "^3.1.0"
    istanbul-lib-coverage "^1.2.1"
    mkdirp "^0.5.1"
    rimraf "^2.6.1"
    source-map "^0.5.3"

istanbul-reports@^1.5.1:
  version "1.5.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/istanbul-reports/-/istanbul-reports-1.5.1.tgz#97e4dbf3b515e8c484caea15d6524eebd3ff4e1a"
  integrity sha1-l+Tb87UV6MSEyuoV1lJO69P/Tho=
  dependencies:
    handlebars "^4.0.3"

jest-changed-files@^23.4.2:
  version "23.4.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/jest-changed-files/-/jest-changed-files-23.4.2.tgz#1eed688370cd5eebafe4ae93d34bb3b64968fe83"
  integrity sha1-Hu1og3DNXuuv5K6T00uztklo/oM=
  dependencies:
    throat "^4.0.0"

jest-cli@^23.6.0:
  version "23.6.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/jest-cli/-/jest-cli-23.6.0.tgz#61ab917744338f443ef2baa282ddffdd658a5da4"
  integrity sha1-YauRd0Qzj0Q+8rqigt3/3WWKXaQ=
  dependencies:
    ansi-escapes "^3.0.0"
    chalk "^2.0.1"
    exit "^0.1.2"
    glob "^7.1.2"
    graceful-fs "^4.1.11"
    import-local "^1.0.0"
    is-ci "^1.0.10"
    istanbul-api "^1.3.1"
    istanbul-lib-coverage "^1.2.0"
    istanbul-lib-instrument "^1.10.1"
    istanbul-lib-source-maps "^1.2.4"
    jest-changed-files "^23.4.2"
    jest-config "^23.6.0"
    jest-environment-jsdom "^23.4.0"
    jest-get-type "^22.1.0"
    jest-haste-map "^23.6.0"
    jest-message-util "^23.4.0"
    jest-regex-util "^23.3.0"
    jest-resolve-dependencies "^23.6.0"
    jest-runner "^23.6.0"
    jest-runtime "^23.6.0"
    jest-snapshot "^23.6.0"
    jest-util "^23.4.0"
    jest-validate "^23.6.0"
    jest-watcher "^23.4.0"
    jest-worker "^23.2.0"
    micromatch "^2.3.11"
    node-notifier "^5.2.1"
    prompts "^0.1.9"
    realpath-native "^1.0.0"
    rimraf "^2.5.4"
    slash "^1.0.0"
    string-length "^2.0.0"
    strip-ansi "^4.0.0"
    which "^1.2.12"
    yargs "^11.0.0"

jest-config@^23.6.0:
  version "23.6.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/jest-config/-/jest-config-23.6.0.tgz#f82546a90ade2d8c7026fbf6ac5207fc22f8eb1d"
  integrity sha1-+CVGqQreLYxwJvv2rFIH/CL46x0=
  dependencies:
    babel-core "^6.0.0"
    babel-jest "^23.6.0"
    chalk "^2.0.1"
    glob "^7.1.1"
    jest-environment-jsdom "^23.4.0"
    jest-environment-node "^23.4.0"
    jest-get-type "^22.1.0"
    jest-jasmine2 "^23.6.0"
    jest-regex-util "^23.3.0"
    jest-resolve "^23.6.0"
    jest-util "^23.4.0"
    jest-validate "^23.6.0"
    micromatch "^2.3.11"
    pretty-format "^23.6.0"

jest-diff@^23.6.0:
  version "23.6.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/jest-diff/-/jest-diff-23.6.0.tgz#1500f3f16e850bb3d71233408089be099f610c7d"
  integrity sha1-FQDz8W6FC7PXEjNAgIm+CZ9hDH0=
  dependencies:
    chalk "^2.0.1"
    diff "^3.2.0"
    jest-get-type "^22.1.0"
    pretty-format "^23.6.0"

jest-docblock@^23.2.0:
  version "23.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/jest-docblock/-/jest-docblock-23.2.0.tgz#f085e1f18548d99fdd69b20207e6fd55d91383a7"
  integrity sha1-8IXh8YVI2Z/dabICB+b9VdkTg6c=
  dependencies:
    detect-newline "^2.1.0"

jest-each@^23.6.0:
  version "23.6.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/jest-each/-/jest-each-23.6.0.tgz#ba0c3a82a8054387016139c733a05242d3d71575"
  integrity sha1-ugw6gqgFQ4cBYTnHM6BSQtPXFXU=
  dependencies:
    chalk "^2.0.1"
    pretty-format "^23.6.0"

jest-environment-jsdom@^23.4.0:
  version "23.4.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/jest-environment-jsdom/-/jest-environment-jsdom-23.4.0.tgz#056a7952b3fea513ac62a140a2c368c79d9e6023"
  integrity sha1-BWp5UrP+pROsYqFAosNox52eYCM=
  dependencies:
    jest-mock "^23.2.0"
    jest-util "^23.4.0"
    jsdom "^11.5.1"

jest-environment-node@^23.4.0:
  version "23.4.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/jest-environment-node/-/jest-environment-node-23.4.0.tgz#57e80ed0841dea303167cce8cd79521debafde10"
  integrity sha1-V+gO0IQd6jAxZ8zozXlSHeuv3hA=
  dependencies:
    jest-mock "^23.2.0"
    jest-util "^23.4.0"

jest-get-type@^22.1.0:
  version "22.4.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/jest-get-type/-/jest-get-type-22.4.3.tgz#e3a8504d8479342dd4420236b322869f18900ce4"
  integrity sha1-46hQTYR5NC3UQgI2syKGnxiQDOQ=

jest-haste-map@^23.6.0:
  version "23.6.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/jest-haste-map/-/jest-haste-map-23.6.0.tgz#2e3eb997814ca696d62afdb3f2529f5bbc935e16"
  integrity sha1-Lj65l4FMppbWKv2z8lKfW7yTXhY=
  dependencies:
    fb-watchman "^2.0.0"
    graceful-fs "^4.1.11"
    invariant "^2.2.4"
    jest-docblock "^23.2.0"
    jest-serializer "^23.0.1"
    jest-worker "^23.2.0"
    micromatch "^2.3.11"
    sane "^2.0.0"

jest-jasmine2@^23.6.0:
  version "23.6.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/jest-jasmine2/-/jest-jasmine2-23.6.0.tgz#840e937f848a6c8638df24360ab869cc718592e0"
  integrity sha1-hA6Tf4SKbIY43yQ2CrhpzHGFkuA=
  dependencies:
    babel-traverse "^6.0.0"
    chalk "^2.0.1"
    co "^4.6.0"
    expect "^23.6.0"
    is-generator-fn "^1.0.0"
    jest-diff "^23.6.0"
    jest-each "^23.6.0"
    jest-matcher-utils "^23.6.0"
    jest-message-util "^23.4.0"
    jest-snapshot "^23.6.0"
    jest-util "^23.4.0"
    pretty-format "^23.6.0"

jest-leak-detector@^23.6.0:
  version "23.6.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/jest-leak-detector/-/jest-leak-detector-23.6.0.tgz#e4230fd42cf381a1a1971237ad56897de7e171de"
  integrity sha1-5CMP1CzzgaGhlxI3rVaJfefhcd4=
  dependencies:
    pretty-format "^23.6.0"

jest-matcher-utils@^23.6.0:
  version "23.6.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/jest-matcher-utils/-/jest-matcher-utils-23.6.0.tgz#726bcea0c5294261a7417afb6da3186b4b8cac80"
  integrity sha1-cmvOoMUpQmGnQXr7baMYa0uMrIA=
  dependencies:
    chalk "^2.0.1"
    jest-get-type "^22.1.0"
    pretty-format "^23.6.0"

jest-message-util@^23.4.0:
  version "23.4.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/jest-message-util/-/jest-message-util-23.4.0.tgz#17610c50942349508d01a3d1e0bda2c079086a9f"
  integrity sha1-F2EMUJQjSVCNAaPR4L2iwHkIap8=
  dependencies:
    "@babel/code-frame" "^7.0.0-beta.35"
    chalk "^2.0.1"
    micromatch "^2.3.11"
    slash "^1.0.0"
    stack-utils "^1.0.1"

jest-mock@^23.2.0:
  version "23.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/jest-mock/-/jest-mock-23.2.0.tgz#ad1c60f29e8719d47c26e1138098b6d18b261134"
  integrity sha1-rRxg8p6HGdR8JuETgJi20YsmETQ=

jest-regex-util@^23.3.0:
  version "23.3.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/jest-regex-util/-/jest-regex-util-23.3.0.tgz#5f86729547c2785c4002ceaa8f849fe8ca471bc5"
  integrity sha1-X4ZylUfCeFxAAs6qj4Sf6MpHG8U=

jest-resolve-dependencies@^23.6.0:
  version "23.6.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/jest-resolve-dependencies/-/jest-resolve-dependencies-23.6.0.tgz#b4526af24c8540d9a3fab102c15081cf509b723d"
  integrity sha1-tFJq8kyFQNmj+rECwVCBz1Cbcj0=
  dependencies:
    jest-regex-util "^23.3.0"
    jest-snapshot "^23.6.0"

jest-resolve@^23.6.0:
  version "23.6.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/jest-resolve/-/jest-resolve-23.6.0.tgz#cf1d1a24ce7ee7b23d661c33ba2150f3aebfa0ae"
  integrity sha1-zx0aJM5+57I9ZhwzuiFQ866/oK4=
  dependencies:
    browser-resolve "^1.11.3"
    chalk "^2.0.1"
    realpath-native "^1.0.0"

jest-runner@^23.6.0:
  version "23.6.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/jest-runner/-/jest-runner-23.6.0.tgz#3894bd219ffc3f3cb94dc48a4170a2e6f23a5a38"
  integrity sha1-OJS9IZ/8Pzy5TcSKQXCi5vI6Wjg=
  dependencies:
    exit "^0.1.2"
    graceful-fs "^4.1.11"
    jest-config "^23.6.0"
    jest-docblock "^23.2.0"
    jest-haste-map "^23.6.0"
    jest-jasmine2 "^23.6.0"
    jest-leak-detector "^23.6.0"
    jest-message-util "^23.4.0"
    jest-runtime "^23.6.0"
    jest-util "^23.4.0"
    jest-worker "^23.2.0"
    source-map-support "^0.5.6"
    throat "^4.0.0"

jest-runtime@^23.6.0:
  version "23.6.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/jest-runtime/-/jest-runtime-23.6.0.tgz#059e58c8ab445917cd0e0d84ac2ba68de8f23082"
  integrity sha1-BZ5YyKtEWRfNDg2ErCumjejyMII=
  dependencies:
    babel-core "^6.0.0"
    babel-plugin-istanbul "^4.1.6"
    chalk "^2.0.1"
    convert-source-map "^1.4.0"
    exit "^0.1.2"
    fast-json-stable-stringify "^2.0.0"
    graceful-fs "^4.1.11"
    jest-config "^23.6.0"
    jest-haste-map "^23.6.0"
    jest-message-util "^23.4.0"
    jest-regex-util "^23.3.0"
    jest-resolve "^23.6.0"
    jest-snapshot "^23.6.0"
    jest-util "^23.4.0"
    jest-validate "^23.6.0"
    micromatch "^2.3.11"
    realpath-native "^1.0.0"
    slash "^1.0.0"
    strip-bom "3.0.0"
    write-file-atomic "^2.1.0"
    yargs "^11.0.0"

jest-serializer@^23.0.1:
  version "23.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/jest-serializer/-/jest-serializer-23.0.1.tgz#a3776aeb311e90fe83fab9e533e85102bd164165"
  integrity sha1-o3dq6zEekP6D+rnlM+hRAr0WQWU=

jest-snapshot@^23.6.0:
  version "23.6.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/jest-snapshot/-/jest-snapshot-23.6.0.tgz#f9c2625d1b18acda01ec2d2b826c0ce58a5aa17a"
  integrity sha1-+cJiXRsYrNoB7C0rgmwM5YpaoXo=
  dependencies:
    babel-types "^6.0.0"
    chalk "^2.0.1"
    jest-diff "^23.6.0"
    jest-matcher-utils "^23.6.0"
    jest-message-util "^23.4.0"
    jest-resolve "^23.6.0"
    mkdirp "^0.5.1"
    natural-compare "^1.4.0"
    pretty-format "^23.6.0"
    semver "^5.5.0"

jest-util@^23.4.0:
  version "23.4.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/jest-util/-/jest-util-23.4.0.tgz#4d063cb927baf0a23831ff61bec2cbbf49793561"
  integrity sha1-TQY8uSe68KI4Mf9hvsLLv0l5NWE=
  dependencies:
    callsites "^2.0.0"
    chalk "^2.0.1"
    graceful-fs "^4.1.11"
    is-ci "^1.0.10"
    jest-message-util "^23.4.0"
    mkdirp "^0.5.1"
    slash "^1.0.0"
    source-map "^0.6.0"

jest-validate@^23.6.0:
  version "23.6.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/jest-validate/-/jest-validate-23.6.0.tgz#36761f99d1ed33fcd425b4e4c5595d62b6597474"
  integrity sha1-NnYfmdHtM/zUJbTkxVldYrZZdHQ=
  dependencies:
    chalk "^2.0.1"
    jest-get-type "^22.1.0"
    leven "^2.1.0"
    pretty-format "^23.6.0"

jest-watcher@^23.4.0:
  version "23.4.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/jest-watcher/-/jest-watcher-23.4.0.tgz#d2e28ce74f8dad6c6afc922b92cabef6ed05c91c"
  integrity sha1-0uKM50+NrWxq/JIrksq+9u0FyRw=
  dependencies:
    ansi-escapes "^3.0.0"
    chalk "^2.0.1"
    string-length "^2.0.0"

jest-worker@^23.2.0:
  version "23.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/jest-worker/-/jest-worker-23.2.0.tgz#faf706a8da36fae60eb26957257fa7b5d8ea02b9"
  integrity sha1-+vcGqNo2+uYOsmlXJX+ntdjqArk=
  dependencies:
    merge-stream "^1.0.1"

jest@^23.5.0:
  version "23.6.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/jest/-/jest-23.6.0.tgz#ad5835e923ebf6e19e7a1d7529a432edfee7813d"
  integrity sha1-rVg16SPr9uGeeh11KaQy7f7ngT0=
  dependencies:
    import-local "^1.0.0"
    jest-cli "^23.6.0"

js-base64@^2.1.8:
  version "2.5.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/js-base64/-/js-base64-2.5.1.tgz#1efa39ef2c5f7980bb1784ade4a8af2de3291121"
  integrity sha1-Hvo57yxfeYC7F4St5KivLeMpESE=

js-levenshtein@^1.1.3:
  version "1.1.6"
  resolved "https://nexus.lanmaoly.com/repository/npm/js-levenshtein/-/js-levenshtein-1.1.6.tgz#c6cee58eb3550372df8deb85fad5ce66ce01d59d"
  integrity sha1-xs7ljrNVA3LfjeuF+tXOZs4B1Z0=

"js-tokens@^3.0.0 || ^4.0.0", js-tokens@^4.0.0:
  version "4.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/js-tokens/-/js-tokens-4.0.0.tgz#19203fb59991df98e3a287050d4647cdeaf32499"
  integrity sha1-GSA/tZmR35jjoocFDUZHzerzJJk=

js-tokens@^3.0.2:
  version "3.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/js-tokens/-/js-tokens-3.0.2.tgz#9866df395102130e38f7f996bceb65443209c25b"
  integrity sha1-mGbfOVECEw449/mWvOtlRDIJwls=

js-yaml@^3.13.0, js-yaml@^3.13.1, js-yaml@^3.7.0:
  version "3.13.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/js-yaml/-/js-yaml-3.13.1.tgz#aff151b30bfdfa8e49e05da22e7415e9dfa37847"
  integrity sha1-r/FRswv9+o5J4F2iLnQV6d+jeEc=
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

jsbn@~0.1.0:
  version "0.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/jsbn/-/jsbn-0.1.1.tgz#a5e654c2e5a2deb5f201d96cefbca80c0ef2f513"
  integrity sha1-peZUwuWi3rXyAdls77yoDA7y9RM=

jsdom@^11.5.1:
  version "11.12.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/jsdom/-/jsdom-11.12.0.tgz#1a80d40ddd378a1de59656e9e6dc5a3ba8657bc8"
  integrity sha1-GoDUDd03ih3lllbp5txaO6hle8g=
  dependencies:
    abab "^2.0.0"
    acorn "^5.5.3"
    acorn-globals "^4.1.0"
    array-equal "^1.0.0"
    cssom ">= 0.3.2 < 0.4.0"
    cssstyle "^1.0.0"
    data-urls "^1.0.0"
    domexception "^1.0.1"
    escodegen "^1.9.1"
    html-encoding-sniffer "^1.0.2"
    left-pad "^1.3.0"
    nwsapi "^2.0.7"
    parse5 "4.0.0"
    pn "^1.1.0"
    request "^2.87.0"
    request-promise-native "^1.0.5"
    sax "^1.2.4"
    symbol-tree "^3.2.2"
    tough-cookie "^2.3.4"
    w3c-hr-time "^1.0.1"
    webidl-conversions "^4.0.2"
    whatwg-encoding "^1.0.3"
    whatwg-mimetype "^2.1.0"
    whatwg-url "^6.4.1"
    ws "^5.2.0"
    xml-name-validator "^3.0.0"

jsesc@^1.3.0:
  version "1.3.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/jsesc/-/jsesc-1.3.0.tgz#46c3fec8c1892b12b0833db9bc7622176dbab34b"
  integrity sha1-RsP+yMGJKxKwgz25vHYiF226s0s=

jsesc@^2.5.1:
  version "2.5.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/jsesc/-/jsesc-2.5.2.tgz#80564d2e483dacf6e8ef209650a67df3f0c283a4"
  integrity sha1-gFZNLkg9rPbo7yCWUKZ98/DCg6Q=

jsesc@~0.5.0:
  version "0.5.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/jsesc/-/jsesc-0.5.0.tgz#e7dee66e35d6fc16f710fe91d5cf69f70f08911d"
  integrity sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0=

json-parse-better-errors@^1.0.1, json-parse-better-errors@^1.0.2:
  version "1.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/json-parse-better-errors/-/json-parse-better-errors-1.0.2.tgz#bb867cfb3450e69107c131d1c514bab3dc8bcaa9"
  integrity sha1-u4Z8+zRQ5pEHwTHRxRS6s9yLyqk=

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz#69f6a87d9513ab8bb8fe63bdb0979c448e684660"
  integrity sha1-afaofZUTq4u4/mO9sJecRI5oRmA=

json-schema@0.2.3:
  version "0.2.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/json-schema/-/json-schema-0.2.3.tgz#b480c892e59a2f05954ce727bd3f2a4e882f9e13"
  integrity sha1-tIDIkuWaLwWVTOcnvT8qTogvnhM=

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz#9db7b59496ad3f3cfef30a75142d2d930ad72651"
  integrity sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=

json-stringify-safe@~5.0.1:
  version "5.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz#1296a2d58fd45f19a0f6ce01d65701e2c735b6eb"
  integrity sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus=

json3@^3.3.2:
  version "3.3.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/json3/-/json3-3.3.3.tgz#7fc10e375fc5ae42c4705a5cc0aa6f62be305b81"
  integrity sha1-f8EON1/FrkLEcFpcwKpvYr4wW4E=

json5@^0.5.0, json5@^0.5.1:
  version "0.5.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/json5/-/json5-0.5.1.tgz#1eade7acc012034ad84e2396767ead9fa5495821"
  integrity sha1-Hq3nrMASA0rYTiOWdn6tn6VJWCE=

json5@^1.0.1:
  version "1.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/json5/-/json5-1.0.1.tgz#779fb0018604fa854eacbf6252180d83543e3dbe"
  integrity sha1-d5+wAYYE+oVOrL9iUhgNg1Q+Pb4=
  dependencies:
    minimist "^1.2.0"

json5@^2.1.0:
  version "2.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/json5/-/json5-2.1.0.tgz#e7a0c62c48285c628d20a10b85c89bb807c32850"
  integrity sha1-56DGLEgoXGKNIKELhcibuAfDKFA=
  dependencies:
    minimist "^1.2.0"

json5@^2.1.2:
  version "2.2.0"
  resolved "https://r.cnpmjs.org/json5/download/json5-2.2.0.tgz#2dfefe720c6ba525d9ebd909950f0515316c89a3"
  integrity sha1-Lf7+cgxrpSXZ69kJlQ8FFTFsiaM=
  dependencies:
    minimist "^1.2.5"

jsonfile@^4.0.0:
  version "4.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/jsonfile/-/jsonfile-4.0.0.tgz#8771aae0799b64076b76640fca058f9c10e33ecb"
  integrity sha1-h3Gq4HmbZAdrdmQPygWPnBDjPss=
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonify@~0.0.0:
  version "0.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/jsonify/-/jsonify-0.0.0.tgz#2c74b6ee41d93ca51b7b5aaee8f503631d252a73"
  integrity sha1-LHS27kHZPKUbe1qu6PUDYx0lKnM=

jsprim@^1.2.2:
  version "1.4.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/jsprim/-/jsprim-1.4.1.tgz#313e66bc1e5cc06e438bc1b7499c2e5c56acb6a2"
  integrity sha1-MT5mvB5cwG5Di8G3SZwuXFastqI=
  dependencies:
    assert-plus "1.0.0"
    extsprintf "1.3.0"
    json-schema "0.2.3"
    verror "1.10.0"

jsx-ast-utils@^2.0.1:
  version "2.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/jsx-ast-utils/-/jsx-ast-utils-2.0.1.tgz#e801b1b39985e20fffc87b40e3748080e2dcac7f"
  integrity sha1-6AGxs5mF4g//yHtA43SAgOLcrH8=
  dependencies:
    array-includes "^3.0.3"

killable@^1.0.1:
  version "1.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/killable/-/killable-1.0.1.tgz#4c8ce441187a061c7474fb87ca08e2a638194892"
  integrity sha1-TIzkQRh6Bhx0dPuHygjipjgZSJI=

kind-of@^3.0.2, kind-of@^3.0.3, kind-of@^3.2.0:
  version "3.2.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/kind-of/-/kind-of-3.2.2.tgz#31ea21a734bab9bbb0f32466d893aea51e4a3c64"
  integrity sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=
  dependencies:
    is-buffer "^1.1.5"

kind-of@^4.0.0:
  version "4.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/kind-of/-/kind-of-4.0.0.tgz#20813df3d712928b207378691a45066fae72dd57"
  integrity sha1-IIE989cSkosgc3hpGkUGb65y3Vc=
  dependencies:
    is-buffer "^1.1.5"

kind-of@^5.0.0:
  version "5.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/kind-of/-/kind-of-5.1.0.tgz#729c91e2d857b7a419a1f9aa65685c4c33f5845d"
  integrity sha1-cpyR4thXt6QZofmqZWhcTDP1hF0=

kind-of@^6.0.0, kind-of@^6.0.2:
  version "6.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/kind-of/-/kind-of-6.0.2.tgz#01146b36a6218e64e58f3a8d66de5d7fc6f6d051"
  integrity sha1-ARRrNqYhjmTljzqNZt5df8b20FE=

kleur@^2.0.1:
  version "2.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/kleur/-/kleur-2.0.2.tgz#b704f4944d95e255d038f0cb05fb8a602c55a300"
  integrity sha1-twT0lE2V4lXQOPDLBfuKYCxVowA=

lcid@^1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/lcid/-/lcid-1.0.0.tgz#308accafa0bc483a3867b4b6f2b9506251d1b835"
  integrity sha1-MIrMr6C8SDo4Z7S28rlQYlHRuDU=
  dependencies:
    invert-kv "^1.0.0"

lcid@^2.0.0:
  version "2.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/lcid/-/lcid-2.0.0.tgz#6ef5d2df60e52f82eb228a4c373e8d1f397253cf"
  integrity sha1-bvXS32DlL4LrIopMNz6NHzlyU88=
  dependencies:
    invert-kv "^2.0.0"

left-pad@^1.3.0:
  version "1.3.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/left-pad/-/left-pad-1.3.0.tgz#5b8a3a7765dfe001261dde915589e782f8c94d1e"
  integrity sha1-W4o6d2Xf4AEmHd6RVYnngvjJTR4=

less-loader@^4.1.0:
  version "4.1.0"
  resolved "https://r.cnpmjs.org/less-loader/download/less-loader-4.1.0.tgz#2c1352c5b09a4f84101490274fd51674de41363e"
  integrity sha1-LBNSxbCaT4QQFJAnT9UWdN5BNj4=
  dependencies:
    clone "^2.1.1"
    loader-utils "^1.1.0"
    pify "^3.0.0"

less-loader@^5.0.0:
  version "5.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/less-loader/-/less-loader-5.0.0.tgz#498dde3a6c6c4f887458ee9ed3f086a12ad1b466"
  integrity sha1-SY3eOmxsT4h0WO6e0/CGoSrRtGY=
  dependencies:
    clone "^2.1.1"
    loader-utils "^1.1.0"
    pify "^4.0.1"

less@^3.8.1:
  version "3.13.1"
  resolved "https://r.cnpmjs.org/less/download/less-3.13.1.tgz#0ebc91d2a0e9c0c6735b83d496b0ab0583077909"
  integrity sha1-DryR0qDpwMZzW4PUlrCrBYMHeQk=
  dependencies:
    copy-anything "^2.0.1"
    tslib "^1.10.0"
  optionalDependencies:
    errno "^0.1.1"
    graceful-fs "^4.1.2"
    image-size "~0.5.0"
    make-dir "^2.1.0"
    mime "^1.4.1"
    native-request "^1.0.5"
    source-map "~0.6.0"

less@^3.9.0:
  version "3.9.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/less/-/less-3.9.0.tgz#b7511c43f37cf57dc87dffd9883ec121289b1474"
  integrity sha1-t1EcQ/N89X3Iff/ZiD7BISibFHQ=
  dependencies:
    clone "^2.1.2"
  optionalDependencies:
    errno "^0.1.1"
    graceful-fs "^4.1.2"
    image-size "~0.5.0"
    mime "^1.4.1"
    mkdirp "^0.5.0"
    promise "^7.1.1"
    request "^2.83.0"
    source-map "~0.6.0"

leven@^2.1.0:
  version "2.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/leven/-/leven-2.1.0.tgz#c2e7a9f772094dee9d34202ae8acce4687875580"
  integrity sha1-wuep93IJTe6dNCAq6KzORoeHVYA=

levn@^0.3.0, levn@~0.3.0:
  version "0.3.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/levn/-/levn-0.3.0.tgz#3b09924edf9f083c0490fdd4c0bc4421e04764ee"
  integrity sha1-OwmSTt+fCDwEkP3UwLxEIeBHZO4=
  dependencies:
    prelude-ls "~1.1.2"
    type-check "~0.3.2"

load-json-file@^1.0.0:
  version "1.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/load-json-file/-/load-json-file-1.1.0.tgz#956905708d58b4bab4c2261b04f59f31c99374c0"
  integrity sha1-lWkFcI1YtLq0wiYbBPWfMcmTdMA=
  dependencies:
    graceful-fs "^4.1.2"
    parse-json "^2.2.0"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"
    strip-bom "^2.0.0"

load-json-file@^2.0.0:
  version "2.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/load-json-file/-/load-json-file-2.0.0.tgz#7947e42149af80d696cbf797bcaabcfe1fe29ca8"
  integrity sha1-eUfkIUmvgNaWy/eXvKq8/h/inKg=
  dependencies:
    graceful-fs "^4.1.2"
    parse-json "^2.2.0"
    pify "^2.0.0"
    strip-bom "^3.0.0"

load-json-file@^4.0.0:
  version "4.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/load-json-file/-/load-json-file-4.0.0.tgz#2f5f45ab91e33216234fd53adab668eb4ec0993b"
  integrity sha1-L19Fq5HjMhYjT9U62rZo607AmTs=
  dependencies:
    graceful-fs "^4.1.2"
    parse-json "^4.0.0"
    pify "^3.0.0"
    strip-bom "^3.0.0"

loader-runner@^2.3.0:
  version "2.4.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/loader-runner/-/loader-runner-2.4.0.tgz#ed47066bfe534d7e84c4c7b9998c2a75607d9357"
  integrity sha1-7UcGa/5TTX6ExMe5mYwqdWB9k1c=

loader-utils@0.2.16:
  version "0.2.16"
  resolved "https://r.cnpmjs.org/loader-utils/download/loader-utils-0.2.16.tgz#f08632066ed8282835dff88dfb52704765adee6d"
  integrity sha1-8IYyBm7YKCg13/iN+1JwR2Wt7m0=
  dependencies:
    big.js "^3.1.3"
    emojis-list "^2.0.0"
    json5 "^0.5.0"
    object-assign "^4.0.1"

loader-utils@1.2.3, loader-utils@^1.0.1, loader-utils@^1.0.2, loader-utils@^1.1.0, loader-utils@^1.2.3:
  version "1.2.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/loader-utils/-/loader-utils-1.2.3.tgz#1ff5dc6911c9f0a062531a4c04b609406108c2c7"
  integrity sha1-H/XcaRHJ8KBiUxpMBLYJQGEIwsc=
  dependencies:
    big.js "^5.2.2"
    emojis-list "^2.0.0"
    json5 "^1.0.1"

loader-utils@^0.2.16:
  version "0.2.17"
  resolved "https://nexus.lanmaoly.com/repository/npm/loader-utils/-/loader-utils-0.2.17.tgz#f86e6374d43205a6e6c60e9196f17c0299bfb348"
  integrity sha1-+G5jdNQyBabmxg6RlvF8Apm/s0g=
  dependencies:
    big.js "^3.1.3"
    emojis-list "^2.0.0"
    json5 "^0.5.0"
    object-assign "^4.0.1"

loader-utils@^2.0.0:
  version "2.0.0"
  resolved "https://r.cnpmjs.org/loader-utils/download/loader-utils-2.0.0.tgz#e4cace5b816d425a166b5f097e10cd12b36064b0"
  integrity sha1-5MrOW4FtQloWa18JfhDNErNgZLA=
  dependencies:
    big.js "^5.2.2"
    emojis-list "^3.0.0"
    json5 "^2.1.2"

locate-path@^2.0.0:
  version "2.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/locate-path/-/locate-path-2.0.0.tgz#2b568b265eec944c6d9c0de9c3dbbbca0354cd8e"
  integrity sha1-K1aLJl7slExtnA3pw9u7ygNUzY4=
  dependencies:
    p-locate "^2.0.0"
    path-exists "^3.0.0"

locate-path@^3.0.0:
  version "3.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/locate-path/-/locate-path-3.0.0.tgz#dbec3b3ab759758071b58fe59fc41871af21400e"
  integrity sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4=
  dependencies:
    p-locate "^3.0.0"
    path-exists "^3.0.0"

lodash._getnative@^3.0.0:
  version "3.9.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/lodash._getnative/-/lodash._getnative-3.9.1.tgz#570bc7dede46d61cdcde687d65d3eecbaa3aaff5"
  integrity sha1-VwvH3t5G1hzc3mh9ZdPuy6o6r/U=

lodash.isarguments@^3.0.0:
  version "3.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/lodash.isarguments/-/lodash.isarguments-3.1.0.tgz#2f573d85c6a24289ff00663b491c1d338ff3458a"
  integrity sha1-L1c9hcaiQon/AGY7SRwdM4/zRYo=

lodash.isarray@^3.0.0:
  version "3.0.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/lodash.isarray/-/lodash.isarray-3.0.4.tgz#79e4eb88c36a8122af86f844aa9bcd851b5fbb55"
  integrity sha1-eeTriMNqgSKvhvhEqpvNhRtfu1U=

lodash.keys@^3.1.2:
  version "3.1.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/lodash.keys/-/lodash.keys-3.1.2.tgz#4dbc0472b156be50a0b286855d1bd0b0c656098a"
  integrity sha1-TbwEcrFWvlCgsoaFXRvQsMZWCYo=
  dependencies:
    lodash._getnative "^3.0.0"
    lodash.isarguments "^3.0.0"
    lodash.isarray "^3.0.0"

lodash.merge@^4.6.1:
  version "4.6.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/lodash.merge/-/lodash.merge-4.6.2.tgz#558aa53b43b661e1925a0afdfa36a9a1085fe57a"
  integrity sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo=

lodash.sortby@^4.7.0:
  version "4.7.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/lodash.sortby/-/lodash.sortby-4.7.0.tgz#edd14c824e2cc9c1e0b0a1b42bb5210516a42438"
  integrity sha1-7dFMgk4sycHgsKG0K7UhBRakJDg=

lodash.tail@^4.1.1:
  version "4.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/lodash.tail/-/lodash.tail-4.1.1.tgz#d2333a36d9e7717c8ad2f7cacafec7c32b444664"
  integrity sha1-0jM6NtnncXyK0vfKyv7HwytERmQ=

"lodash@>=3.5 <5", lodash@^4.0.0, lodash@^4.13.1, lodash@^4.17.10, lodash@^4.17.11, lodash@^4.17.12, lodash@^4.17.13, lodash@^4.17.14, lodash@^4.17.15, lodash@^4.17.3, lodash@^4.17.4, lodash@~4.17.10:
  version "4.17.15"
  resolved "https://nexus.lanmaoly.com/repository/npm/lodash/-/lodash-4.17.15.tgz#b447f6670a0455bbfeedd11392eff330ea097548"
  integrity sha1-tEf2ZwoEVbv+7dETku/zMOoJdUg=

lodash@^4.16.6:
  version "4.17.21"
  resolved "https://r.cnpmjs.org/lodash/download/lodash-4.17.21.tgz#679591c564c3bffaae8454cf0b3df370c3d6911c"
  integrity sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=

loglevel@^1.6.3:
  version "1.6.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/loglevel/-/loglevel-1.6.3.tgz#77f2eb64be55a404c9fd04ad16d57c1d6d6b1280"
  integrity sha1-d/LrZL5VpATJ/QStFtV8HW1rEoA=

loose-envify@^1.0.0, loose-envify@^1.1.0, loose-envify@^1.2.0, loose-envify@^1.3.1, loose-envify@^1.4.0:
  version "1.4.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/loose-envify/-/loose-envify-1.4.0.tgz#71ee51fa7be4caec1a63839f7e682d8132d30caf"
  integrity sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8=
  dependencies:
    js-tokens "^3.0.0 || ^4.0.0"

loud-rejection@^1.0.0:
  version "1.6.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/loud-rejection/-/loud-rejection-1.6.0.tgz#5b46f80147edee578870f086d04821cf998e551f"
  integrity sha1-W0b4AUft7leIcPCG0Eghz5mOVR8=
  dependencies:
    currently-unhandled "^0.4.1"
    signal-exit "^3.0.0"

lower-case@^1.1.1:
  version "1.1.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/lower-case/-/lower-case-1.1.4.tgz#9a2cabd1b9e8e0ae993a4bf7d5875c39c42e8eac"
  integrity sha1-miyr0bno4K6ZOkv31YdcOcQujqw=

lru-cache@^4.0.1, lru-cache@^4.1.1:
  version "4.1.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/lru-cache/-/lru-cache-4.1.5.tgz#8bbe50ea85bed59bc9e33dcab8235ee9bcf443cd"
  integrity sha1-i75Q6oW+1ZvJ4z3KuCNe6bz0Q80=
  dependencies:
    pseudomap "^1.0.2"
    yallist "^2.1.2"

lru-cache@^5.1.1:
  version "5.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/lru-cache/-/lru-cache-5.1.1.tgz#1da27e6710271947695daf6848e847f01d84b920"
  integrity sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=
  dependencies:
    yallist "^3.0.2"

make-cancellable-promise@^1.0.0:
  version "1.0.0"
  resolved "https://r.cnpmjs.org/make-cancellable-promise/download/make-cancellable-promise-1.0.0.tgz#826214115b0827ca7a45ba204df7c31546243870"
  integrity sha1-gmIUEVsIJ8p6RbogTffDFUYkOHA=

make-dir@^1.0.0:
  version "1.3.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/make-dir/-/make-dir-1.3.0.tgz#79c1033b80515bd6d24ec9933e860ca75ee27f0c"
  integrity sha1-ecEDO4BRW9bSTsmTPoYMp17ifww=
  dependencies:
    pify "^3.0.0"

make-dir@^2.0.0, make-dir@^2.1.0:
  version "2.1.0"
  resolved "https://r.cnpmjs.org/make-dir/download/make-dir-2.1.0.tgz#5f0310e18b8be898cc07009295a30ae41e91e6f5"
  integrity sha1-XwMQ4YuL6JjMBwCSlaMK5B6R5vU=
  dependencies:
    pify "^4.0.1"
    semver "^5.6.0"

make-event-props@^1.1.0:
  version "1.2.0"
  resolved "https://r.cnpmjs.org/make-event-props/download/make-event-props-1.2.0.tgz#96b87d88919533b8f8934b58b4c3d5679459a0cf"
  integrity sha1-lrh9iJGVM7j4k0tYtMPVZ5RZoM8=

makeerror@1.0.x:
  version "1.0.11"
  resolved "https://nexus.lanmaoly.com/repository/npm/makeerror/-/makeerror-1.0.11.tgz#e01a5c9109f2af79660e4e8b9587790184f5a96c"
  integrity sha1-4BpckQnyr3lmDk6LlYd5AYT1qWw=
  dependencies:
    tmpl "1.0.x"

mamacro@^0.0.3:
  version "0.0.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/mamacro/-/mamacro-0.0.3.tgz#ad2c9576197c9f1abf308d0787865bd975a3f3e4"
  integrity sha1-rSyVdhl8nxq/MI0Hh4Zb2XWj8+Q=

map-age-cleaner@^0.1.1:
  version "0.1.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/map-age-cleaner/-/map-age-cleaner-0.1.3.tgz#7d583a7306434c055fe474b0f45078e6e1b4b92a"
  integrity sha1-fVg6cwZDTAVf5HSw9FB45uG0uSo=
  dependencies:
    p-defer "^1.0.0"

map-cache@^0.2.2:
  version "0.2.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/map-cache/-/map-cache-0.2.2.tgz#c32abd0bd6525d9b051645bb4f26ac5dc98a0dbf"
  integrity sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8=

map-obj@^1.0.0, map-obj@^1.0.1:
  version "1.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/map-obj/-/map-obj-1.0.1.tgz#d933ceb9205d82bdcf4886f6742bdc2b4dea146d"
  integrity sha1-2TPOuSBdgr3PSIb2dCvcK03qFG0=

map-visit@^1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/map-visit/-/map-visit-1.0.0.tgz#ecdca8f13144e660f1b5bd41f12f3479d98dfb8f"
  integrity sha1-7Nyo8TFE5mDxtb1B8S80edmN+48=
  dependencies:
    object-visit "^1.0.0"

math-random@^1.0.1:
  version "1.0.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/math-random/-/math-random-1.0.4.tgz#5dd6943c938548267016d4e34f057583080c514c"
  integrity sha1-XdaUPJOFSCZwFtTjTwV1gwgMUUw=

md5.js@^1.3.4:
  version "1.3.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/md5.js/-/md5.js-1.3.5.tgz#b5d07b8e3216e3e27cd728d72f70d1e6a342005f"
  integrity sha1-tdB7jjIW4+J81yjXL3DR5qNCAF8=
  dependencies:
    hash-base "^3.0.0"
    inherits "^2.0.1"
    safe-buffer "^5.1.2"

media-typer@0.3.0:
  version "0.3.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/media-typer/-/media-typer-0.3.0.tgz#8710d7af0aa626f8fffa1ce00168545263255748"
  integrity sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=

mem@^1.1.0:
  version "1.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/mem/-/mem-1.1.0.tgz#5edd52b485ca1d900fe64895505399a0dfa45f76"
  integrity sha1-Xt1StIXKHZAP5kiVUFOZoN+kX3Y=
  dependencies:
    mimic-fn "^1.0.0"

mem@^4.0.0:
  version "4.3.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/mem/-/mem-4.3.0.tgz#461af497bc4ae09608cdb2e60eefb69bff744178"
  integrity sha1-Rhr0l7xK4JYIzbLmDu+2m/90QXg=
  dependencies:
    map-age-cleaner "^0.1.1"
    mimic-fn "^2.0.0"
    p-is-promise "^2.0.0"

memory-fs@^0.4.0, memory-fs@^0.4.1, memory-fs@~0.4.1:
  version "0.4.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/memory-fs/-/memory-fs-0.4.1.tgz#3a9a20b8462523e447cfbc7e8bb80ed667bfc552"
  integrity sha1-OpoguEYlI+RHz7x+i7gO1me/xVI=
  dependencies:
    errno "^0.1.3"
    readable-stream "^2.0.1"

memory-fs@^0.5.0:
  version "0.5.0"
  resolved "https://r.cnpmjs.org/memory-fs/download/memory-fs-0.5.0.tgz#324c01288b88652966d161db77838720845a8e3c"
  integrity sha1-MkwBKIuIZSlm0WHbd4OHIIRajjw=
  dependencies:
    errno "^0.1.3"
    readable-stream "^2.0.1"

memorystream@^0.3.1:
  version "0.3.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/memorystream/-/memorystream-0.3.1.tgz#86d7090b30ce455d63fbae12dda51a47ddcaf9b2"
  integrity sha1-htcJCzDORV1j+64S3aUaR93K+bI=

meow@^3.7.0:
  version "3.7.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/meow/-/meow-3.7.0.tgz#72cb668b425228290abbfa856892587308a801fb"
  integrity sha1-cstmi0JSKCkKu/qFaJJYcwioAfs=
  dependencies:
    camelcase-keys "^2.0.0"
    decamelize "^1.1.2"
    loud-rejection "^1.0.0"
    map-obj "^1.0.1"
    minimist "^1.1.3"
    normalize-package-data "^2.3.4"
    object-assign "^4.0.1"
    read-pkg-up "^1.0.1"
    redent "^1.0.0"
    trim-newlines "^1.0.0"

merge-class-names@^1.1.1:
  version "1.4.0"
  resolved "https://r.cnpmjs.org/merge-class-names/download/merge-class-names-1.4.0.tgz#02edcdd5ff677fbb03b47ecd4586df89d697b81b"
  integrity sha1-Au3N1f9nf7sDtH7NRYbfidaXuBs=

merge-descriptors@1.0.1:
  version "1.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/merge-descriptors/-/merge-descriptors-1.0.1.tgz#b00aaa556dd8b44568150ec9d1b953f3f90cbb61"
  integrity sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E=

merge-refs@^1.0.0:
  version "1.0.0"
  resolved "https://r.cnpmjs.org/merge-refs/download/merge-refs-1.0.0.tgz#388348bce22e623782c6df9d3c4fc55888276120"
  integrity sha1-OINIvOIuYjeCxt+dPE/FWIgnYSA=

merge-stream@^1.0.1:
  version "1.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/merge-stream/-/merge-stream-1.0.1.tgz#4041202d508a342ba00174008df0c251b8c135e1"
  integrity sha1-QEEgLVCKNCugAXQAjfDCUbjBNeE=
  dependencies:
    readable-stream "^2.0.1"

merge@^1.2.0:
  version "1.2.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/merge/-/merge-1.2.1.tgz#38bebf80c3220a8a487b6fcfb3941bb11720c145"
  integrity sha1-OL6/gMMiCopIe2/Ps5QbsRcgwUU=

methods@~1.1.2:
  version "1.1.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/methods/-/methods-1.1.2.tgz#5529a4d67654134edcc5266656835b0f851afcee"
  integrity sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=

micromatch@^2.3.11:
  version "2.3.11"
  resolved "https://nexus.lanmaoly.com/repository/npm/micromatch/-/micromatch-2.3.11.tgz#86677c97d1720b363431d04d0d15293bd38c1565"
  integrity sha1-hmd8l9FyCzY0MdBNDRUpO9OMFWU=
  dependencies:
    arr-diff "^2.0.0"
    array-unique "^0.2.1"
    braces "^1.8.2"
    expand-brackets "^0.1.4"
    extglob "^0.3.1"
    filename-regex "^2.0.0"
    is-extglob "^1.0.0"
    is-glob "^2.0.1"
    kind-of "^3.0.2"
    normalize-path "^2.0.1"
    object.omit "^2.0.0"
    parse-glob "^3.0.4"
    regex-cache "^0.4.2"

micromatch@^3.0.4, micromatch@^3.1.10, micromatch@^3.1.4, micromatch@^3.1.8:
  version "3.1.10"
  resolved "https://nexus.lanmaoly.com/repository/npm/micromatch/-/micromatch-3.1.10.tgz#70859bc95c9840952f359a068a3fc49f9ecfac23"
  integrity sha1-cIWbyVyYQJUvNZoGij/En57PrCM=
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    braces "^2.3.1"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    extglob "^2.0.4"
    fragment-cache "^0.2.1"
    kind-of "^6.0.2"
    nanomatch "^1.2.9"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.2"

miller-rabin@^4.0.0:
  version "4.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/miller-rabin/-/miller-rabin-4.0.1.tgz#f080351c865b0dc562a8462966daa53543c78a4d"
  integrity sha1-8IA1HIZbDcViqEYpZtqlNUPHik0=
  dependencies:
    bn.js "^4.0.0"
    brorand "^1.0.1"

mime-db@1.40.0, "mime-db@>= 1.40.0 < 2":
  version "1.40.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/mime-db/-/mime-db-1.40.0.tgz#a65057e998db090f732a68f6c276d387d4126c32"
  integrity sha1-plBX6ZjbCQ9zKmj2wnbTh9QSbDI=

mime-types@^2.1.12, mime-types@~2.1.17, mime-types@~2.1.19, mime-types@~2.1.24:
  version "2.1.24"
  resolved "https://nexus.lanmaoly.com/repository/npm/mime-types/-/mime-types-2.1.24.tgz#b6f8d0b3e951efb77dedeca194cff6d16f676f81"
  integrity sha1-tvjQs+lR77d97eyhlM/20W9nb4E=
  dependencies:
    mime-db "1.40.0"

mime@1.6.0, mime@^1.4.1:
  version "1.6.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/mime/-/mime-1.6.0.tgz#32cd9e5c64553bd58d19a568af452acff04981b1"
  integrity sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE=

mime@^2.0.3, mime@^2.4.2:
  version "2.4.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/mime/-/mime-2.4.4.tgz#bd7b91135fc6b01cde3e9bae33d659b63d8857e5"
  integrity sha1-vXuRE1/GsBzePpuuM9ZZtj2IV+U=

mimic-fn@^1.0.0:
  version "1.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/mimic-fn/-/mimic-fn-1.2.0.tgz#820c86a39334640e99516928bd03fca88057d022"
  integrity sha1-ggyGo5M0ZA6ZUWkovQP8qIBX0CI=

mimic-fn@^2.0.0:
  version "2.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/mimic-fn/-/mimic-fn-2.1.0.tgz#7ed2c2ccccaf84d3ffcb7a69b57711fc2083401b"
  integrity sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=

min-document@^2.19.0:
  version "2.19.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/min-document/-/min-document-2.19.0.tgz#7bd282e3f5842ed295bb748cdd9f1ffa2c824685"
  integrity sha1-e9KC4/WELtKVu3SM3Z8f+iyCRoU=
  dependencies:
    dom-walk "^0.1.0"

mini-css-extract-plugin@^0.4.2:
  version "0.4.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/mini-css-extract-plugin/-/mini-css-extract-plugin-0.4.5.tgz#c99e9e78d54f3fa775633aee5933aeaa4e80719a"
  integrity sha1-yZ6eeNVPP6d1YzruWTOuqk6AcZo=
  dependencies:
    loader-utils "^1.1.0"
    schema-utils "^1.0.0"
    webpack-sources "^1.1.0"

minimalistic-assert@^1.0.0, minimalistic-assert@^1.0.1:
  version "1.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/minimalistic-assert/-/minimalistic-assert-1.0.1.tgz#2e194de044626d4a10e7f7fbc00ce73e83e4d5c7"
  integrity sha1-LhlN4ERibUoQ5/f7wAznPoPk1cc=

minimalistic-crypto-utils@^1.0.0, minimalistic-crypto-utils@^1.0.1:
  version "1.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/minimalistic-crypto-utils/-/minimalistic-crypto-utils-1.0.1.tgz#f6c00c1c0b082246e5c4d99dfb8c7c083b2b582a"
  integrity sha1-9sAMHAsIIkblxNmd+4x8CDsrWCo=

minimatch@^3.0.3, minimatch@^3.0.4, minimatch@~3.0.2:
  version "3.0.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/minimatch/-/minimatch-3.0.4.tgz#5166e286457f03306064be5497e8dbb0c3d32083"
  integrity sha1-UWbihkV/AzBgZL5Ul+jbsMPTIIM=
  dependencies:
    brace-expansion "^1.1.7"

minimist@0.0.8:
  version "0.0.8"
  resolved "https://nexus.lanmaoly.com/repository/npm/minimist/-/minimist-0.0.8.tgz#857fcabfc3397d2625b8228262e86aa7a011b05d"
  integrity sha1-hX/Kv8M5fSYluCKCYuhqp6ARsF0=

minimist@^1.1.1, minimist@^1.1.3, minimist@^1.2.0:
  version "1.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/minimist/-/minimist-1.2.0.tgz#a35008b20f41383eec1fb914f4cd5df79a264284"
  integrity sha1-o1AIsg9BOD7sH7kU9M1d95omQoQ=

minimist@^1.2.5:
  version "1.2.5"
  resolved "https://r.cnpmjs.org/minimist/download/minimist-1.2.5.tgz#67d66014b66a6a8aaa0c083c5fd58df4e4e97602"
  integrity sha1-Z9ZgFLZqaoqqDAg8X9WN9OTpdgI=

minimist@~0.0.1:
  version "0.0.10"
  resolved "https://nexus.lanmaoly.com/repository/npm/minimist/-/minimist-0.0.10.tgz#de3f98543dbf96082be48ad1a0c7cda836301dcf"
  integrity sha1-3j+YVD2/lggr5IrRoMfNqDYwHc8=

minipass@^2.2.1, minipass@^2.3.5:
  version "2.3.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/minipass/-/minipass-2.3.5.tgz#cacebe492022497f656b0f0f51e2682a9ed2d848"
  integrity sha1-ys6+SSAiSX9law8PUeJoKp7S2Eg=
  dependencies:
    safe-buffer "^5.1.2"
    yallist "^3.0.0"

minizlib@^1.2.1:
  version "1.2.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/minizlib/-/minizlib-1.2.1.tgz#dd27ea6136243c7c880684e8672bb3a45fd9b614"
  integrity sha1-3SfqYTYkPHyIBoToZyuzpF/ZthQ=
  dependencies:
    minipass "^2.2.1"

mississippi@^2.0.0:
  version "2.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/mississippi/-/mississippi-2.0.0.tgz#3442a508fafc28500486feea99409676e4ee5a6f"
  integrity sha1-NEKlCPr8KFAEhv7qmUCWduTuWm8=
  dependencies:
    concat-stream "^1.5.0"
    duplexify "^3.4.2"
    end-of-stream "^1.1.0"
    flush-write-stream "^1.0.0"
    from2 "^2.1.0"
    parallel-transform "^1.1.0"
    pump "^2.0.1"
    pumpify "^1.3.3"
    stream-each "^1.1.0"
    through2 "^2.0.0"

mississippi@^3.0.0:
  version "3.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/mississippi/-/mississippi-3.0.0.tgz#ea0a3291f97e0b5e8776b363d5f0a12d94c67022"
  integrity sha1-6goykfl+C16HdrNj1fChLZTGcCI=
  dependencies:
    concat-stream "^1.5.0"
    duplexify "^3.4.2"
    end-of-stream "^1.1.0"
    flush-write-stream "^1.0.0"
    from2 "^2.1.0"
    parallel-transform "^1.1.0"
    pump "^3.0.0"
    pumpify "^1.3.3"
    stream-each "^1.1.0"
    through2 "^2.0.0"

mixin-deep@^1.2.0:
  version "1.3.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/mixin-deep/-/mixin-deep-1.3.2.tgz#1120b43dc359a785dce65b55b82e257ccf479566"
  integrity sha1-ESC0PcNZp4Xc5ltVuC4lfM9HlWY=
  dependencies:
    for-in "^1.0.2"
    is-extendable "^1.0.1"

mixin-object@^2.0.1:
  version "2.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/mixin-object/-/mixin-object-2.0.1.tgz#4fb949441dab182540f1fe035ba60e1947a5e57e"
  integrity sha1-T7lJRB2rGCVA8f4DW6YOGUel5X4=
  dependencies:
    for-in "^0.1.3"
    is-extendable "^0.1.1"

mkdirp@0.5.x, "mkdirp@>=0.5 0", mkdirp@^0.5.0, mkdirp@^0.5.1, mkdirp@~0.5.0:
  version "0.5.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/mkdirp/-/mkdirp-0.5.1.tgz#30057438eac6cf7f8c4767f38648d6697d75c903"
  integrity sha1-MAV0OOrGz3+MR2fzhkjWaX11yQM=
  dependencies:
    minimist "0.0.8"

move-concurrently@^1.0.1:
  version "1.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/move-concurrently/-/move-concurrently-1.0.1.tgz#be2c005fda32e0b29af1f05d7c4b33214c701f92"
  integrity sha1-viwAX9oy4LKa8fBdfEszIUxwH5I=
  dependencies:
    aproba "^1.1.1"
    copy-concurrently "^1.0.0"
    fs-write-stream-atomic "^1.0.8"
    mkdirp "^0.5.1"
    rimraf "^2.5.4"
    run-queue "^1.0.3"

ms@2.0.0:
  version "2.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/ms/-/ms-2.0.0.tgz#5608aeadfc00be6c2901df5f9861788de0d597c8"
  integrity sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=

ms@2.1.1:
  version "2.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/ms/-/ms-2.1.1.tgz#30a5864eb3ebb0a66f2ebe6d727af06a09d86e0a"
  integrity sha1-MKWGTrPrsKZvLr5tcnrwagnYbgo=

ms@^2.1.1:
  version "2.1.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/ms/-/ms-2.1.2.tgz#d09d1f357b443f493382a8eb3ccd183872ae6009"
  integrity sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=

multicast-dns-service-types@^1.1.0:
  version "1.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/multicast-dns-service-types/-/multicast-dns-service-types-1.1.0.tgz#899f11d9686e5e05cb91b35d5f0e63b773cfc901"
  integrity sha1-iZ8R2WhuXgXLkbNdXw5jt3PPyQE=

multicast-dns@^6.0.1:
  version "6.2.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/multicast-dns/-/multicast-dns-6.2.3.tgz#a0ec7bd9055c4282f790c3c82f4e28db3b31b229"
  integrity sha1-oOx72QVcQoL3kMPIL04o2zsxsik=
  dependencies:
    dns-packet "^1.3.1"
    thunky "^1.0.2"

mute-stream@0.0.7:
  version "0.0.7"
  resolved "https://nexus.lanmaoly.com/repository/npm/mute-stream/-/mute-stream-0.0.7.tgz#3075ce93bc21b8fab43e1bc4da7e8115ed1e7bab"
  integrity sha1-MHXOk7whuPq0PhvE2n6BFe0ee6s=

nan@^2.12.1, nan@^2.13.2:
  version "2.14.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/nan/-/nan-2.14.0.tgz#7818f722027b2459a86f0295d434d1fc2336c52c"
  integrity sha1-eBj3IgJ7JFmobwKV1DTR/CM2xSw=

nanomatch@^1.2.9:
  version "1.2.13"
  resolved "https://nexus.lanmaoly.com/repository/npm/nanomatch/-/nanomatch-1.2.13.tgz#b87a8aa4fc0de8fe6be88895b38983ff265bd119"
  integrity sha1-uHqKpPwN6P5r6IiVs4mD/yZb0Rk=
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    fragment-cache "^0.2.1"
    is-windows "^1.0.2"
    kind-of "^6.0.2"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

native-request@^1.0.5:
  version "1.0.8"
  resolved "https://r.cnpmjs.org/native-request/download/native-request-1.0.8.tgz#8f66bf606e0f7ea27c0e5995eb2f5d03e33ae6fb"
  integrity sha1-j2a/YG4PfqJ8DlmV6y9dA+M65vs=

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/natural-compare/-/natural-compare-1.4.0.tgz#4abebfeed7541f2c27acfb29bdbbd15c8d5ba4f7"
  integrity sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=

needle@^2.2.1:
  version "2.4.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/needle/-/needle-2.4.0.tgz#6833e74975c444642590e15a750288c5f939b57c"
  integrity sha1-aDPnSXXERGQlkOFadQKIxfk5tXw=
  dependencies:
    debug "^3.2.6"
    iconv-lite "^0.4.4"
    sax "^1.2.4"

negotiator@0.6.2:
  version "0.6.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/negotiator/-/negotiator-0.6.2.tgz#feacf7ccf525a77ae9634436a64883ffeca346fb"
  integrity sha1-/qz3zPUlp3rpY0Q2pkiD/+yjRvs=

neo-async@^2.5.0, neo-async@^2.6.0:
  version "2.6.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/neo-async/-/neo-async-2.6.1.tgz#ac27ada66167fa8849a6addd837f6b189ad2081c"
  integrity sha1-rCetpmFn+ohJpq3dg39rGJrSCBw=

nice-try@^1.0.4:
  version "1.0.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/nice-try/-/nice-try-1.0.5.tgz#a3378a7696ce7d223e88fc9b764bd7ef1089e366"
  integrity sha1-ozeKdpbOfSI+iPybdkvX7xCJ42Y=

no-case@^2.2.0:
  version "2.3.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/no-case/-/no-case-2.3.2.tgz#60b813396be39b3f1288a4c1ed5d1e7d28b464ac"
  integrity sha1-YLgTOWvjmz8SiKTB7V0efSi0ZKw=
  dependencies:
    lower-case "^1.1.1"

node-fetch@^1.0.1:
  version "1.7.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/node-fetch/-/node-fetch-1.7.3.tgz#980f6f72d85211a5347c6b2bc18c5b84c3eb47ef"
  integrity sha1-mA9vcthSEaU0fGsrwYxbhMPrR+8=
  dependencies:
    encoding "^0.1.11"
    is-stream "^1.0.1"

node-forge@0.7.5:
  version "0.7.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/node-forge/-/node-forge-0.7.5.tgz#6c152c345ce11c52f465c2abd957e8639cd674df"
  integrity sha1-bBUsNFzhHFL0ZcKr2VfoY5zWdN8=

node-gyp@^3.8.0:
  version "3.8.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/node-gyp/-/node-gyp-3.8.0.tgz#540304261c330e80d0d5edce253a68cb3964218c"
  integrity sha1-VAMEJhwzDoDQ1e3OJTpoyzlkIYw=
  dependencies:
    fstream "^1.0.0"
    glob "^7.0.3"
    graceful-fs "^4.1.2"
    mkdirp "^0.5.0"
    nopt "2 || 3"
    npmlog "0 || 1 || 2 || 3 || 4"
    osenv "0"
    request "^2.87.0"
    rimraf "2"
    semver "~5.3.0"
    tar "^2.0.0"
    which "1"

node-int64@^0.4.0:
  version "0.4.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/node-int64/-/node-int64-0.4.0.tgz#87a9065cdb355d3182d8f94ce11188b825c68a3b"
  integrity sha1-h6kGXNs1XTGC2PlM4RGIuCXGijs=

node-libs-browser@^2.0.0:
  version "2.2.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/node-libs-browser/-/node-libs-browser-2.2.1.tgz#b64f513d18338625f90346d27b0d235e631f6425"
  integrity sha1-tk9RPRgzhiX5A0bSew0jXmMfZCU=
  dependencies:
    assert "^1.1.1"
    browserify-zlib "^0.2.0"
    buffer "^4.3.0"
    console-browserify "^1.1.0"
    constants-browserify "^1.0.0"
    crypto-browserify "^3.11.0"
    domain-browser "^1.1.1"
    events "^3.0.0"
    https-browserify "^1.0.0"
    os-browserify "^0.3.0"
    path-browserify "0.0.1"
    process "^0.11.10"
    punycode "^1.2.4"
    querystring-es3 "^0.2.0"
    readable-stream "^2.3.3"
    stream-browserify "^2.0.1"
    stream-http "^2.7.2"
    string_decoder "^1.0.0"
    timers-browserify "^2.0.4"
    tty-browserify "0.0.0"
    url "^0.11.0"
    util "^0.11.0"
    vm-browserify "^1.0.1"

node-notifier@^5.2.1:
  version "5.4.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/node-notifier/-/node-notifier-5.4.0.tgz#7b455fdce9f7de0c63538297354f3db468426e6a"
  integrity sha1-e0Vf3On33gxjU4KXNU89tGhCbmo=
  dependencies:
    growly "^1.3.0"
    is-wsl "^1.1.0"
    semver "^5.5.0"
    shellwords "^0.1.1"
    which "^1.3.0"

node-pre-gyp@^0.12.0:
  version "0.12.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/node-pre-gyp/-/node-pre-gyp-0.12.0.tgz#39ba4bb1439da030295f899e3b520b7785766149"
  integrity sha1-ObpLsUOdoDApX4meO1ILd4V2YUk=
  dependencies:
    detect-libc "^1.0.2"
    mkdirp "^0.5.1"
    needle "^2.2.1"
    nopt "^4.0.1"
    npm-packlist "^1.1.6"
    npmlog "^4.0.2"
    rc "^1.2.7"
    rimraf "^2.6.1"
    semver "^5.3.0"
    tar "^4"

node-releases@^1.1.25:
  version "1.1.26"
  resolved "https://nexus.lanmaoly.com/repository/npm/node-releases/-/node-releases-1.1.26.tgz#f30563edc5c7dc20cf524cc8652ffa7be0762937"
  integrity sha1-8wVj7cXH3CDPUkzIZS/6e+B2KTc=
  dependencies:
    semver "^5.3.0"

node-sass@^4.14.1:
  version "4.14.1"
  resolved "https://r.cnpmjs.org/node-sass/download/node-sass-4.14.1.tgz#99c87ec2efb7047ed638fb4c9db7f3a42e2217b5"
  integrity sha1-mch+wu+3BH7WOPtMnbfzpC4iF7U=
  dependencies:
    async-foreach "^0.1.3"
    chalk "^1.1.1"
    cross-spawn "^3.0.0"
    gaze "^1.0.0"
    get-stdin "^4.0.1"
    glob "^7.0.3"
    in-publish "^2.0.0"
    lodash "^4.17.15"
    meow "^3.7.0"
    mkdirp "^0.5.1"
    nan "^2.13.2"
    node-gyp "^3.8.0"
    npmlog "^4.0.0"
    request "^2.88.0"
    sass-graph "2.2.5"
    stdout-stream "^1.4.0"
    "true-case-path" "^1.0.2"

"nopt@2 || 3":
  version "3.0.6"
  resolved "https://nexus.lanmaoly.com/repository/npm/nopt/-/nopt-3.0.6.tgz#c6465dbf08abcd4db359317f79ac68a646b28ff9"
  integrity sha1-xkZdvwirzU2zWTF/eaxopkayj/k=
  dependencies:
    abbrev "1"

nopt@^4.0.1:
  version "4.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/nopt/-/nopt-4.0.1.tgz#d0d4685afd5415193c8c7505602d0d17cd64474d"
  integrity sha1-0NRoWv1UFRk8jHUFYC0NF81kR00=
  dependencies:
    abbrev "1"
    osenv "^0.1.4"

normalize-package-data@^2.3.2, normalize-package-data@^2.3.4:
  version "2.5.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/normalize-package-data/-/normalize-package-data-2.5.0.tgz#e66db1838b200c1dfc233225d12cb36520e234a8"
  integrity sha1-5m2xg4sgDB38IzIl0SyzZSDiNKg=
  dependencies:
    hosted-git-info "^2.1.4"
    resolve "^1.10.0"
    semver "2 || 3 || 4 || 5"
    validate-npm-package-license "^3.0.1"

normalize-path@^2.0.1, normalize-path@^2.1.1:
  version "2.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/normalize-path/-/normalize-path-2.1.1.tgz#1ab28b556e198363a8c1a6f7e6fa20137fe6aed9"
  integrity sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=
  dependencies:
    remove-trailing-separator "^1.0.1"

normalize-path@^3.0.0:
  version "3.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/normalize-path/-/normalize-path-3.0.0.tgz#0dcd69ff23a1c9b11fd0978316644a0388216a65"
  integrity sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=

normalize-range@^0.1.2:
  version "0.1.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/normalize-range/-/normalize-range-0.1.2.tgz#2d10c06bdfd312ea9777695a4d28439456b75942"
  integrity sha1-LRDAa9/TEuqXd2laTShDlFa3WUI=

normalize.css@^7.0.0:
  version "7.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/normalize.css/-/normalize.css-7.0.0.tgz#abfb1dd82470674e0322b53ceb1aaf412938e4bf"
  integrity sha1-q/sd2CRwZ04DIrU86xqvQSk45L8=

npm-bundled@^1.0.1:
  version "1.0.6"
  resolved "https://nexus.lanmaoly.com/repository/npm/npm-bundled/-/npm-bundled-1.0.6.tgz#e7ba9aadcef962bb61248f91721cd932b3fe6bdd"
  integrity sha1-57qarc75YrthJI+RchzZMrP+a90=

npm-packlist@^1.1.6:
  version "1.4.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/npm-packlist/-/npm-packlist-1.4.4.tgz#866224233850ac534b63d1a6e76050092b5d2f44"
  integrity sha1-hmIkIzhQrFNLY9Gm52BQCStdL0Q=
  dependencies:
    ignore-walk "^3.0.1"
    npm-bundled "^1.0.1"

npm-run-all@^4.1.3:
  version "4.1.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/npm-run-all/-/npm-run-all-4.1.5.tgz#04476202a15ee0e2e214080861bff12a51d98fba"
  integrity sha1-BEdiAqFe4OLiFAgIYb/xKlHZj7o=
  dependencies:
    ansi-styles "^3.2.1"
    chalk "^2.4.1"
    cross-spawn "^6.0.5"
    memorystream "^0.3.1"
    minimatch "^3.0.4"
    pidtree "^0.3.0"
    read-pkg "^3.0.0"
    shell-quote "^1.6.1"
    string.prototype.padend "^3.0.0"

npm-run-path@^2.0.0:
  version "2.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/npm-run-path/-/npm-run-path-2.0.2.tgz#35a9232dfa35d7067b4cb2ddf2357b1871536c5f"
  integrity sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8=
  dependencies:
    path-key "^2.0.0"

"npmlog@0 || 1 || 2 || 3 || 4", npmlog@^4.0.0, npmlog@^4.0.2:
  version "4.1.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/npmlog/-/npmlog-4.1.2.tgz#08a7f2a8bf734604779a9efa4ad5cc717abb954b"
  integrity sha1-CKfyqL9zRgR3mp76StXMcXq7lUs=
  dependencies:
    are-we-there-yet "~1.1.2"
    console-control-strings "~1.1.0"
    gauge "~2.7.3"
    set-blocking "~2.0.0"

nth-check@~1.0.1:
  version "1.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/nth-check/-/nth-check-1.0.2.tgz#b2bd295c37e3dd58a3bf0700376663ba4d9cf05c"
  integrity sha1-sr0pXDfj3VijvwcAN2Zjuk2c8Fw=
  dependencies:
    boolbase "~1.0.0"

num2fraction@^1.2.2:
  version "1.2.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/num2fraction/-/num2fraction-1.2.2.tgz#6f682b6a027a4e9ddfa4564cd2589d1d4e669ede"
  integrity sha1-b2gragJ6Tp3fpFZM0lidHU5mnt4=

number-is-nan@^1.0.0:
  version "1.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/number-is-nan/-/number-is-nan-1.0.1.tgz#097b602b53422a522c1afb8790318336941a011d"
  integrity sha1-CXtgK1NCKlIsGvuHkDGDNpQaAR0=

nwsapi@^2.0.7:
  version "2.0.9"
  resolved "https://nexus.lanmaoly.com/repository/npm/nwsapi/-/nwsapi-2.0.9.tgz#77ac0cdfdcad52b6a1151a84e73254edc33ed016"
  integrity sha1-d6wM39ytUrahFRqE5zJU7cM+0BY=

oauth-sign@~0.9.0:
  version "0.9.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/oauth-sign/-/oauth-sign-0.9.0.tgz#47a7b016baa68b5fa0ecf3dee08a85c679ac6455"
  integrity sha1-R6ewFrqmi1+g7PPe4IqFxnmsZFU=

object-assign@4.x, object-assign@^4.0.1, object-assign@^4.1.0, object-assign@^4.1.1:
  version "4.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/object-assign/-/object-assign-4.1.1.tgz#2109adc7965887cfc05cbbd442cac8bfbb360863"
  integrity sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=

object-copy@^0.1.0:
  version "0.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/object-copy/-/object-copy-0.1.0.tgz#7e7d858b781bd7c991a41ba975ed3812754e998c"
  integrity sha1-fn2Fi3gb18mRpBupde04EnVOmYw=
  dependencies:
    copy-descriptor "^0.1.0"
    define-property "^0.2.5"
    kind-of "^3.0.3"

object-keys@^1.0.11, object-keys@^1.0.12:
  version "1.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/object-keys/-/object-keys-1.1.1.tgz#1c47f272df277f3b1daf061677d9c82e2322c60e"
  integrity sha1-HEfyct8nfzsdrwYWd9nILiMixg4=

object-unfreeze@^1.1.0:
  version "1.1.0"
  resolved "https://r.cnpmjs.org/object-unfreeze/download/object-unfreeze-1.1.0.tgz#69628bea1f3c9d29f4eb0ba63b38002d70ea3ce9"
  integrity sha1-aWKL6h88nSn06wumOzgALXDqPOk=

object-visit@^1.0.0:
  version "1.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/object-visit/-/object-visit-1.0.1.tgz#f79c4493af0c5377b59fe39d395e41042dd045bb"
  integrity sha1-95xEk68MU3e1n+OdOV5BBC3QRbs=
  dependencies:
    isobject "^3.0.0"

object.assign@^4.1.0:
  version "4.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/object.assign/-/object.assign-4.1.0.tgz#968bf1100d7956bb3ca086f006f846b3bc4008da"
  integrity sha1-lovxEA15Vrs8oIbwBvhGs7xACNo=
  dependencies:
    define-properties "^1.1.2"
    function-bind "^1.1.1"
    has-symbols "^1.0.0"
    object-keys "^1.0.11"

object.entries@^1.1.0:
  version "1.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/object.entries/-/object.entries-1.1.0.tgz#2024fc6d6ba246aee38bdb0ffd5cfbcf371b7519"
  integrity sha1-ICT8bWuiRq7ji9sP/Vz7zzcbdRk=
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.12.0"
    function-bind "^1.1.1"
    has "^1.0.3"

object.fromentries@^2.0.0:
  version "2.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/object.fromentries/-/object.fromentries-2.0.0.tgz#49a543d92151f8277b3ac9600f1e930b189d30ab"
  integrity sha1-SaVD2SFR+Cd7OslgDx6TCxidMKs=
  dependencies:
    define-properties "^1.1.2"
    es-abstract "^1.11.0"
    function-bind "^1.1.1"
    has "^1.0.1"

object.getownpropertydescriptors@^2.0.3:
  version "2.0.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/object.getownpropertydescriptors/-/object.getownpropertydescriptors-2.0.3.tgz#8758c846f5b407adab0f236e0986f14b051caa16"
  integrity sha1-h1jIRvW0B62rDyNuCYbxSwUcqhY=
  dependencies:
    define-properties "^1.1.2"
    es-abstract "^1.5.1"

object.omit@^2.0.0:
  version "2.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/object.omit/-/object.omit-2.0.1.tgz#1a9c744829f39dbb858c76ca3579ae2a54ebd1fa"
  integrity sha1-Gpx0SCnznbuFjHbKNXmuKlTr0fo=
  dependencies:
    for-own "^0.1.4"
    is-extendable "^0.1.1"

object.pick@^1.3.0:
  version "1.3.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/object.pick/-/object.pick-1.3.0.tgz#87a10ac4c1694bd2e1cbf53591a66141fb5dd747"
  integrity sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c=
  dependencies:
    isobject "^3.0.1"

obuf@^1.0.0, obuf@^1.1.2:
  version "1.1.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/obuf/-/obuf-1.1.2.tgz#09bea3343d41859ebd446292d11c9d4db619084e"
  integrity sha1-Cb6jND1BhZ69RGKS0RydTbYZCE4=

old-env-map@^0.0.5:
  version "0.0.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/old-env-map/-/old-env-map-0.0.5.tgz#053c22b302bf43ba0040f986d9966dde361ed1b9"
  integrity sha512-MJQGQL25UJFC5dXhB4RuPqyym+tH2DQ2xa2A61LB8NUlPEk27rR1K6Yu+JQiMSMgqbBib0J2W4sMIEt2xshIGg==

old-fetch@^0.0.17:
  version "0.0.17"
  resolved "https://nexus.lanmaoly.com/repository/npm/old-fetch/-/old-fetch-0.0.17.tgz#73662bf45f7e4102e5292f660ddbcf700b15d259"
  integrity sha512-saZm/ptppfo3+EVU/R2LCU2IGWik9jRzSGpJhWkMzlLq+HIzhAHQd4rXJs7nnyjGtEe+1euzthghoU0GA/4kKg==
  dependencies:
    axios "0.16.2"
    querystring "0.2.0"

on-finished@~2.3.0:
  version "2.3.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/on-finished/-/on-finished-2.3.0.tgz#20f1336481b083cd75337992a16971aa2d906947"
  integrity sha1-IPEzZIGwg811M3mSoWlxqi2QaUc=
  dependencies:
    ee-first "1.1.1"

on-headers@~1.0.2:
  version "1.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/on-headers/-/on-headers-1.0.2.tgz#772b0ae6aaa525c399e489adfad90c403eb3c28f"
  integrity sha1-dysK5qqlJcOZ5Imt+tkMQD6zwo8=

once@^1.3.0, once@^1.3.1, once@^1.4.0:
  version "1.4.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/once/-/once-1.4.0.tgz#583b1aa775961d4b113ac17d9c50baef9dd76bd1"
  integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
  dependencies:
    wrappy "1"

onetime@^2.0.0:
  version "2.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/onetime/-/onetime-2.0.1.tgz#067428230fd67443b2794b22bba528b6867962d4"
  integrity sha1-BnQoIw/WdEOyeUsiu6UotoZ5YtQ=
  dependencies:
    mimic-fn "^1.0.0"

opener@^1.5.1:
  version "1.5.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/opener/-/opener-1.5.1.tgz#6d2f0e77f1a0af0032aca716c2c1fbb8e7e8abed"
  integrity sha1-bS8Od/GgrwAyrKcWwsH7uOfoq+0=

opn@^5.5.0:
  version "5.5.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/opn/-/opn-5.5.0.tgz#fc7164fab56d235904c51c3b27da6758ca3b9bfc"
  integrity sha1-/HFk+rVtI1kExRw7J9pnWMo7m/w=
  dependencies:
    is-wsl "^1.1.0"

optimist@^0.6.1:
  version "0.6.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/optimist/-/optimist-0.6.1.tgz#da3ea74686fa21a19a111c326e90eb15a0196686"
  integrity sha1-2j6nRob6IaGaERwybpDrFaAZZoY=
  dependencies:
    minimist "~0.0.1"
    wordwrap "~0.0.2"

optionator@^0.8.1, optionator@^0.8.2:
  version "0.8.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/optionator/-/optionator-0.8.2.tgz#364c5e409d3f4d6301d6c0b4c05bba50180aeb64"
  integrity sha1-NkxeQJ0/TWMB1sC0wFu6UBgK62Q=
  dependencies:
    deep-is "~0.1.3"
    fast-levenshtein "~2.0.4"
    levn "~0.3.0"
    prelude-ls "~1.1.2"
    type-check "~0.3.2"
    wordwrap "~1.0.0"

original@^1.0.0:
  version "1.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/original/-/original-1.0.2.tgz#e442a61cffe1c5fd20a65f3261c26663b303f25f"
  integrity sha1-5EKmHP/hxf0gpl8yYcJmY7MD8l8=
  dependencies:
    url-parse "^1.4.3"

os-browserify@^0.3.0:
  version "0.3.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/os-browserify/-/os-browserify-0.3.0.tgz#854373c7f5c2315914fc9bfc6bd8238fdda1ec27"
  integrity sha1-hUNzx/XCMVkU/Jv8a9gjj92h7Cc=

os-homedir@^1.0.0:
  version "1.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/os-homedir/-/os-homedir-1.0.2.tgz#ffbc4988336e0e833de0c168c7ef152121aa7fb3"
  integrity sha1-/7xJiDNuDoM94MFox+8VISGqf7M=

os-locale@^2.0.0:
  version "2.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/os-locale/-/os-locale-2.1.0.tgz#42bc2900a6b5b8bd17376c8e882b65afccf24bf2"
  integrity sha1-QrwpAKa1uL0XN2yOiCtlr8zyS/I=
  dependencies:
    execa "^0.7.0"
    lcid "^1.0.0"
    mem "^1.1.0"

os-locale@^3.0.0, os-locale@^3.1.0:
  version "3.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/os-locale/-/os-locale-3.1.0.tgz#a802a6ee17f24c10483ab9935719cef4ed16bf1a"
  integrity sha1-qAKm7hfyTBBIOrmTVxnO9O0Wvxo=
  dependencies:
    execa "^1.0.0"
    lcid "^2.0.0"
    mem "^4.0.0"

os-tmpdir@^1.0.0, os-tmpdir@^1.0.1, os-tmpdir@~1.0.2:
  version "1.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/os-tmpdir/-/os-tmpdir-1.0.2.tgz#bbe67406c79aa85c5cfec766fe5734555dfa1274"
  integrity sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=

osenv@0, osenv@^0.1.4:
  version "0.1.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/osenv/-/osenv-0.1.5.tgz#85cdfafaeb28e8677f416e287592b5f3f49ea410"
  integrity sha1-hc36+uso6Gd/QW4odZK18/SepBA=
  dependencies:
    os-homedir "^1.0.0"
    os-tmpdir "^1.0.0"

p-defer@^1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/p-defer/-/p-defer-1.0.0.tgz#9f6eb182f6c9aa8cd743004a7d4f96b196b0fb0c"
  integrity sha1-n26xgvbJqozXQwBKfU+WsZaw+ww=

p-finally@^1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/p-finally/-/p-finally-1.0.0.tgz#3fbcfb15b899a44123b34b6dcc18b724336a2cae"
  integrity sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4=

p-is-promise@^2.0.0:
  version "2.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/p-is-promise/-/p-is-promise-2.1.0.tgz#918cebaea248a62cf7ffab8e3bca8c5f882fc42e"
  integrity sha1-kYzrrqJIpiz3/6uOO8qMX4gvxC4=

p-limit@^1.0.0, p-limit@^1.1.0:
  version "1.3.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/p-limit/-/p-limit-1.3.0.tgz#b86bd5f0c25690911c7590fcbfc2010d54b3ccb8"
  integrity sha1-uGvV8MJWkJEcdZD8v8IBDVSzzLg=
  dependencies:
    p-try "^1.0.0"

p-limit@^2.0.0:
  version "2.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/p-limit/-/p-limit-2.2.0.tgz#417c9941e6027a9abcba5092dd2904e255b5fbc2"
  integrity sha1-QXyZQeYCepq8ulCS3SkE4lW1+8I=
  dependencies:
    p-try "^2.0.0"

p-locate@^2.0.0:
  version "2.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/p-locate/-/p-locate-2.0.0.tgz#20a0103b222a70c8fd39cc2e580680f3dde5ec43"
  integrity sha1-IKAQOyIqcMj9OcwuWAaA893l7EM=
  dependencies:
    p-limit "^1.1.0"

p-locate@^3.0.0:
  version "3.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/p-locate/-/p-locate-3.0.0.tgz#322d69a05c0264b25997d9f40cd8a891ab0064a4"
  integrity sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ=
  dependencies:
    p-limit "^2.0.0"

p-map@^2.0.0:
  version "2.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/p-map/-/p-map-2.1.0.tgz#310928feef9c9ecc65b68b17693018a665cea175"
  integrity sha1-MQko/u+cnsxltosXaTAYpmXOoXU=

p-retry@^3.0.1:
  version "3.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/p-retry/-/p-retry-3.0.1.tgz#316b4c8893e2c8dc1cfa891f406c4b422bebf328"
  integrity sha1-MWtMiJPiyNwc+okfQGxLQivr8yg=
  dependencies:
    retry "^0.12.0"

p-try@^1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/p-try/-/p-try-1.0.0.tgz#cbc79cdbaf8fd4228e13f621f2b1a237c1b207b3"
  integrity sha1-y8ec26+P1CKOE/Yh8rGiN8GyB7M=

p-try@^2.0.0:
  version "2.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/p-try/-/p-try-2.2.0.tgz#cb2868540e313d61de58fafbe35ce9004d5540e6"
  integrity sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=

pako@~1.0.5:
  version "1.0.10"
  resolved "https://nexus.lanmaoly.com/repository/npm/pako/-/pako-1.0.10.tgz#4328badb5086a426aa90f541977d4955da5c9732"
  integrity sha1-Qyi621CGpCaqkPVBl31JVdpclzI=

parallel-transform@^1.1.0:
  version "1.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/parallel-transform/-/parallel-transform-1.1.0.tgz#d410f065b05da23081fcd10f28854c29bda33b06"
  integrity sha1-1BDwZbBdojCB/NEPKIVMKb2jOwY=
  dependencies:
    cyclist "~0.2.2"
    inherits "^2.0.3"
    readable-stream "^2.1.5"

param-case@2.1.x:
  version "2.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/param-case/-/param-case-2.1.1.tgz#df94fd8cf6531ecf75e6bef9a0858fbc72be2247"
  integrity sha1-35T9jPZTHs915r75oIWPvHK+Ikc=
  dependencies:
    no-case "^2.2.0"

parent-module@^1.0.0:
  version "1.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/parent-module/-/parent-module-1.0.1.tgz#691d2709e78c79fae3a156622452d00762caaaa2"
  integrity sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=
  dependencies:
    callsites "^3.0.0"

parse-asn1@^5.0.0:
  version "5.1.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/parse-asn1/-/parse-asn1-5.1.4.tgz#37f6628f823fbdeb2273b4d540434a22f3ef1fcc"
  integrity sha1-N/Zij4I/vesic7TVQENKIvPvH8w=
  dependencies:
    asn1.js "^4.0.0"
    browserify-aes "^1.0.0"
    create-hash "^1.1.0"
    evp_bytestokey "^1.0.0"
    pbkdf2 "^3.0.3"
    safe-buffer "^5.1.1"

parse-glob@^3.0.4:
  version "3.0.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/parse-glob/-/parse-glob-3.0.4.tgz#b2c376cfb11f35513badd173ef0bb6e3a388391c"
  integrity sha1-ssN2z7EfNVE7rdFz7wu246OIORw=
  dependencies:
    glob-base "^0.3.0"
    is-dotfile "^1.0.0"
    is-extglob "^1.0.0"
    is-glob "^2.0.0"

parse-json@^2.2.0:
  version "2.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/parse-json/-/parse-json-2.2.0.tgz#f480f40434ef80741f8469099f8dea18f55a4dc9"
  integrity sha1-9ID0BDTvgHQfhGkJn43qGPVaTck=
  dependencies:
    error-ex "^1.2.0"

parse-json@^4.0.0:
  version "4.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/parse-json/-/parse-json-4.0.0.tgz#be35f5425be1f7f6c747184f98a788cb99477ee0"
  integrity sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA=
  dependencies:
    error-ex "^1.3.1"
    json-parse-better-errors "^1.0.1"

parse-passwd@^1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/parse-passwd/-/parse-passwd-1.0.0.tgz#6d5b934a456993b23d37f40a382d6f1666a8e5c6"
  integrity sha1-bVuTSkVpk7I9N/QKOC1vFmao5cY=

parse5@4.0.0:
  version "4.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/parse5/-/parse5-4.0.0.tgz#6d78656e3da8d78b4ec0b906f7c08ef1dfe3f608"
  integrity sha1-bXhlbj2o14tOwLkG98CO8d/j9gg=

parseurl@~1.3.2, parseurl@~1.3.3:
  version "1.3.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/parseurl/-/parseurl-1.3.3.tgz#9da19e7bee8d12dff0513ed5b76957793bc2e8d4"
  integrity sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ=

pascalcase@^0.1.1:
  version "0.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/pascalcase/-/pascalcase-0.1.1.tgz#b363e55e8006ca6fe21784d2db22bd15d7917f14"
  integrity sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ=

path-browserify@0.0.1:
  version "0.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/path-browserify/-/path-browserify-0.0.1.tgz#e6c4ddd7ed3aa27c68a20cc4e50e1a4ee83bbc4a"
  integrity sha1-5sTd1+06onxoogzE5Q4aTug7vEo=

path-dirname@^1.0.0:
  version "1.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/path-dirname/-/path-dirname-1.0.2.tgz#cc33d24d525e099a5388c0336c6e32b9160609e0"
  integrity sha1-zDPSTVJeCZpTiMAzbG4yuRYGCeA=

path-exists@^2.0.0:
  version "2.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/path-exists/-/path-exists-2.1.0.tgz#0feb6c64f0fc518d9a754dd5efb62c7022761f4b"
  integrity sha1-D+tsZPD8UY2adU3V77YscCJ2H0s=
  dependencies:
    pinkie-promise "^2.0.0"

path-exists@^3.0.0:
  version "3.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/path-exists/-/path-exists-3.0.0.tgz#ce0ebeaa5f78cb18925ea7d810d7b59b010fd515"
  integrity sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=

path-is-absolute@^1.0.0, path-is-absolute@^1.0.1:
  version "1.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/path-is-absolute/-/path-is-absolute-1.0.1.tgz#174b9268735534ffbc7ace6bf53a5a9e1b5c5f5f"
  integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=

path-is-inside@^1.0.2:
  version "1.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/path-is-inside/-/path-is-inside-1.0.2.tgz#365417dede44430d1c11af61027facf074bdfc53"
  integrity sha1-NlQX3t5EQw0cEa9hAn+s8HS9/FM=

path-key@^2.0.0, path-key@^2.0.1:
  version "2.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/path-key/-/path-key-2.0.1.tgz#411cadb574c5a140d3a4b1910d40d80cc9f40b40"
  integrity sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A=

path-parse@^1.0.5, path-parse@^1.0.6:
  version "1.0.6"
  resolved "https://nexus.lanmaoly.com/repository/npm/path-parse/-/path-parse-1.0.6.tgz#d62dbb5679405d72c4737ec58600e9ddcf06d24c"
  integrity sha1-1i27VnlAXXLEc37FhgDp3c8G0kw=

path-to-regexp@0.1.7:
  version "0.1.7"
  resolved "https://nexus.lanmaoly.com/repository/npm/path-to-regexp/-/path-to-regexp-0.1.7.tgz#df604178005f522f15eb4490e7247a1bfaa67f8c"
  integrity sha1-32BBeABfUi8V60SQ5yR6G/qmf4w=

path-to-regexp@^1.7.0:
  version "1.7.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/path-to-regexp/-/path-to-regexp-1.7.0.tgz#59fde0f435badacba103a84e9d3bc64e96b9937d"
  integrity sha1-Wf3g9DW62suhA6hOnTvGTpa5k30=
  dependencies:
    isarray "0.0.1"

path-type@^1.0.0:
  version "1.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/path-type/-/path-type-1.1.0.tgz#59c44f7ee491da704da415da5a4070ba4f8fe441"
  integrity sha1-WcRPfuSR2nBNpBXaWkBwuk+P5EE=
  dependencies:
    graceful-fs "^4.1.2"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"

path-type@^2.0.0:
  version "2.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/path-type/-/path-type-2.0.0.tgz#f012ccb8415b7096fc2daa1054c3d72389594c73"
  integrity sha1-8BLMuEFbcJb8LaoQVMPXI4lZTHM=
  dependencies:
    pify "^2.0.0"

path-type@^3.0.0:
  version "3.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/path-type/-/path-type-3.0.0.tgz#cef31dc8e0a1a3bb0d105c0cd97cf3bf47f4e36f"
  integrity sha1-zvMdyOCho7sNEFwM2Xzzv0f0428=
  dependencies:
    pify "^3.0.0"

pbkdf2@^3.0.3:
  version "3.0.17"
  resolved "https://nexus.lanmaoly.com/repository/npm/pbkdf2/-/pbkdf2-3.0.17.tgz#976c206530617b14ebb32114239f7b09336e93a6"
  integrity sha1-l2wgZTBhexTrsyEUI597CTNuk6Y=
  dependencies:
    create-hash "^1.1.2"
    create-hmac "^1.1.4"
    ripemd160 "^2.0.1"
    safe-buffer "^5.0.1"
    sha.js "^2.4.8"

pdfjs-dist@2.6.347:
  version "2.6.347"
  resolved "https://r.cnpmjs.org/pdfjs-dist/download/pdfjs-dist-2.6.347.tgz#f257ed66e83be900cd0fd28524a2187fb9e25cd5"
  integrity sha1-8lftZug76QDND9KFJKIYf7niXNU=

pdfjs-dist@^2.0.550:
  version "2.7.570"
  resolved "https://r.cnpmjs.org/pdfjs-dist/download/pdfjs-dist-2.7.570.tgz#7233241a2437ac22387656099b6e549d032f0b35"
  integrity sha1-cjMkGiQ3rCI4dlYJm25UnQMvCzU=

performance-now@^2.1.0:
  version "2.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/performance-now/-/performance-now-2.1.0.tgz#6309f4e0e5fa913ec1c69307ae364b4b377c9e7b"
  integrity sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns=

pidtree@^0.3.0:
  version "0.3.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/pidtree/-/pidtree-0.3.0.tgz#f6fada10fccc9f99bf50e90d0b23d72c9ebc2e6b"
  integrity sha1-9vraEPzMn5m/UOkNCyPXLJ68Lms=

pify@^2.0.0:
  version "2.3.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/pify/-/pify-2.3.0.tgz#ed141a6ac043a849ea588498e7dca8b15330e90c"
  integrity sha1-7RQaasBDqEnqWISY59yosVMw6Qw=

pify@^3.0.0:
  version "3.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/pify/-/pify-3.0.0.tgz#e5a4acd2c101fdf3d9a4d07f0dbc4db49dd28176"
  integrity sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=

pify@^4.0.1:
  version "4.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/pify/-/pify-4.0.1.tgz#4b2cd25c50d598735c50292224fd8c6df41e3231"
  integrity sha1-SyzSXFDVmHNcUCkiJP2MbfQeMjE=

pinkie-promise@^2.0.0:
  version "2.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/pinkie-promise/-/pinkie-promise-2.0.1.tgz#2135d6dfa7a358c069ac9b178776288228450ffa"
  integrity sha1-ITXW36ejWMBprJsXh3YogihFD/o=
  dependencies:
    pinkie "^2.0.0"

pinkie@^2.0.0:
  version "2.0.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/pinkie/-/pinkie-2.0.4.tgz#72556b80cfa0d48a974e80e77248e80ed4f7f870"
  integrity sha1-clVrgM+g1IqXToDnckjoDtT3+HA=

pkg-dir@^2.0.0:
  version "2.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/pkg-dir/-/pkg-dir-2.0.0.tgz#f6d5d1109e19d63edf428e0bd57e12777615334b"
  integrity sha1-9tXREJ4Z1j7fQo4L1X4Sd3YVM0s=
  dependencies:
    find-up "^2.1.0"

pkg-dir@^3.0.0:
  version "3.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/pkg-dir/-/pkg-dir-3.0.0.tgz#2749020f239ed990881b1f71210d51eb6523bea3"
  integrity sha1-J0kCDyOe2ZCIGx9xIQ1R62UjvqM=
  dependencies:
    find-up "^3.0.0"

pkg-up@^2.0.0:
  version "2.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/pkg-up/-/pkg-up-2.0.0.tgz#c819ac728059a461cab1c3889a2be3c49a004d7f"
  integrity sha1-yBmscoBZpGHKscOImivjxJoATX8=
  dependencies:
    find-up "^2.1.0"

pn@^1.1.0:
  version "1.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/pn/-/pn-1.1.0.tgz#e2f4cef0e219f463c179ab37463e4e1ecdccbafb"
  integrity sha1-4vTO8OIZ9GPBeas3Rj5OHs3Muvs=

portfinder@^1.0.20:
  version "1.0.21"
  resolved "https://nexus.lanmaoly.com/repository/npm/portfinder/-/portfinder-1.0.21.tgz#60e1397b95ac170749db70034ece306b9a27e324"
  integrity sha1-YOE5e5WsFwdJ23ADTs4wa5on4yQ=
  dependencies:
    async "^1.5.2"
    debug "^2.2.0"
    mkdirp "0.5.x"

posix-character-classes@^0.1.0:
  version "0.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/posix-character-classes/-/posix-character-classes-0.1.1.tgz#01eac0fe3b5af71a2a6c02feabb8c1fef7e00eab"
  integrity sha1-AerA/jta9xoqbAL+q7jB/vfgDqs=

post-compile-webpack-plugin@^0.1.2:
  version "0.1.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/post-compile-webpack-plugin/-/post-compile-webpack-plugin-0.1.2.tgz#0f6e780a4dff2e5f59f207f903a8d5f0476c842e"
  integrity sha1-D254Ck3/Ll9Z8gf5A6jV8EdshC4=

postcss-load-config@^2.0.0:
  version "2.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/postcss-load-config/-/postcss-load-config-2.1.0.tgz#c84d692b7bb7b41ddced94ee62e8ab31b417b003"
  integrity sha1-yE1pK3u3tB3c7ZTuYuirMbQXsAM=
  dependencies:
    cosmiconfig "^5.0.0"
    import-cwd "^2.0.0"

postcss-loader@^3.0.0:
  version "3.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/postcss-loader/-/postcss-loader-3.0.0.tgz#6b97943e47c72d845fa9e03f273773d4e8dd6c2d"
  integrity sha1-a5eUPkfHLYRfqeA/Jzdz1OjdbC0=
  dependencies:
    loader-utils "^1.1.0"
    postcss "^7.0.0"
    postcss-load-config "^2.0.0"
    schema-utils "^1.0.0"

postcss-modules-extract-imports@^1.2.0:
  version "1.2.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/postcss-modules-extract-imports/-/postcss-modules-extract-imports-1.2.1.tgz#dc87e34148ec7eab5f791f7cd5849833375b741a"
  integrity sha1-3IfjQUjsfqtfeR981YSYMzdbdBo=
  dependencies:
    postcss "^6.0.1"

postcss-modules-local-by-default@^1.2.0:
  version "1.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/postcss-modules-local-by-default/-/postcss-modules-local-by-default-1.2.0.tgz#f7d80c398c5a393fa7964466bd19500a7d61c069"
  integrity sha1-99gMOYxaOT+nlkRmvRlQCn1hwGk=
  dependencies:
    css-selector-tokenizer "^0.7.0"
    postcss "^6.0.1"

postcss-modules-scope@^1.1.0:
  version "1.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/postcss-modules-scope/-/postcss-modules-scope-1.1.0.tgz#d6ea64994c79f97b62a72b426fbe6056a194bb90"
  integrity sha1-1upkmUx5+XtipytCb75gVqGUu5A=
  dependencies:
    css-selector-tokenizer "^0.7.0"
    postcss "^6.0.1"

postcss-modules-values@^1.3.0:
  version "1.3.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/postcss-modules-values/-/postcss-modules-values-1.3.0.tgz#ecffa9d7e192518389f42ad0e83f72aec456ea20"
  integrity sha1-7P+p1+GSUYOJ9CrQ6D9yrsRW6iA=
  dependencies:
    icss-replace-symbols "^1.1.0"
    postcss "^6.0.1"

postcss-value-parser@^3.2.3, postcss-value-parser@^3.3.0:
  version "3.3.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/postcss-value-parser/-/postcss-value-parser-3.3.1.tgz#9ff822547e2893213cf1c30efa51ac5fd1ba8281"
  integrity sha1-n/giVH4okyE88cMO+lGsX9G6goE=

postcss@^6.0.1, postcss@^6.0.23:
  version "6.0.23"
  resolved "https://nexus.lanmaoly.com/repository/npm/postcss/-/postcss-6.0.23.tgz#61c82cc328ac60e677645f979054eb98bc0e3324"
  integrity sha1-YcgswyisYOZ3ZF+XkFTrmLwOMyQ=
  dependencies:
    chalk "^2.4.1"
    source-map "^0.6.1"
    supports-color "^5.4.0"

postcss@^7.0.0, postcss@^7.0.2:
  version "7.0.17"
  resolved "https://nexus.lanmaoly.com/repository/npm/postcss/-/postcss-7.0.17.tgz#4da1bdff5322d4a0acaab4d87f3e782436bad31f"
  integrity sha1-TaG9/1Mi1KCsqrTYfz54JDa60x8=
  dependencies:
    chalk "^2.4.2"
    source-map "^0.6.1"
    supports-color "^6.1.0"

prelude-ls@~1.1.2:
  version "1.1.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/prelude-ls/-/prelude-ls-1.1.2.tgz#21932a549f5e52ffd9a827f570e04be62a97da54"
  integrity sha1-IZMqVJ9eUv/ZqCf1cOBL5iqX2lQ=

preserve@^0.2.0:
  version "0.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/preserve/-/preserve-0.2.0.tgz#815ed1f6ebc65926f865b310c0713bcb3315ce4b"
  integrity sha1-gV7R9uvGWSb4ZbMQwHE7yzMVzks=

pretty-error@^2.0.2:
  version "2.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/pretty-error/-/pretty-error-2.1.1.tgz#5f4f87c8f91e5ae3f3ba87ab4cf5e03b1a17f1a3"
  integrity sha1-X0+HyPkeWuPzuoerTPXgOxoX8aM=
  dependencies:
    renderkid "^2.0.1"
    utila "~0.4"

pretty-format@^23.6.0:
  version "23.6.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/pretty-format/-/pretty-format-23.6.0.tgz#5eaac8eeb6b33b987b7fe6097ea6a8a146ab5760"
  integrity sha1-XqrI7razO5h7f+YJfqaooUarV2A=
  dependencies:
    ansi-regex "^3.0.0"
    ansi-styles "^3.2.0"

private@^0.1.6, private@^0.1.8:
  version "0.1.8"
  resolved "https://nexus.lanmaoly.com/repository/npm/private/-/private-0.1.8.tgz#2381edb3689f7a53d653190060fcf822d2f368ff"
  integrity sha1-I4Hts2ifelPWUxkAYPz4ItLzaP8=

process-nextick-args@~2.0.0:
  version "2.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/process-nextick-args/-/process-nextick-args-2.0.1.tgz#7820d9b16120cc55ca9ae7792680ae7dba6d7fe2"
  integrity sha1-eCDZsWEgzFXKmud5JoCufbptf+I=

process@^0.11.10:
  version "0.11.10"
  resolved "https://nexus.lanmaoly.com/repository/npm/process/-/process-0.11.10.tgz#7332300e840161bda3e69a1d1d91a7d4bc16f182"
  integrity sha1-czIwDoQBYb2j5podHZGn1LwW8YI=

progress@^2.0.0:
  version "2.0.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/progress/-/progress-2.0.3.tgz#7e8cf8d8f5b8f239c1bc68beb4eb78567d572ef8"
  integrity sha1-foz42PW48jnBvGi+tOt4Vn1XLvg=

promise-inflight@^1.0.1:
  version "1.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/promise-inflight/-/promise-inflight-1.0.1.tgz#98472870bf228132fcbdd868129bad12c3c029e3"
  integrity sha1-mEcocL8igTL8vdhoEputEsPAKeM=

promise@^7.1.1:
  version "7.3.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/promise/-/promise-7.3.1.tgz#064b72602b18f90f29192b8b1bc418ffd1ebd3bf"
  integrity sha1-BktyYCsY+Q8pGSuLG8QY/9Hr078=
  dependencies:
    asap "~2.0.3"

prompts@^0.1.9:
  version "0.1.14"
  resolved "https://nexus.lanmaoly.com/repository/npm/prompts/-/prompts-0.1.14.tgz#a8e15c612c5c9ec8f8111847df3337c9cbd443b2"
  integrity sha1-qOFcYSxcnsj4ERhH3zM3ycvUQ7I=
  dependencies:
    kleur "^2.0.1"
    sisteransi "^0.1.1"

prop-types@15.x, prop-types@^15.5.0, prop-types@^15.5.10, prop-types@^15.5.4, prop-types@^15.5.6, prop-types@^15.5.8, prop-types@^15.6.0, prop-types@^15.6.1, prop-types@^15.6.2:
  version "15.7.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/prop-types/-/prop-types-15.7.2.tgz#52c41e75b8c87e72b9d9360e0206b99dcbffa6c5"
  integrity sha1-UsQedbjIfnK52TYOAga5ncv/psU=
  dependencies:
    loose-envify "^1.4.0"
    object-assign "^4.1.1"
    react-is "^16.8.1"

proxy-addr@~2.0.5:
  version "2.0.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/proxy-addr/-/proxy-addr-2.0.5.tgz#34cbd64a2d81f4b1fd21e76f9f06c8a45299ee34"
  integrity sha1-NMvWSi2B9LH9IedvnwbIpFKZ7jQ=
  dependencies:
    forwarded "~0.1.2"
    ipaddr.js "1.9.0"

prr@~1.0.1:
  version "1.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/prr/-/prr-1.0.1.tgz#d3fc114ba06995a45ec6893f484ceb1d78f5f476"
  integrity sha1-0/wRS6BplaRexok/SEzrHXj19HY=

pseudomap@^1.0.2:
  version "1.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/pseudomap/-/pseudomap-1.0.2.tgz#f052a28da70e618917ef0a8ac34c1ae5a68286b3"
  integrity sha1-8FKijacOYYkX7wqKw0wa5aaChrM=

psl@^1.1.24, psl@^1.1.28:
  version "1.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/psl/-/psl-1.2.0.tgz#df12b5b1b3a30f51c329eacbdef98f3a6e136dc6"
  integrity sha1-3xK1sbOjD1HDKerL3vmPOm4TbcY=

public-encrypt@^4.0.0:
  version "4.0.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/public-encrypt/-/public-encrypt-4.0.3.tgz#4fcc9d77a07e48ba7527e7cbe0de33d0701331e0"
  integrity sha1-T8ydd6B+SLp1J+fL4N4z0HATMeA=
  dependencies:
    bn.js "^4.1.0"
    browserify-rsa "^4.0.0"
    create-hash "^1.1.0"
    parse-asn1 "^5.0.0"
    randombytes "^2.0.1"
    safe-buffer "^5.1.2"

pump@^2.0.0, pump@^2.0.1:
  version "2.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/pump/-/pump-2.0.1.tgz#12399add6e4cf7526d973cbc8b5ce2e2908b3909"
  integrity sha1-Ejma3W5M91Jtlzy8i1zi4pCLOQk=
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

pump@^3.0.0:
  version "3.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/pump/-/pump-3.0.0.tgz#b4a2116815bde2f4e1ea602354e8c75565107a64"
  integrity sha1-tKIRaBW94vTh6mAjVOjHVWUQemQ=
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

pumpify@^1.3.3:
  version "1.5.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/pumpify/-/pumpify-1.5.1.tgz#36513be246ab27570b1a374a5ce278bfd74370ce"
  integrity sha1-NlE74karJ1cLGjdKXOJ4v9dDcM4=
  dependencies:
    duplexify "^3.6.0"
    inherits "^2.0.3"
    pump "^2.0.0"

punycode@1.3.2:
  version "1.3.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/punycode/-/punycode-1.3.2.tgz#9653a036fb7c1ee42342f2325cceefea3926c48d"
  integrity sha1-llOgNvt8HuQjQvIyXM7v6jkmxI0=

punycode@^1.2.4, punycode@^1.4.1:
  version "1.4.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/punycode/-/punycode-1.4.1.tgz#c0d5a63b2718800ad8e1eb0fa5269c84dd41845e"
  integrity sha1-wNWmOycYgArY4esPpSachN1BhF4=

punycode@^2.1.0, punycode@^2.1.1:
  version "2.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/punycode/-/punycode-2.1.1.tgz#b58b010ac40c22c5657616c8d2c2c02c7bf479ec"
  integrity sha1-tYsBCsQMIsVldhbI0sLALHv0eew=

qs@6.7.0:
  version "6.7.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/qs/-/qs-6.7.0.tgz#41dc1a015e3d581f1621776be31afb2876a9b1bc"
  integrity sha1-QdwaAV49WB8WIXdr4xr7KHapsbw=

qs@~6.5.2:
  version "6.5.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/qs/-/qs-6.5.2.tgz#cb3ae806e8740444584ef154ce8ee98d403f3e36"
  integrity sha1-yzroBuh0BERYTvFUzo7pjUA/PjY=

querystring-es3@^0.2.0:
  version "0.2.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/querystring-es3/-/querystring-es3-0.2.1.tgz#9ec61f79049875707d69414596fd907a4d711e73"
  integrity sha1-nsYfeQSYdXB9aUFFlv2Qek1xHnM=

querystring@0.2.0:
  version "0.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/querystring/-/querystring-0.2.0.tgz#b209849203bb25df820da756e747005878521620"
  integrity sha1-sgmEkgO7Jd+CDadW50cAWHhSFiA=

querystringify@^2.1.1:
  version "2.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/querystringify/-/querystringify-2.1.1.tgz#60e5a5fd64a7f8bfa4d2ab2ed6fdf4c85bad154e"
  integrity sha1-YOWl/WSn+L+k0qsu1v30yFutFU4=

raf@^3.1.0, raf@^3.3.2, raf@^3.4.0:
  version "3.4.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/raf/-/raf-3.4.1.tgz#0742e99a4a6552f445d73e3ee0328af0ff1ede39"
  integrity sha1-B0LpmkplUvRF1z4+4DKK8P8e3jk=
  dependencies:
    performance-now "^2.1.0"

randomatic@^3.0.0:
  version "3.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/randomatic/-/randomatic-3.1.1.tgz#b776efc59375984e36c537b2f51a1f0aff0da1ed"
  integrity sha1-t3bvxZN1mE42xTey9RofCv8Noe0=
  dependencies:
    is-number "^4.0.0"
    kind-of "^6.0.0"
    math-random "^1.0.1"

randombytes@^2.0.0, randombytes@^2.0.1, randombytes@^2.0.5:
  version "2.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/randombytes/-/randombytes-2.1.0.tgz#df6f84372f0270dc65cdf6291349ab7a473d4f2a"
  integrity sha1-32+ENy8CcNxlzfYpE0mrekc9Tyo=
  dependencies:
    safe-buffer "^5.1.0"

randomfill@^1.0.3:
  version "1.0.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/randomfill/-/randomfill-1.0.4.tgz#c92196fc86ab42be983f1bf31778224931d61458"
  integrity sha1-ySGW/IarQr6YPxvzF3giSTHWFFg=
  dependencies:
    randombytes "^2.0.5"
    safe-buffer "^5.1.0"

range-parser@^1.2.1, range-parser@~1.2.1:
  version "1.2.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/range-parser/-/range-parser-1.2.1.tgz#3cf37023d199e1c24d1a55b84800c2f3e6468031"
  integrity sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE=

raw-body@2.4.0:
  version "2.4.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/raw-body/-/raw-body-2.4.0.tgz#a1ce6fb9c9bc356ca52e89256ab59059e13d0332"
  integrity sha1-oc5vucm8NWylLoklarWQWeE9AzI=
  dependencies:
    bytes "3.1.0"
    http-errors "1.7.2"
    iconv-lite "0.4.24"
    unpipe "1.0.0"

rc-align@^2.4.0:
  version "2.4.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/rc-align/-/rc-align-2.4.5.tgz#c941a586f59d1017f23a428f0b468663fb7102ab"
  integrity sha1-yUGlhvWdEBfyOkKPC0aGY/txAqs=
  dependencies:
    babel-runtime "^6.26.0"
    dom-align "^1.7.0"
    prop-types "^15.5.8"
    rc-util "^4.0.4"

rc-animate@2.x, rc-animate@^2.4.4:
  version "2.6.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/rc-animate/-/rc-animate-2.6.0.tgz#ca8440d042781af7a1329d84f97ea94794c5ec15"
  integrity sha1-yoRA0EJ4GvehMp2E+X6pR5TF7BU=
  dependencies:
    babel-runtime "6.x"
    classnames "^2.2.6"
    css-animation "^1.3.2"
    prop-types "15.x"
    raf "^3.4.0"
    react-lifecycles-compat "^3.0.4"

rc-checkbox@~2.0.0:
  version "2.0.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/rc-checkbox/-/rc-checkbox-2.0.3.tgz#436a9d508948e224980f0535ea738b48177a8f25"
  integrity sha1-Q2qdUIlI4iSYDwU16nOLSBd6jyU=
  dependencies:
    babel-runtime "^6.23.0"
    classnames "2.x"
    prop-types "15.x"
    rc-util "^4.0.4"

rc-collapse@~1.9.1:
  version "1.9.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/rc-collapse/-/rc-collapse-1.9.3.tgz#d9741db06a823353e1fd1aec3ba4c0f9d8af4b26"
  integrity sha1-2XQdsGqCM1Ph/RrsO6TA+divSyY=
  dependencies:
    classnames "2.x"
    css-animation "1.x"
    prop-types "^15.5.6"
    rc-animate "2.x"

rc-form@^2.4.12:
  version "2.4.12"
  resolved "https://nexus.lanmaoly.com/repository/npm/rc-form/-/rc-form-2.4.12.tgz#4ee8711e90a2584baa7ac276de96bee0d9b0f5f1"
  integrity sha1-TuhxHpCiWEuqesJ23pa+4Nmw9fE=
  dependencies:
    async-validator "~1.11.3"
    babel-runtime "6.x"
    create-react-class "^15.5.3"
    dom-scroll-into-view "1.x"
    hoist-non-react-statics "^3.3.0"
    lodash "^4.17.4"
    rc-util "^4.15.3"
    react-is "^16.13.1"
    warning "^4.0.3"

rc-gesture@~0.0.18, rc-gesture@~0.0.22:
  version "0.0.22"
  resolved "https://nexus.lanmaoly.com/repository/npm/rc-gesture/-/rc-gesture-0.0.22.tgz#fbcbdd5b46387a978b3ede48b42748e8ff77dddd"
  integrity sha1-+8vdW0Y4epeLPt5ItCdI6P933d0=
  dependencies:
    babel-runtime "6.x"

rc-slider@~8.2.0:
  version "8.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/rc-slider/-/rc-slider-8.2.0.tgz#ae37d17144cad60e1da6eac0ee4ffcfea0b0a6e8"
  integrity sha1-rjfRcUTK1g4dpurA7k/8/qCwpug=
  dependencies:
    babel-runtime "6.x"
    classnames "^2.2.5"
    prop-types "^15.5.4"
    rc-tooltip "^3.4.2"
    rc-util "^4.0.4"
    shallowequal "^1.0.1"
    warning "^3.0.0"

rc-swipeout@~2.0.0:
  version "2.0.11"
  resolved "https://nexus.lanmaoly.com/repository/npm/rc-swipeout/-/rc-swipeout-2.0.11.tgz#dfad9c7b38a15ea0376e39cb3356e36fed7a4155"
  integrity sha1-362cezihXqA3bjnLM1bjb+16QVU=
  dependencies:
    babel-runtime "6.x"
    classnames "2.x"
    rc-gesture "~0.0.22"
    react-native-swipeout "^2.2.2"

rc-tooltip@^3.4.2:
  version "3.7.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/rc-tooltip/-/rc-tooltip-3.7.3.tgz#280aec6afcaa44e8dff0480fbaff9e87fc00aecc"
  integrity sha1-KArsavyqROjf8EgPuv+eh/wArsw=
  dependencies:
    babel-runtime "6.x"
    prop-types "^15.5.8"
    rc-trigger "^2.2.2"

rc-trigger@^2.2.2:
  version "2.6.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/rc-trigger/-/rc-trigger-2.6.2.tgz#a9c09ba5fad63af3b2ec46349c7db6cb46657001"
  integrity sha1-qcCbpfrWOvOy7EY0nH22y0ZlcAE=
  dependencies:
    babel-runtime "6.x"
    classnames "^2.2.6"
    prop-types "15.x"
    rc-align "^2.4.0"
    rc-animate "2.x"
    rc-util "^4.4.0"

rc-util@4.x, rc-util@^4.0.4, rc-util@^4.4.0:
  version "4.6.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/rc-util/-/rc-util-4.6.0.tgz#ba33721783192ec4f3afb259e182b04e55deb7f6"
  integrity sha1-ujNyF4MZLsTzr7JZ4YKwTlXet/Y=
  dependencies:
    add-dom-event-listener "^1.1.0"
    babel-runtime "6.x"
    prop-types "^15.5.10"
    shallowequal "^0.2.2"

rc-util@^4.15.3:
  version "4.21.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/rc-util/-/rc-util-4.21.1.tgz#88602d0c3185020aa1053d9a1e70eac161becb05"
  integrity sha1-iGAtDDGFAgqhBT2aHnDqwWG+ywU=
  dependencies:
    add-dom-event-listener "^1.1.0"
    prop-types "^15.5.10"
    react-is "^16.12.0"
    react-lifecycles-compat "^3.0.4"
    shallowequal "^1.1.0"

rc-util@^5.9.4:
  version "5.12.2"
  resolved "https://r.cnpmjs.org/rc-util/download/rc-util-5.12.2.tgz#41572687a2cd174fdaec028da756d288e398e9f8"
  integrity sha1-QVcmh6LNF0/a7AKNp1bSiOOY6fg=
  dependencies:
    "@babel/runtime" "^7.12.5"
    react-is "^16.12.0"
    shallowequal "^1.1.0"

rc@^1.2.7:
  version "1.2.8"
  resolved "https://nexus.lanmaoly.com/repository/npm/rc/-/rc-1.2.8.tgz#cd924bf5200a075b83c188cd6b9e211b7fc0d3ed"
  integrity sha1-zZJL9SAKB1uDwYjNa54hG3/A0+0=
  dependencies:
    deep-extend "^0.6.0"
    ini "~1.3.0"
    minimist "^1.2.0"
    strip-json-comments "~2.0.1"

react-css-modules@^4.7.7:
  version "4.7.11"
  resolved "https://r.cnpmjs.org/react-css-modules/download/react-css-modules-4.7.11.tgz#e9bc7ac6e3dd7e71c8e46e9d22c1d0abb2110682"
  integrity sha1-6bx6xuPdfnHI5G6dIsHQq7IRBoI=
  dependencies:
    hoist-non-react-statics "^2.5.5"
    lodash "^4.16.6"
    object-unfreeze "^1.1.0"

react-dom@^16.4.2:
  version "16.8.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/react-dom/-/react-dom-16.8.4.tgz#1061a8e01a2b3b0c8160037441c3bf00a0e3bc48"
  integrity sha1-EGGo4BorOwyBYAN0QcO/AKDjvEg=
  dependencies:
    loose-envify "^1.1.0"
    object-assign "^4.1.1"
    prop-types "^15.6.2"
    scheduler "^0.13.4"

react-dom@^16.6.0:
  version "16.13.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/react-dom/-/react-dom-16.13.1.tgz#c1bd37331a0486c078ee54c4740720993b2e0e7f"
  integrity sha1-wb03MxoEhsB47lTEdAcgmTsuDn8=
  dependencies:
    loose-envify "^1.1.0"
    object-assign "^4.1.1"
    prop-types "^15.6.2"
    scheduler "^0.19.1"

react-fastclick@^3.0.2:
  version "3.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/react-fastclick/-/react-fastclick-3.0.2.tgz#2994c60088cda90b0b2cbfac4b6e7c6bc73d6a3a"
  integrity sha1-KZTGAIjNqQsLLL+sS258a8c9ajo=

react-hot-loader@4.5.3:
  version "4.5.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/react-hot-loader/-/react-hot-loader-4.5.3.tgz#d7e5cd04fd6ae7c482404202d5f162e4eaefb268"
  integrity sha1-1+XNBP1q58SCQEIC1fFi5Orvsmg=
  dependencies:
    fast-levenshtein "^2.0.6"
    global "^4.3.0"
    hoist-non-react-statics "^2.5.0"
    loader-utils "^1.1.0"
    lodash.merge "^4.6.1"
    prop-types "^15.6.1"
    react-lifecycles-compat "^3.0.4"
    shallowequal "^1.0.2"
    source-map "^0.7.3"

react-input-groups@^1.0.3:
  version "1.0.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/react-input-groups/-/react-input-groups-1.0.3.tgz#d2f73b8aee199facecbf3956136658b2448d6ea3"
  integrity sha1-0vc7iu4Zn6zsvzlWE2ZYskSNbqM=
  dependencies:
    prop-types "^15.6.0"
    react "^16.6.0"
    react-dom "^16.6.0"

react-is@^16.12.0, react-is@^16.13.1:
  version "16.13.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/react-is/-/react-is-16.13.1.tgz#789729a4dc36de2999dc156dd6c1d9c18cea56a4"
  integrity sha1-eJcppNw23imZ3BVt1sHZwYzqVqQ=

react-is@^16.6.0, react-is@^16.7.0, react-is@^16.8.1:
  version "16.8.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/react-is/-/react-is-16.8.4.tgz#90f336a68c3a29a096a3d648ab80e87ec61482a2"
  integrity sha1-kPM2pow6KaCWo9ZIq4DofsYUgqI=

react-json-pretty@^2.1.0:
  version "2.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/react-json-pretty/-/react-json-pretty-2.1.0.tgz#ba2fd96b26706c7455f72d8683e7d7a00f5c4e9f"
  integrity sha1-ui/ZayZwbHRV9y2Gg+fXoA9cTp8=
  dependencies:
    prop-types "^15.6.2"

react-lifecycles-compat@^3.0.0, react-lifecycles-compat@^3.0.4:
  version "3.0.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/react-lifecycles-compat/-/react-lifecycles-compat-3.0.4.tgz#4f1a273afdfc8f3488a8c516bfda78f872352362"
  integrity sha1-TxonOv38jzSIqMUWv9p4+HI1I2I=

react-loadable@^5.5.0:
  version "5.5.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/react-loadable/-/react-loadable-5.5.0.tgz#582251679d3da86c32aae2c8e689c59f1196d8c4"
  integrity sha1-WCJRZ509qGwyquLI5onFnxGW2MQ=
  dependencies:
    prop-types "^15.5.0"

react-native-swipeout@^2.2.2:
  version "2.3.6"
  resolved "https://nexus.lanmaoly.com/repository/npm/react-native-swipeout/-/react-native-swipeout-2.3.6.tgz#47dac8a835825cf3f2eef9e495574a3d9ab6d3fa"
  integrity sha1-R9rIqDWCXPPy7vnklVdKPZq20/o=
  dependencies:
    create-react-class "^15.6.0"
    prop-types "^15.5.10"
    react-tween-state "^0.1.5"

react-pdf@^5.3.0:
  version "5.3.0"
  resolved "https://r.cnpmjs.org/react-pdf/download/react-pdf-5.3.0.tgz#29f128c70fdb2397ce9d65f3402e762847259f9d"
  integrity sha1-KfEoxw/bI5fOnWXzQC52KEcln50=
  dependencies:
    "@babel/runtime" "^7.0.0"
    file-loader "^6.0.0"
    make-cancellable-promise "^1.0.0"
    make-event-props "^1.1.0"
    merge-class-names "^1.1.1"
    merge-refs "^1.0.0"
    pdfjs-dist "2.6.347"
    prop-types "^15.6.2"

react-read-pdf@^2.0.9:
  version "2.0.9"
  resolved "https://r.cnpmjs.org/react-read-pdf/download/react-read-pdf-2.0.9.tgz#e0a0bc43c899d4dffab33fc00115b935f4a2fdc5"
  integrity sha1-4KC8Q8iZ1N/6sz/AARW5NfSi/cU=
  dependencies:
    babel-polyfill "^6.26.0"
    babel-preset-stage-2 "^6.24.1"
    less "^3.8.1"
    less-loader "^4.1.0"
    pdfjs-dist "^2.0.550"
    react-css-modules "^4.7.7"
    ts-loader "^5.2.2"
    typings-for-css-modules-loader "^1.7.0"
    url-loader "^1.1.2"

react-redux@^5.0.7:
  version "5.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/react-redux/-/react-redux-5.1.1.tgz#88e368682c7fa80e34e055cd7ac56f5936b0f52f"
  integrity sha1-iONoaCx/qA404FXNesVvWTaw9S8=
  dependencies:
    "@babel/runtime" "^7.1.2"
    hoist-non-react-statics "^3.1.0"
    invariant "^2.2.4"
    loose-envify "^1.1.0"
    prop-types "^15.6.1"
    react-is "^16.6.0"
    react-lifecycles-compat "^3.0.0"

react-router-dom@^4.3.1:
  version "4.3.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/react-router-dom/-/react-router-dom-4.3.1.tgz#4c2619fc24c4fa87c9fd18f4fb4a43fe63fbd5c6"
  integrity sha1-TCYZ/CTE+ofJ/Rj0+0pD/mP71cY=
  dependencies:
    history "^4.7.2"
    invariant "^2.2.4"
    loose-envify "^1.3.1"
    prop-types "^15.6.1"
    react-router "^4.3.1"
    warning "^4.0.1"

react-router-redux@^4.0.0:
  version "4.0.8"
  resolved "https://nexus.lanmaoly.com/repository/npm/react-router-redux/-/react-router-redux-4.0.8.tgz#227403596b5151e182377dab835b5d45f0f8054e"
  integrity sha1-InQDWWtRUeGCN32rg1tdRfD4BU4=

react-router@^4.3.1:
  version "4.3.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/react-router/-/react-router-4.3.1.tgz#aada4aef14c809cb2e686b05cee4742234506c4e"
  integrity sha1-qtpK7xTICcsuaGsFzuR0IjRQbE4=
  dependencies:
    history "^4.7.2"
    hoist-non-react-statics "^2.5.0"
    invariant "^2.2.4"
    loose-envify "^1.3.1"
    path-to-regexp "^1.7.0"
    prop-types "^15.6.1"
    warning "^4.0.1"

react-tween-state@^0.1.5:
  version "0.1.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/react-tween-state/-/react-tween-state-0.1.5.tgz#e98b066551efb93cb92dd1be14995c2e3deae339"
  integrity sha1-6YsGZVHvuTy5LdG+FJlcLj3q4zk=
  dependencies:
    raf "^3.1.0"
    tween-functions "^1.0.1"

react-wx-images-viewer@^1.0.6:
  version "1.0.6"
  resolved "https://registry.npm.taobao.org/react-wx-images-viewer/download/react-wx-images-viewer-1.0.6.tgz#a39592de6e2b1451943565ecc36f8d99c318f6c2"
  integrity sha1-o5WS3m4rFFGUNWXsw2+NmcMY9sI=
  dependencies:
    prop-types "^15.6.0"
    raf "^3.1.0"

react@^16.4.2:
  version "16.8.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/react/-/react-16.8.4.tgz#fdf7bd9ae53f03a9c4cd1a371432c206be1c4768"
  integrity sha1-/fe9muU/A6nEzRo3FDLCBr4cR2g=
  dependencies:
    loose-envify "^1.1.0"
    object-assign "^4.1.1"
    prop-types "^15.6.2"
    scheduler "^0.13.4"

react@^16.6.0:
  version "16.13.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/react/-/react-16.13.1.tgz#2e818822f1a9743122c063d6410d85c1e3afe48e"
  integrity sha1-LoGIIvGpdDEiwGPWQQ2FweOv5I4=
  dependencies:
    loose-envify "^1.1.0"
    object-assign "^4.1.1"
    prop-types "^15.6.2"

read-pkg-up@^1.0.1:
  version "1.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/read-pkg-up/-/read-pkg-up-1.0.1.tgz#9d63c13276c065918d57f002a57f40a1b643fb02"
  integrity sha1-nWPBMnbAZZGNV/ACpX9AobZD+wI=
  dependencies:
    find-up "^1.0.0"
    read-pkg "^1.0.0"

read-pkg-up@^2.0.0:
  version "2.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/read-pkg-up/-/read-pkg-up-2.0.0.tgz#6b72a8048984e0c41e79510fd5e9fa99b3b549be"
  integrity sha1-a3KoBImE4MQeeVEP1en6mbO1Sb4=
  dependencies:
    find-up "^2.0.0"
    read-pkg "^2.0.0"

read-pkg@^1.0.0:
  version "1.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/read-pkg/-/read-pkg-1.1.0.tgz#f5ffaa5ecd29cb31c0474bca7d756b6bb29e3f28"
  integrity sha1-9f+qXs0pyzHAR0vKfXVra7KePyg=
  dependencies:
    load-json-file "^1.0.0"
    normalize-package-data "^2.3.2"
    path-type "^1.0.0"

read-pkg@^2.0.0:
  version "2.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/read-pkg/-/read-pkg-2.0.0.tgz#8ef1c0623c6a6db0dc6713c4bfac46332b2368f8"
  integrity sha1-jvHAYjxqbbDcZxPEv6xGMysjaPg=
  dependencies:
    load-json-file "^2.0.0"
    normalize-package-data "^2.3.2"
    path-type "^2.0.0"

read-pkg@^3.0.0:
  version "3.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/read-pkg/-/read-pkg-3.0.0.tgz#9cbc686978fee65d16c00e2b19c237fcf6e38389"
  integrity sha1-nLxoaXj+5l0WwA4rGcI3/Pbjg4k=
  dependencies:
    load-json-file "^4.0.0"
    normalize-package-data "^2.3.2"
    path-type "^3.0.0"

"readable-stream@1 || 2", readable-stream@^2.0.0, readable-stream@^2.0.1, readable-stream@^2.0.2, readable-stream@^2.0.6, readable-stream@^2.1.5, readable-stream@^2.2.2, readable-stream@^2.3.3, readable-stream@^2.3.6, readable-stream@~2.3.6:
  version "2.3.6"
  resolved "https://nexus.lanmaoly.com/repository/npm/readable-stream/-/readable-stream-2.3.6.tgz#b11c27d88b8ff1fbe070643cf94b0c79ae1b0aaf"
  integrity sha1-sRwn2IuP8fvgcGQ8+UsMea4bCq8=
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readable-stream@^3.0.6, readable-stream@^3.1.1:
  version "3.4.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/readable-stream/-/readable-stream-3.4.0.tgz#a51c26754658e0a3c21dbf59163bd45ba6f447fc"
  integrity sha1-pRwmdUZY4KPCHb9ZFjvUW6b0R/w=
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readdirp@^2.2.1:
  version "2.2.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/readdirp/-/readdirp-2.2.1.tgz#0e87622a3325aa33e892285caf8b4e846529a525"
  integrity sha1-DodiKjMlqjPokihcr4tOhGUppSU=
  dependencies:
    graceful-fs "^4.1.11"
    micromatch "^3.1.10"
    readable-stream "^2.0.2"

realpath-native@^1.0.0:
  version "1.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/realpath-native/-/realpath-native-1.0.2.tgz#cd51ce089b513b45cf9b1516c82989b51ccc6560"
  integrity sha1-zVHOCJtRO0XPmxUWyCmJtRzMZWA=
  dependencies:
    util.promisify "^1.0.0"

redent@^1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/redent/-/redent-1.0.0.tgz#cf916ab1fd5f1f16dfb20822dd6ec7f730c2afde"
  integrity sha1-z5Fqsf1fHxbfsggi3W7H9zDCr94=
  dependencies:
    indent-string "^2.1.0"
    strip-indent "^1.0.1"

redux-logger@^3.0.6:
  version "3.0.6"
  resolved "https://nexus.lanmaoly.com/repository/npm/redux-logger/-/redux-logger-3.0.6.tgz#f7555966f3098f3c88604c449cf0baf5778274bf"
  integrity sha1-91VZZvMJjzyIYExEnPC69XeCdL8=
  dependencies:
    deep-diff "^0.3.5"

redux-persist@^5.10.0:
  version "5.10.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/redux-persist/-/redux-persist-5.10.0.tgz#5d8d802c5571e55924efc1c3a9b23575283be62b"
  integrity sha1-XY2ALFVx5Vkk78HDqbI1dSg75is=

redux-seamless-immutable@^0.4.0:
  version "0.4.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/redux-seamless-immutable/-/redux-seamless-immutable-0.4.0.tgz#b50f8680ecc5ef04021551267f78fa1ffd3cf985"
  integrity sha1-tQ+GgOzF7wQCFVEmf3j6H/08+YU=
  dependencies:
    react-router-redux "^4.0.0"
    seamless-immutable "^7.1.2"

redux-thunk@^2.3.0:
  version "2.3.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/redux-thunk/-/redux-thunk-2.3.0.tgz#51c2c19a185ed5187aaa9a2d08b666d0d6467622"
  integrity sha1-UcLBmhhe1Rh6qpotCLZm0NZGdiI=

redux@^4.0.0:
  version "4.0.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/redux/-/redux-4.0.4.tgz#4ee1aeb164b63d6a1bcc57ae4aa0b6e6fa7a3796"
  integrity sha1-TuGusWS2PWobzFeuSqC25vp6N5Y=
  dependencies:
    loose-envify "^1.4.0"
    symbol-observable "^1.2.0"

regenerate-unicode-properties@^8.0.2:
  version "8.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/regenerate-unicode-properties/-/regenerate-unicode-properties-8.1.0.tgz#ef51e0f0ea4ad424b77bf7cb41f3e015c70a3f0e"
  integrity sha1-71Hg8OpK1CS3e/fLQfPgFccKPw4=
  dependencies:
    regenerate "^1.4.0"

regenerate@^1.2.1, regenerate@^1.4.0:
  version "1.4.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/regenerate/-/regenerate-1.4.0.tgz#4a856ec4b56e4077c557589cae85e7a4c8869a11"
  integrity sha1-SoVuxLVuQHfFV1icroXnpMiGmhE=

regenerator-runtime@^0.10.5:
  version "0.10.5"
  resolved "https://r.cnpmjs.org/regenerator-runtime/download/regenerator-runtime-0.10.5.tgz#336c3efc1220adcedda2c9fab67b5a7955a33658"
  integrity sha1-M2w+/BIgrc7dosn6tntaeVWjNlg=

regenerator-runtime@^0.11.0:
  version "0.11.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/regenerator-runtime/-/regenerator-runtime-0.11.1.tgz#be05ad7f9bf7d22e056f9726cee5017fbf19e2e9"
  integrity sha1-vgWtf5v30i4Fb5cmzuUBf78Z4uk=

regenerator-runtime@^0.13.2:
  version "0.13.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/regenerator-runtime/-/regenerator-runtime-0.13.3.tgz#7cf6a77d8f5c6f60eb73c5fc1955b2ceb01e6bf5"
  integrity sha1-fPanfY9cb2Drc8X8GVWyzrAea/U=

regenerator-runtime@^0.13.4:
  version "0.13.7"
  resolved "https://r.cnpmjs.org/regenerator-runtime/download/regenerator-runtime-0.13.7.tgz#cac2dacc8a1ea675feaabaeb8ae833898ae46f55"
  integrity sha1-ysLazIoepnX+qrrriugziYrkb1U=

regenerator-transform@^0.14.0:
  version "0.14.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/regenerator-transform/-/regenerator-transform-0.14.1.tgz#3b2fce4e1ab7732c08f665dfdb314749c7ddd2fb"
  integrity sha1-Oy/OThq3cywI9mXf2zFHScfd0vs=
  dependencies:
    private "^0.1.6"

regex-cache@^0.4.2:
  version "0.4.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/regex-cache/-/regex-cache-0.4.4.tgz#75bdc58a2a1496cec48a12835bc54c8d562336dd"
  integrity sha1-db3FiioUls7EihKDW8VMjVYjNt0=
  dependencies:
    is-equal-shallow "^0.1.3"

regex-not@^1.0.0, regex-not@^1.0.2:
  version "1.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/regex-not/-/regex-not-1.0.2.tgz#1f4ece27e00b0b65e0247a6810e6a85d83a5752c"
  integrity sha1-H07OJ+ALC2XgJHpoEOaoXYOldSw=
  dependencies:
    extend-shallow "^3.0.2"
    safe-regex "^1.1.0"

regexp-tree@^0.1.6:
  version "0.1.11"
  resolved "https://nexus.lanmaoly.com/repository/npm/regexp-tree/-/regexp-tree-0.1.11.tgz#c9c7f00fcf722e0a56c7390983a7a63dd6c272f3"
  integrity sha1-ycfwD89yLgpWxzkJg6emPdbCcvM=

regexpp@^2.0.1:
  version "2.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/regexpp/-/regexpp-2.0.1.tgz#8d19d31cf632482b589049f8281f93dbcba4d07f"
  integrity sha1-jRnTHPYySCtYkEn4KB+T28uk0H8=

regexpu-core@^1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/regexpu-core/-/regexpu-core-1.0.0.tgz#86a763f58ee4d7c2f6b102e4764050de7ed90c6b"
  integrity sha1-hqdj9Y7k18L2sQLkdkBQ3n7ZDGs=
  dependencies:
    regenerate "^1.2.1"
    regjsgen "^0.2.0"
    regjsparser "^0.1.4"

regexpu-core@^4.5.4:
  version "4.5.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/regexpu-core/-/regexpu-core-4.5.4.tgz#080d9d02289aa87fe1667a4f5136bc98a6aebaae"
  integrity sha1-CA2dAiiaqH/hZnpPUTa8mKauuq4=
  dependencies:
    regenerate "^1.4.0"
    regenerate-unicode-properties "^8.0.2"
    regjsgen "^0.5.0"
    regjsparser "^0.6.0"
    unicode-match-property-ecmascript "^1.0.4"
    unicode-match-property-value-ecmascript "^1.1.0"

regjsgen@^0.2.0:
  version "0.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/regjsgen/-/regjsgen-0.2.0.tgz#6c016adeac554f75823fe37ac05b92d5a4edb1f7"
  integrity sha1-bAFq3qxVT3WCP+N6wFuS1aTtsfc=

regjsgen@^0.5.0:
  version "0.5.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/regjsgen/-/regjsgen-0.5.0.tgz#a7634dc08f89209c2049adda3525711fb97265dd"
  integrity sha1-p2NNwI+JIJwgSa3aNSVxH7lyZd0=

regjsparser@^0.1.4:
  version "0.1.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/regjsparser/-/regjsparser-0.1.5.tgz#7ee8f84dc6fa792d3fd0ae228d24bd949ead205c"
  integrity sha1-fuj4Tcb6eS0/0K4ijSS9lJ6tIFw=
  dependencies:
    jsesc "~0.5.0"

regjsparser@^0.6.0:
  version "0.6.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/regjsparser/-/regjsparser-0.6.0.tgz#f1e6ae8b7da2bae96c99399b868cd6c933a2ba9c"
  integrity sha1-8eaui32iuulsmTmbhozWyTOiupw=
  dependencies:
    jsesc "~0.5.0"

relateurl@0.2.x:
  version "0.2.7"
  resolved "https://nexus.lanmaoly.com/repository/npm/relateurl/-/relateurl-0.2.7.tgz#54dbf377e51440aca90a4cd274600d3ff2d888a9"
  integrity sha1-VNvzd+UUQKypCkzSdGANP/LYiKk=

remove-trailing-separator@^1.0.1:
  version "1.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/remove-trailing-separator/-/remove-trailing-separator-1.1.0.tgz#c24bce2a283adad5bc3f58e0d48249b92379d8ef"
  integrity sha1-wkvOKig62tW8P1jg1IJJuSN52O8=

renderkid@^2.0.1:
  version "2.0.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/renderkid/-/renderkid-2.0.3.tgz#380179c2ff5ae1365c522bf2fcfcff01c5b74149"
  integrity sha1-OAF5wv9a4TZcUivy/Pz/AcW3QUk=
  dependencies:
    css-select "^1.1.0"
    dom-converter "^0.2"
    htmlparser2 "^3.3.0"
    strip-ansi "^3.0.0"
    utila "^0.4.0"

repeat-element@^1.1.2:
  version "1.1.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/repeat-element/-/repeat-element-1.1.3.tgz#782e0d825c0c5a3bb39731f84efee6b742e6b1ce"
  integrity sha1-eC4NglwMWjuzlzH4Tv7mt0Lmsc4=

repeat-string@^1.5.2, repeat-string@^1.6.1:
  version "1.6.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/repeat-string/-/repeat-string-1.6.1.tgz#8dcae470e1c88abc2d600fff4a776286da75e637"
  integrity sha1-jcrkcOHIirwtYA//Sndihtp15jc=

repeating@^2.0.0:
  version "2.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/repeating/-/repeating-2.0.1.tgz#5214c53a926d3552707527fbab415dbc08d06dda"
  integrity sha1-UhTFOpJtNVJwdSf7q0FdvAjQbdo=
  dependencies:
    is-finite "^1.0.0"

request-promise-core@1.1.1:
  version "1.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/request-promise-core/-/request-promise-core-1.1.1.tgz#3eee00b2c5aa83239cfb04c5700da36f81cd08b6"
  integrity sha1-Pu4AssWqgyOc+wTFcA2jb4HNCLY=
  dependencies:
    lodash "^4.13.1"

request-promise-native@^1.0.5:
  version "1.0.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/request-promise-native/-/request-promise-native-1.0.5.tgz#5281770f68e0c9719e5163fd3fab482215f4fda5"
  integrity sha1-UoF3D2jgyXGeUWP9P6tIIhX0/aU=
  dependencies:
    request-promise-core "1.1.1"
    stealthy-require "^1.1.0"
    tough-cookie ">=2.3.3"

request@^2.83.0, request@^2.87.0, request@^2.88.0:
  version "2.88.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/request/-/request-2.88.0.tgz#9c2fca4f7d35b592efe57c7f0a55e81052124fef"
  integrity sha1-nC/KT301tZLv5Xx/ClXoEFIST+8=
  dependencies:
    aws-sign2 "~0.7.0"
    aws4 "^1.8.0"
    caseless "~0.12.0"
    combined-stream "~1.0.6"
    extend "~3.0.2"
    forever-agent "~0.6.1"
    form-data "~2.3.2"
    har-validator "~5.1.0"
    http-signature "~1.2.0"
    is-typedarray "~1.0.0"
    isstream "~0.1.2"
    json-stringify-safe "~5.0.1"
    mime-types "~2.1.19"
    oauth-sign "~0.9.0"
    performance-now "^2.1.0"
    qs "~6.5.2"
    safe-buffer "^5.1.2"
    tough-cookie "~2.4.3"
    tunnel-agent "^0.6.0"
    uuid "^3.3.2"

require-directory@^2.1.1:
  version "2.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/require-directory/-/require-directory-2.1.1.tgz#8c64ad5fd30dab1c976e2344ffe7f792a6a6df42"
  integrity sha1-jGStX9MNqxyXbiNE/+f3kqam30I=

require-main-filename@^1.0.1:
  version "1.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/require-main-filename/-/require-main-filename-1.0.1.tgz#97f717b69d48784f5f526a6c5aa8ffdda055a4d1"
  integrity sha1-l/cXtp1IeE9fUmpsWqj/3aBVpNE=

require-main-filename@^2.0.0:
  version "2.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/require-main-filename/-/require-main-filename-2.0.0.tgz#d0b329ecc7cc0f61649f62215be69af54aa8989b"
  integrity sha1-0LMp7MfMD2Fkn2IhW+aa9UqomJs=

requires-port@^1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/requires-port/-/requires-port-1.0.0.tgz#925d2601d39ac485e091cf0da5c6e694dc3dcaff"
  integrity sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8=

reselect@^3.0.1:
  version "3.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/reselect/-/reselect-3.0.1.tgz#efdaa98ea7451324d092b2b2163a6a1d7a9a2147"
  integrity sha1-79qpjqdFEyTQkrKyFjpqHXqaIUc=

resolve-cwd@^2.0.0:
  version "2.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/resolve-cwd/-/resolve-cwd-2.0.0.tgz#00a9f7387556e27038eae232caa372a6a59b665a"
  integrity sha1-AKn3OHVW4nA46uIyyqNypqWbZlo=
  dependencies:
    resolve-from "^3.0.0"

resolve-dir@^1.0.0, resolve-dir@^1.0.1:
  version "1.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/resolve-dir/-/resolve-dir-1.0.1.tgz#79a40644c362be82f26effe739c9bb5382046f43"
  integrity sha1-eaQGRMNivoLybv/nOcm7U4IEb0M=
  dependencies:
    expand-tilde "^2.0.0"
    global-modules "^1.0.0"

resolve-from@^3.0.0:
  version "3.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/resolve-from/-/resolve-from-3.0.0.tgz#b22c7af7d9d6881bc8b6e653335eebcb0a188748"
  integrity sha1-six699nWiBvItuZTM17rywoYh0g=

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/resolve-from/-/resolve-from-4.0.0.tgz#4abcd852ad32dd7baabfe9b40e00a36db5f392e6"
  integrity sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=

resolve-pathname@^2.2.0:
  version "2.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/resolve-pathname/-/resolve-pathname-2.2.0.tgz#7e9ae21ed815fd63ab189adeee64dc831eefa879"
  integrity sha1-fpriHtgV/WOrGJre7mTcgx7vqHk=

resolve-url@^0.2.1:
  version "0.2.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/resolve-url/-/resolve-url-0.2.1.tgz#2c637fe77c893afd2a663fe21aa9080068e2052a"
  integrity sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo=

resolve@1.1.7:
  version "1.1.7"
  resolved "https://nexus.lanmaoly.com/repository/npm/resolve/-/resolve-1.1.7.tgz#203114d82ad2c5ed9e8e0411b3932875e889e97b"
  integrity sha1-IDEU2CrSxe2ejgQRs5ModeiJ6Xs=

resolve@^1.10.0, resolve@^1.3.2, resolve@^1.4.0, resolve@^1.5.0, resolve@^1.8.1, resolve@^1.9.0:
  version "1.11.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/resolve/-/resolve-1.11.1.tgz#ea10d8110376982fef578df8fc30b9ac30a07a3e"
  integrity sha1-6hDYEQN2mC/vV434/DC5rDCgej4=
  dependencies:
    path-parse "^1.0.6"

restore-cursor@^2.0.0:
  version "2.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/restore-cursor/-/restore-cursor-2.0.0.tgz#9f7ee287f82fd326d4fd162923d62129eee0dfaf"
  integrity sha1-n37ih/gv0ybU/RYpI9YhKe7g368=
  dependencies:
    onetime "^2.0.0"
    signal-exit "^3.0.2"

ret@~0.1.10:
  version "0.1.15"
  resolved "https://nexus.lanmaoly.com/repository/npm/ret/-/ret-0.1.15.tgz#b8a4825d5bdb1fc3f6f53c2bc33f81388681c7bc"
  integrity sha1-uKSCXVvbH8P29Twrwz+BOIaBx7w=

retry@^0.12.0:
  version "0.12.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/retry/-/retry-0.12.0.tgz#1b42a6266a21f07421d1b0b54b7dc167b01c013b"
  integrity sha1-G0KmJmoh8HQh0bC1S33BZ7AcATs=

rimraf@2, rimraf@2.6.3, rimraf@^2.5.4, rimraf@^2.6.1, rimraf@^2.6.2, rimraf@^2.6.3:
  version "2.6.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/rimraf/-/rimraf-2.6.3.tgz#b2d104fe0d8fb27cf9e0a1cda8262dd3833c6cab"
  integrity sha1-stEE/g2Psnz54KHNqCYt04M8bKs=
  dependencies:
    glob "^7.1.3"

ripemd160@^2.0.0, ripemd160@^2.0.1:
  version "2.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/ripemd160/-/ripemd160-2.0.2.tgz#a1c1a6f624751577ba5d07914cbc92850585890c"
  integrity sha1-ocGm9iR1FXe6XQeRTLyShQWFiQw=
  dependencies:
    hash-base "^3.0.0"
    inherits "^2.0.1"

rmc-align@~1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/rmc-align/-/rmc-align-1.0.0.tgz#8d64ab484609a041ab424506012a15b7c5b933dd"
  integrity sha1-jWSrSEYJoEGrQkUGASoVt8W5M90=
  dependencies:
    babel-runtime "6.x"
    dom-align "1.x"
    rc-util "4.x"

rmc-calendar@^1.0.0:
  version "1.1.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/rmc-calendar/-/rmc-calendar-1.1.4.tgz#7db4990087877cd49a7772f4524d33b8016d3bd2"
  integrity sha1-fbSZAIeHfNSad3L0Uk0zuAFtO9I=
  dependencies:
    babel-runtime "^6.26.0"
    rc-animate "^2.4.4"
    rmc-date-picker "^6.0.8"

rmc-cascader@~5.0.0:
  version "5.0.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/rmc-cascader/-/rmc-cascader-5.0.3.tgz#c605b1eac6613e4c54aa6aed2cbae7f9c5a8c65f"
  integrity sha1-xgWx6sZhPkxUqmrtLLrn+cWoxl8=
  dependencies:
    array-tree-filter "2.1.x"
    babel-runtime "6.x"
    rmc-picker "~5.0.0"

rmc-date-picker@^6.0.8:
  version "6.0.10"
  resolved "https://nexus.lanmaoly.com/repository/npm/rmc-date-picker/-/rmc-date-picker-6.0.10.tgz#34dc7dfd424248be2d43527421576247c31583f6"
  integrity sha1-NNx9/UJCSL4tQ1J0IVdiR8MVg/Y=
  dependencies:
    babel-runtime "6.x"
    rmc-picker "~5.0.0"

rmc-dialog@^1.0.1, rmc-dialog@^1.1.1:
  version "1.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/rmc-dialog/-/rmc-dialog-1.1.1.tgz#1d7fbc6b2cad5b0b53fbab71fe29636d76f78217"
  integrity sha1-HX+8ayytWwtT+6tx/iljbXb3ghc=
  dependencies:
    babel-runtime "6.x"
    rc-animate "2.x"

rmc-drawer@^0.4.11:
  version "0.4.11"
  resolved "https://nexus.lanmaoly.com/repository/npm/rmc-drawer/-/rmc-drawer-0.4.11.tgz#9a8c6125a4ccd37b916f32f7e8b477d11d413ee3"
  integrity sha1-moxhJaTM03uRbzL36LR30R1BPuM=
  dependencies:
    babel-runtime "6.x"
    classnames "^2.2.4"
    prop-types "^15.5.10"

rmc-feedback@^2.0.0:
  version "2.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/rmc-feedback/-/rmc-feedback-2.0.0.tgz#cbc6cb3ae63c7a635eef0e25e4fbaf5ac366eeaa"
  integrity sha1-y8bLOuY8emNe7w4l5PuvWsNm7qo=
  dependencies:
    babel-runtime "6.x"
    classnames "^2.2.5"

rmc-input-number@^1.0.0:
  version "1.0.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/rmc-input-number/-/rmc-input-number-1.0.5.tgz#42e02a27b0c3c366be9ff0ce19d818b71e406f8f"
  integrity sha1-QuAqJ7DDw2a+n/DOGdgYtx5Ab48=
  dependencies:
    babel-runtime "6.x"
    classnames "^2.2.0"
    rmc-feedback "^2.0.0"

rmc-list-view@^0.11.0:
  version "0.11.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/rmc-list-view/-/rmc-list-view-0.11.5.tgz#8e152a5dbec6aec45a8ccd1f33cb8ef140b93a1e"
  integrity sha1-jhUqXb7GrsRajM0fM8uO8UC5Oh4=
  dependencies:
    babel-runtime "6.x"
    classnames "^2.2.5"
    fbjs "^0.8.3"
    prop-types "^15.5.8"
    warning "^3.0.0"
    zscroller "~0.4.0"

rmc-notification@~1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/rmc-notification/-/rmc-notification-1.0.0.tgz#1fcee98f99b9733f7ce63a91d7663a578743d075"
  integrity sha1-H87pj5m5cz985jqR12Y6V4dD0HU=
  dependencies:
    babel-runtime "6.x"
    classnames "2.x"
    prop-types "^15.5.8"
    rc-animate "2.x"
    rc-util "^4.0.4"

rmc-nuka-carousel@~3.0.0:
  version "3.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/rmc-nuka-carousel/-/rmc-nuka-carousel-3.0.1.tgz#a2a997676b0f986354976dac39ec66d8701b4b71"
  integrity sha1-oqmXZ2sPmGNUl22sOexm2HAbS3E=
  dependencies:
    exenv "^1.2.0"
    raf "^3.3.2"

rmc-picker@~5.0.0:
  version "5.0.10"
  resolved "https://nexus.lanmaoly.com/repository/npm/rmc-picker/-/rmc-picker-5.0.10.tgz#9ca0acf45ad2c8afe9015a103a898436d825e18f"
  integrity sha1-nKCs9FrSyK/pAVoQOomENtgl4Y8=
  dependencies:
    babel-runtime "6.x"
    classnames "^2.2.6"
    rmc-dialog "^1.1.1"
    rmc-feedback "^2.0.0"

rmc-pull-to-refresh@~1.0.1:
  version "1.0.11"
  resolved "https://nexus.lanmaoly.com/repository/npm/rmc-pull-to-refresh/-/rmc-pull-to-refresh-1.0.11.tgz#e31fee9f82ab903fa35617509970b28dc61a9857"
  integrity sha1-4x/un4KrkD+jVhdQmXCyjcYamFc=
  dependencies:
    babel-runtime "6.x"
    classnames "^2.2.5"

rmc-steps@~1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/rmc-steps/-/rmc-steps-1.0.0.tgz#0c50d0dff9d3e72e101914300a781993552dc526"
  integrity sha1-DFDQ3/nT5y4QGRQwCngZk1UtxSY=
  dependencies:
    babel-runtime "^6.23.0"
    classnames "^2.2.3"

rmc-tabs@~1.2.0:
  version "1.2.29"
  resolved "https://nexus.lanmaoly.com/repository/npm/rmc-tabs/-/rmc-tabs-1.2.29.tgz#dd2191525debbf8521e85aeb6d97670f652e4c83"
  integrity sha1-3SGRUl3rv4Uh6FrrbZdnD2UuTIM=
  dependencies:
    babel-runtime "6.x"
    rc-gesture "~0.0.18"

rmc-tooltip@~1.0.0:
  version "1.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/rmc-tooltip/-/rmc-tooltip-1.0.1.tgz#5af16a3e8f764fa26d2b11932975bd88b1d848d2"
  integrity sha1-WvFqPo92T6JtKxGTKXW9iLHYSNI=
  dependencies:
    babel-runtime "6.x"
    rmc-trigger "1.x"

rmc-trigger@1.x:
  version "1.0.12"
  resolved "https://nexus.lanmaoly.com/repository/npm/rmc-trigger/-/rmc-trigger-1.0.12.tgz#34df10a16f1fc8f9e8b14d13d58cabe294ab7488"
  integrity sha1-NN8QoW8fyPnosU0T1Yyr4pSrdIg=
  dependencies:
    babel-runtime "6.x"
    rc-animate "2.x"
    rc-util "4.x"
    rmc-align "~1.0.0"

rsvp@^3.3.3:
  version "3.6.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/rsvp/-/rsvp-3.6.2.tgz#2e96491599a96cde1b515d5674a8f7a91452926a"
  integrity sha1-LpZJFZmpbN4bUV1WdKj3qRRSkmo=

run-async@^2.2.0:
  version "2.3.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/run-async/-/run-async-2.3.0.tgz#0371ab4ae0bdd720d4166d7dfda64ff7a445a6c0"
  integrity sha1-A3GrSuC91yDUFm19/aZP96RFpsA=
  dependencies:
    is-promise "^2.1.0"

run-queue@^1.0.0, run-queue@^1.0.3:
  version "1.0.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/run-queue/-/run-queue-1.0.3.tgz#e848396f057d223f24386924618e25694161ec47"
  integrity sha1-6Eg5bwV9Ij8kOGkkYY4laUFh7Ec=
  dependencies:
    aproba "^1.1.1"

rxjs@^6.4.0:
  version "6.5.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/rxjs/-/rxjs-6.5.2.tgz#2e35ce815cd46d84d02a209fb4e5921e051dbec7"
  integrity sha1-LjXOgVzUbYTQKiCftOWSHgUdvsc=
  dependencies:
    tslib "^1.9.0"

safe-buffer@5.1.2, safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/safe-buffer/-/safe-buffer-5.1.2.tgz#991ec69d296e0313747d59bdfd2b745c35f8828d"
  integrity sha1-mR7GnSluAxN0fVm9/St0XDX4go0=

safe-buffer@>=5.1.0, safe-buffer@^5.0.1, safe-buffer@^5.1.0, safe-buffer@^5.1.1, safe-buffer@^5.1.2:
  version "5.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/safe-buffer/-/safe-buffer-5.2.0.tgz#b74daec49b1148f88c64b68d49b1e815c1f2f519"
  integrity sha1-t02uxJsRSPiMZLaNSbHoFcHy9Rk=

safe-regex@^1.1.0:
  version "1.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/safe-regex/-/safe-regex-1.1.0.tgz#40a3669f3b077d1e943d44629e157dd48023bf2e"
  integrity sha1-QKNmnzsHfR6UPURinhV91IAjvy4=
  dependencies:
    ret "~0.1.10"

"safer-buffer@>= 2.1.2 < 3", safer-buffer@^2.0.2, safer-buffer@^2.1.0, safer-buffer@~2.1.0:
  version "2.1.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/safer-buffer/-/safer-buffer-2.1.2.tgz#44fa161b0187b9549dd84bb91802f9bd8385cd6a"
  integrity sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=

sane@^2.0.0:
  version "2.5.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/sane/-/sane-2.5.2.tgz#b4dc1861c21b427e929507a3e751e2a2cb8ab3fa"
  integrity sha1-tNwYYcIbQn6SlQej51HiosuKs/o=
  dependencies:
    anymatch "^2.0.0"
    capture-exit "^1.2.0"
    exec-sh "^0.2.0"
    fb-watchman "^2.0.0"
    micromatch "^3.1.4"
    minimist "^1.1.1"
    walker "~1.0.5"
    watch "~0.18.0"
  optionalDependencies:
    fsevents "^1.2.3"

sass-graph@2.2.5:
  version "2.2.5"
  resolved "https://r.cnpmjs.org/sass-graph/download/sass-graph-2.2.5.tgz#a981c87446b8319d96dce0671e487879bd24c2e8"
  integrity sha1-qYHIdEa4MZ2W3OBnHkh4eb0kwug=
  dependencies:
    glob "^7.0.0"
    lodash "^4.0.0"
    scss-tokenizer "^0.2.3"
    yargs "^13.3.2"

sass-loader@^7.1.0:
  version "7.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/sass-loader/-/sass-loader-7.1.0.tgz#16fd5138cb8b424bf8a759528a1972d72aad069d"
  integrity sha1-Fv1ROMuLQkv4p1lSihly1yqtBp0=
  dependencies:
    clone-deep "^2.0.1"
    loader-utils "^1.0.1"
    lodash.tail "^4.1.1"
    neo-async "^2.5.0"
    pify "^3.0.0"
    semver "^5.5.0"

sax@^1.2.4:
  version "1.2.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/sax/-/sax-1.2.4.tgz#2816234e2378bddc4e5354fab5caa895df7100d9"
  integrity sha1-KBYjTiN4vdxOU1T6tcqold9xANk=

scheduler@^0.13.4:
  version "0.13.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/scheduler/-/scheduler-0.13.4.tgz#8fef05e7a3580c76c0364d2df5e550e4c9140298"
  integrity sha1-j+8F56NYDHbANk0t9eVQ5MkUApg=
  dependencies:
    loose-envify "^1.1.0"
    object-assign "^4.1.1"

scheduler@^0.19.1:
  version "0.19.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/scheduler/-/scheduler-0.19.1.tgz#4f3e2ed2c1a7d65681f4c854fa8c5a1ccb40f196"
  integrity sha1-Tz4u0sGn1laB9MhU+oxaHMtA8ZY=
  dependencies:
    loose-envify "^1.1.0"
    object-assign "^4.1.1"

schema-utils@^1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/schema-utils/-/schema-utils-1.0.0.tgz#0b79a93204d7b600d4b2850d1f66c2a34951c770"
  integrity sha1-C3mpMgTXtgDUsoUNH2bCo0lRx3A=
  dependencies:
    ajv "^6.1.0"
    ajv-errors "^1.0.0"
    ajv-keywords "^3.1.0"

schema-utils@^3.0.0:
  version "3.0.0"
  resolved "https://r.cnpmjs.org/schema-utils/download/schema-utils-3.0.0.tgz#67502f6aa2b66a2d4032b4279a2944978a0913ef"
  integrity sha1-Z1AvaqK2ai1AMrQnmilEl4oJE+8=
  dependencies:
    "@types/json-schema" "^7.0.6"
    ajv "^6.12.5"
    ajv-keywords "^3.5.2"

scss-tokenizer@^0.2.3:
  version "0.2.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/scss-tokenizer/-/scss-tokenizer-0.2.3.tgz#8eb06db9a9723333824d3f5530641149847ce5d1"
  integrity sha1-jrBtualyMzOCTT9VMGQRSYR85dE=
  dependencies:
    js-base64 "^2.1.8"
    source-map "^0.4.2"

seamless-immutable@^7.1.2, seamless-immutable@^7.1.3:
  version "7.1.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/seamless-immutable/-/seamless-immutable-7.1.4.tgz#6e9536def083ddc4dea0207d722e0e80d0f372f8"
  integrity sha1-bpU23vCD3cTeoCB9ci4OgNDzcvg=

select-hose@^2.0.0:
  version "2.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/select-hose/-/select-hose-2.0.0.tgz#625d8658f865af43ec962bfc376a37359a4994ca"
  integrity sha1-Yl2GWPhlr0Psliv8N2o3NZpJlMo=

selfsigned@^1.10.4:
  version "1.10.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/selfsigned/-/selfsigned-1.10.4.tgz#cdd7eccfca4ed7635d47a08bf2d5d3074092e2cd"
  integrity sha1-zdfsz8pO12NdR6CL8tXTB0CS4s0=
  dependencies:
    node-forge "0.7.5"

"semver@2 || 3 || 4 || 5", semver@^5.3.0, semver@^5.4.1, semver@^5.5.0, semver@^5.5.1, semver@^5.6.0:
  version "5.7.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/semver/-/semver-5.7.0.tgz#790a7cf6fea5459bac96110b29b60412dc8ff96b"
  integrity sha1-eQp89v6lRZuslhELKbYEEtyP+Ws=

semver@^5.0.1:
  version "5.7.1"
  resolved "https://r.cnpmjs.org/semver/download/semver-5.7.1.tgz#a954f931aeba508d307bbf069eff0c01c96116f7"
  integrity sha1-qVT5Ma66UI0we78Gnv8MAclhFvc=

semver@^6.1.1:
  version "6.3.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/semver/-/semver-6.3.0.tgz#ee0a64c8af5e8ceea67687b133761e1becbd1d3d"
  integrity sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0=

semver@~5.3.0:
  version "5.3.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/semver/-/semver-5.3.0.tgz#9b2ce5d3de02d17c6012ad326aa6b4d0cf54f94f"
  integrity sha1-myzl094C0XxgEq0yaqa00M9U+U8=

send@0.17.1:
  version "0.17.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/send/-/send-0.17.1.tgz#c1d8b059f7900f7466dd4938bdc44e11ddb376c8"
  integrity sha1-wdiwWfeQD3Rm3Uk4vcROEd2zdsg=
  dependencies:
    debug "2.6.9"
    depd "~1.1.2"
    destroy "~1.0.4"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    fresh "0.5.2"
    http-errors "~1.7.2"
    mime "1.6.0"
    ms "2.1.1"
    on-finished "~2.3.0"
    range-parser "~1.2.1"
    statuses "~1.5.0"

serialize-javascript@^1.4.0, serialize-javascript@^1.7.0:
  version "1.7.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/serialize-javascript/-/serialize-javascript-1.7.0.tgz#d6e0dfb2a3832a8c94468e6eb1db97e55a192a65"
  integrity sha1-1uDfsqODKoyURo5usduX5VoZKmU=

serve-index@^1.9.1:
  version "1.9.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/serve-index/-/serve-index-1.9.1.tgz#d3768d69b1e7d82e5ce050fff5b453bea12a9239"
  integrity sha1-03aNabHn2C5c4FD/9bRTvqEqkjk=
  dependencies:
    accepts "~1.3.4"
    batch "0.6.1"
    debug "2.6.9"
    escape-html "~1.0.3"
    http-errors "~1.6.2"
    mime-types "~2.1.17"
    parseurl "~1.3.2"

serve-static@1.14.1:
  version "1.14.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/serve-static/-/serve-static-1.14.1.tgz#666e636dc4f010f7ef29970a88a674320898b2f9"
  integrity sha1-Zm5jbcTwEPfvKZcKiKZ0MgiYsvk=
  dependencies:
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    parseurl "~1.3.3"
    send "0.17.1"

set-blocking@^2.0.0, set-blocking@~2.0.0:
  version "2.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/set-blocking/-/set-blocking-2.0.0.tgz#045f9782d011ae9a6803ddd382b24392b3d890f7"
  integrity sha1-BF+XgtARrppoA93TgrJDkrPYkPc=

set-value@^2.0.0, set-value@^2.0.1:
  version "2.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/set-value/-/set-value-2.0.1.tgz#a18d40530e6f07de4228c7defe4227af8cad005b"
  integrity sha1-oY1AUw5vB95CKMfe/kInr4ytAFs=
  dependencies:
    extend-shallow "^2.0.1"
    is-extendable "^0.1.1"
    is-plain-object "^2.0.3"
    split-string "^3.0.1"

setimmediate@^1.0.4, setimmediate@^1.0.5:
  version "1.0.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/setimmediate/-/setimmediate-1.0.5.tgz#290cbb232e306942d7d7ea9b83732ab7856f8285"
  integrity sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU=

setprototypeof@1.1.0:
  version "1.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/setprototypeof/-/setprototypeof-1.1.0.tgz#d0bd85536887b6fe7c0d818cb962d9d91c54e656"
  integrity sha1-0L2FU2iHtv58DYGMuWLZ2RxU5lY=

setprototypeof@1.1.1:
  version "1.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/setprototypeof/-/setprototypeof-1.1.1.tgz#7e95acb24aa92f5885e0abef5ba131330d4ae683"
  integrity sha1-fpWsskqpL1iF4KvvW6ExMw1K5oM=

sha.js@^2.4.0, sha.js@^2.4.8:
  version "2.4.11"
  resolved "https://nexus.lanmaoly.com/repository/npm/sha.js/-/sha.js-2.4.11.tgz#37a5cf0b81ecbc6943de109ba2960d1b26584ae7"
  integrity sha1-N6XPC4HsvGlD3hCbopYNGyZYSuc=
  dependencies:
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

shallow-clone@^1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/shallow-clone/-/shallow-clone-1.0.0.tgz#4480cd06e882ef68b2ad88a3ea54832e2c48b571"
  integrity sha1-RIDNBuiC72iyrYij6lSDLixItXE=
  dependencies:
    is-extendable "^0.1.1"
    kind-of "^5.0.0"
    mixin-object "^2.0.1"

shallowequal@^0.2.2:
  version "0.2.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/shallowequal/-/shallowequal-0.2.2.tgz#1e32fd5bcab6ad688a4812cb0cc04efc75c7014e"
  integrity sha1-HjL9W8q2rWiKSBLLDMBO/HXHAU4=
  dependencies:
    lodash.keys "^3.1.2"

shallowequal@^1.0.1, shallowequal@^1.0.2, shallowequal@^1.1.0:
  version "1.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/shallowequal/-/shallowequal-1.1.0.tgz#188d521de95b9087404fd4dcb68b13df0ae4e7f8"
  integrity sha1-GI1SHelbkIdAT9TctosT3wrk5/g=

shebang-command@^1.2.0:
  version "1.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/shebang-command/-/shebang-command-1.2.0.tgz#44aac65b695b03398968c39f363fee5deafdf1ea"
  integrity sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=
  dependencies:
    shebang-regex "^1.0.0"

shebang-regex@^1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/shebang-regex/-/shebang-regex-1.0.0.tgz#da42f49740c0b42db2ca9728571cb190c98efea3"
  integrity sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM=

shell-quote@^1.6.1:
  version "1.6.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/shell-quote/-/shell-quote-1.6.1.tgz#f4781949cce402697127430ea3b3c5476f481767"
  integrity sha1-9HgZSczkAmlxJ0MOo7PFR29IF2c=
  dependencies:
    array-filter "~0.0.0"
    array-map "~0.0.0"
    array-reduce "~0.0.0"
    jsonify "~0.0.0"

shellwords@^0.1.1:
  version "0.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/shellwords/-/shellwords-0.1.1.tgz#d6b9181c1a48d397324c84871efbcfc73fc0654b"
  integrity sha1-1rkYHBpI05cyTISHHvvPxz/AZUs=

signal-exit@^3.0.0, signal-exit@^3.0.2:
  version "3.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/signal-exit/-/signal-exit-3.0.2.tgz#b5fdc08f1287ea1178628e415e25132b73646c6d"
  integrity sha1-tf3AjxKH6hF4Yo5BXiUTK3NkbG0=

sisteransi@^0.1.1:
  version "0.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/sisteransi/-/sisteransi-0.1.1.tgz#5431447d5f7d1675aac667ccd0b865a4994cb3ce"
  integrity sha1-VDFEfV99FnWqxmfM0LhlpJlMs84=

slash@^1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/slash/-/slash-1.0.0.tgz#c41f2f6c39fc16d1cd17ad4b5d896114ae470d55"
  integrity sha1-xB8vbDn8FtHNF61LXYlhFK5HDVU=

slice-ansi@^2.1.0:
  version "2.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/slice-ansi/-/slice-ansi-2.1.0.tgz#cacd7693461a637a5788d92a7dd4fba068e81636"
  integrity sha1-ys12k0YaY3pXiNkqfdT7oGjoFjY=
  dependencies:
    ansi-styles "^3.2.0"
    astral-regex "^1.0.0"
    is-fullwidth-code-point "^2.0.0"

smoothscroll-polyfill@^0.4.4:
  version "0.4.4"
  resolved "https://r.cnpmjs.org/smoothscroll-polyfill/download/smoothscroll-polyfill-0.4.4.tgz#3a259131dc6930e6ca80003e1cb03b603b69abf8"
  integrity sha1-OiWRMdxpMObKgAA+HLA7YDtpq/g=

snapdragon-node@^2.0.1:
  version "2.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/snapdragon-node/-/snapdragon-node-2.1.1.tgz#6c175f86ff14bdb0724563e8f3c1b021a286853b"
  integrity sha1-bBdfhv8UvbByRWPo88GwIaKGhTs=
  dependencies:
    define-property "^1.0.0"
    isobject "^3.0.0"
    snapdragon-util "^3.0.1"

snapdragon-util@^3.0.1:
  version "3.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/snapdragon-util/-/snapdragon-util-3.0.1.tgz#f956479486f2acd79700693f6f7b805e45ab56e2"
  integrity sha1-+VZHlIbyrNeXAGk/b3uAXkWrVuI=
  dependencies:
    kind-of "^3.2.0"

snapdragon@^0.8.1:
  version "0.8.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/snapdragon/-/snapdragon-0.8.2.tgz#64922e7c565b0e14204ba1aa7d6964278d25182d"
  integrity sha1-ZJIufFZbDhQgS6GqfWlkJ40lGC0=
  dependencies:
    base "^0.11.1"
    debug "^2.2.0"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    map-cache "^0.2.2"
    source-map "^0.5.6"
    source-map-resolve "^0.5.0"
    use "^3.1.0"

sockjs-client@1.3.0:
  version "1.3.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/sockjs-client/-/sockjs-client-1.3.0.tgz#12fc9d6cb663da5739d3dc5fb6e8687da95cb177"
  integrity sha1-EvydbLZj2lc509xftuhofalcsXc=
  dependencies:
    debug "^3.2.5"
    eventsource "^1.0.7"
    faye-websocket "~0.11.1"
    inherits "^2.0.3"
    json3 "^3.3.2"
    url-parse "^1.4.3"

sockjs@0.3.19:
  version "0.3.19"
  resolved "https://nexus.lanmaoly.com/repository/npm/sockjs/-/sockjs-0.3.19.tgz#d976bbe800af7bd20ae08598d582393508993c0d"
  integrity sha1-2Xa76ACve9IK4IWY1YI5NQiZPA0=
  dependencies:
    faye-websocket "^0.10.0"
    uuid "^3.0.1"

source-list-map@^2.0.0:
  version "2.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/source-list-map/-/source-list-map-2.0.1.tgz#3993bd873bfc48479cca9ea3a547835c7c154b34"
  integrity sha1-OZO9hzv8SEecyp6jpUeDXHwVSzQ=

source-map-resolve@^0.5.0:
  version "0.5.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/source-map-resolve/-/source-map-resolve-0.5.2.tgz#72e2cc34095543e43b2c62b2c4c10d4a9054f259"
  integrity sha1-cuLMNAlVQ+Q7LGKyxMENSpBU8lk=
  dependencies:
    atob "^2.1.1"
    decode-uri-component "^0.2.0"
    resolve-url "^0.2.1"
    source-map-url "^0.4.0"
    urix "^0.1.0"

source-map-support@^0.4.15:
  version "0.4.18"
  resolved "https://nexus.lanmaoly.com/repository/npm/source-map-support/-/source-map-support-0.4.18.tgz#0286a6de8be42641338594e97ccea75f0a2c585f"
  integrity sha1-Aoam3ovkJkEzhZTpfM6nXwosWF8=
  dependencies:
    source-map "^0.5.6"

source-map-support@^0.5.6, source-map-support@~0.5.12:
  version "0.5.12"
  resolved "https://nexus.lanmaoly.com/repository/npm/source-map-support/-/source-map-support-0.5.12.tgz#b4f3b10d51857a5af0138d3ce8003b201613d599"
  integrity sha1-tPOxDVGFelrwE4086AA7IBYT1Zk=
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map-url@0.4.0, source-map-url@^0.4.0:
  version "0.4.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/source-map-url/-/source-map-url-0.4.0.tgz#3e935d7ddd73631b97659956d55128e87b5084a3"
  integrity sha1-PpNdfd1zYxuXZZlW1VEo6HtQhKM=

source-map@^0.4.2:
  version "0.4.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/source-map/-/source-map-0.4.4.tgz#eba4f5da9c0dc999de68032d8b4f76173652036b"
  integrity sha1-66T12pwNyZneaAMti092FzZSA2s=
  dependencies:
    amdefine ">=0.0.4"

source-map@^0.5.0, source-map@^0.5.3, source-map@^0.5.6, source-map@^0.5.7:
  version "0.5.7"
  resolved "https://nexus.lanmaoly.com/repository/npm/source-map/-/source-map-0.5.7.tgz#8a039d2d1021d22d1ea14c80d8ea468ba2ef3fcc"
  integrity sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=

source-map@^0.6.0, source-map@^0.6.1, source-map@~0.6.0, source-map@~0.6.1:
  version "0.6.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/source-map/-/source-map-0.6.1.tgz#74722af32e9614e9c287a8d0bbde48b5e2f1a263"
  integrity sha1-dHIq8y6WFOnCh6jQu95IteLxomM=

source-map@^0.7.3:
  version "0.7.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/source-map/-/source-map-0.7.3.tgz#5302f8169031735226544092e64981f751750383"
  integrity sha1-UwL4FpAxc1ImVECS5kmB91F1A4M=

spdx-correct@^3.0.0:
  version "3.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/spdx-correct/-/spdx-correct-3.1.0.tgz#fb83e504445268f154b074e218c87c003cd31df4"
  integrity sha1-+4PlBERSaPFUsHTiGMh8ADzTHfQ=
  dependencies:
    spdx-expression-parse "^3.0.0"
    spdx-license-ids "^3.0.0"

spdx-exceptions@^2.1.0:
  version "2.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/spdx-exceptions/-/spdx-exceptions-2.2.0.tgz#2ea450aee74f2a89bfb94519c07fcd6f41322977"
  integrity sha1-LqRQrudPKom/uUUZwH/Nb0EyKXc=

spdx-expression-parse@^3.0.0:
  version "3.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/spdx-expression-parse/-/spdx-expression-parse-3.0.0.tgz#99e119b7a5da00e05491c9fa338b7904823b41d0"
  integrity sha1-meEZt6XaAOBUkcn6M4t5BII7QdA=
  dependencies:
    spdx-exceptions "^2.1.0"
    spdx-license-ids "^3.0.0"

spdx-license-ids@^3.0.0:
  version "3.0.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/spdx-license-ids/-/spdx-license-ids-3.0.5.tgz#3694b5804567a458d3c8045842a6358632f62654"
  integrity sha1-NpS1gEVnpFjTyARYQqY1hjL2JlQ=

spdy-transport@^3.0.0:
  version "3.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/spdy-transport/-/spdy-transport-3.0.0.tgz#00d4863a6400ad75df93361a1608605e5dcdcf31"
  integrity sha1-ANSGOmQArXXfkzYaFghgXl3NzzE=
  dependencies:
    debug "^4.1.0"
    detect-node "^2.0.4"
    hpack.js "^2.1.6"
    obuf "^1.1.2"
    readable-stream "^3.0.6"
    wbuf "^1.7.3"

spdy@^4.0.0:
  version "4.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/spdy/-/spdy-4.0.0.tgz#81f222b5a743a329aa12cea6a390e60e9b613c52"
  integrity sha1-gfIitadDoymqEs6mo5DmDpthPFI=
  dependencies:
    debug "^4.1.0"
    handle-thing "^2.0.0"
    http-deceiver "^1.2.7"
    select-hose "^2.0.0"
    spdy-transport "^3.0.0"

split-string@^3.0.1, split-string@^3.0.2:
  version "3.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/split-string/-/split-string-3.1.0.tgz#7cb09dda3a86585705c64b39a6466038682e8fe2"
  integrity sha1-fLCd2jqGWFcFxks5pkZgOGguj+I=
  dependencies:
    extend-shallow "^3.0.0"

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/sprintf-js/-/sprintf-js-1.0.3.tgz#04e6926f662895354f3dd015203633b857297e2c"
  integrity sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=

sshpk@^1.7.0:
  version "1.16.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/sshpk/-/sshpk-1.16.1.tgz#fb661c0bef29b39db40769ee39fa70093d6f6877"
  integrity sha1-+2YcC+8ps520B2nuOfpwCT1vaHc=
  dependencies:
    asn1 "~0.2.3"
    assert-plus "^1.0.0"
    bcrypt-pbkdf "^1.0.0"
    dashdash "^1.12.0"
    ecc-jsbn "~0.1.1"
    getpass "^0.1.1"
    jsbn "~0.1.0"
    safer-buffer "^2.0.2"
    tweetnacl "~0.14.0"

ssri@^5.2.4:
  version "5.3.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/ssri/-/ssri-5.3.0.tgz#ba3872c9c6d33a0704a7d71ff045e5ec48999d06"
  integrity sha1-ujhyycbTOgcEp9cf8EXl7EiZnQY=
  dependencies:
    safe-buffer "^5.1.1"

ssri@^6.0.1:
  version "6.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/ssri/-/ssri-6.0.1.tgz#2a3c41b28dd45b62b63676ecb74001265ae9edd8"
  integrity sha1-KjxBso3UW2K2Nnbst0ABJlrp7dg=
  dependencies:
    figgy-pudding "^3.5.1"

stack-utils@^1.0.1:
  version "1.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/stack-utils/-/stack-utils-1.0.2.tgz#33eba3897788558bebfc2db059dc158ec36cebb8"
  integrity sha1-M+ujiXeIVYvr/C2wWdwVjsNs67g=

stackframe@^1.0.4:
  version "1.0.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/stackframe/-/stackframe-1.0.4.tgz#357b24a992f9427cba6b545d96a14ed2cbca187b"
  integrity sha1-NXskqZL5Qny6a1RdlqFO0svKGHs=

static-extend@^0.1.1:
  version "0.1.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/static-extend/-/static-extend-0.1.2.tgz#60809c39cbff55337226fd5e0b520f341f1fb5c6"
  integrity sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY=
  dependencies:
    define-property "^0.2.5"
    object-copy "^0.1.0"

"statuses@>= 1.4.0 < 2", "statuses@>= 1.5.0 < 2", statuses@~1.5.0:
  version "1.5.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/statuses/-/statuses-1.5.0.tgz#161c7dac177659fd9811f43771fa99381478628c"
  integrity sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow=

stdout-stream@^1.4.0:
  version "1.4.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/stdout-stream/-/stdout-stream-1.4.1.tgz#5ac174cdd5cd726104aa0c0b2bd83815d8d535de"
  integrity sha1-WsF0zdXNcmEEqgwLK9g4FdjVNd4=
  dependencies:
    readable-stream "^2.0.1"

stealthy-require@^1.1.0:
  version "1.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/stealthy-require/-/stealthy-require-1.1.1.tgz#35b09875b4ff49f26a777e509b3090a3226bf24b"
  integrity sha1-NbCYdbT/SfJqd35QmzCQoyJr8ks=

stream-browserify@^2.0.1:
  version "2.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/stream-browserify/-/stream-browserify-2.0.2.tgz#87521d38a44aa7ee91ce1cd2a47df0cb49dd660b"
  integrity sha1-h1IdOKRKp+6RzhzSpH3wy0ndZgs=
  dependencies:
    inherits "~2.0.1"
    readable-stream "^2.0.2"

stream-each@^1.1.0:
  version "1.2.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/stream-each/-/stream-each-1.2.3.tgz#ebe27a0c389b04fbcc233642952e10731afa9bae"
  integrity sha1-6+J6DDibBPvMIzZClS4Qcxr6m64=
  dependencies:
    end-of-stream "^1.1.0"
    stream-shift "^1.0.0"

stream-http@^2.7.2:
  version "2.8.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/stream-http/-/stream-http-2.8.3.tgz#b2d242469288a5a27ec4fe8933acf623de6514fc"
  integrity sha1-stJCRpKIpaJ+xP6JM6z2I95lFPw=
  dependencies:
    builtin-status-codes "^3.0.0"
    inherits "^2.0.1"
    readable-stream "^2.3.6"
    to-arraybuffer "^1.0.0"
    xtend "^4.0.0"

stream-shift@^1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/stream-shift/-/stream-shift-1.0.0.tgz#d5c752825e5367e786f78e18e445ea223a155952"
  integrity sha1-1cdSgl5TZ+eG944Y5EXqIjoVWVI=

string-length@^2.0.0:
  version "2.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/string-length/-/string-length-2.0.0.tgz#d40dbb686a3ace960c1cffca562bf2c45f8363ed"
  integrity sha1-1A27aGo6zpYMHP/KVivyxF+DY+0=
  dependencies:
    astral-regex "^1.0.0"
    strip-ansi "^4.0.0"

string-width@^1.0.1:
  version "1.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/string-width/-/string-width-1.0.2.tgz#118bdf5b8cdc51a2a7e70d211e07e2b0b9b107d3"
  integrity sha1-EYvfW4zcUaKn5w0hHgfisLmxB9M=
  dependencies:
    code-point-at "^1.0.0"
    is-fullwidth-code-point "^1.0.0"
    strip-ansi "^3.0.0"

"string-width@^1.0.2 || 2", string-width@^2.0.0, string-width@^2.1.0, string-width@^2.1.1:
  version "2.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/string-width/-/string-width-2.1.1.tgz#ab93f27a8dc13d28cac815c462143a6d9012ae9e"
  integrity sha1-q5Pyeo3BPSjKyBXEYhQ6bZASrp4=
  dependencies:
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^4.0.0"

string-width@^3.0.0, string-width@^3.1.0:
  version "3.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/string-width/-/string-width-3.1.0.tgz#22767be21b62af1081574306f69ac51b62203961"
  integrity sha1-InZ74htirxCBV0MG9prFG2IgOWE=
  dependencies:
    emoji-regex "^7.0.1"
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^5.1.0"

string.prototype.padend@^3.0.0:
  version "3.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/string.prototype.padend/-/string.prototype.padend-3.0.0.tgz#f3aaef7c1719f170c5eab1c32bf780d96e21f2f0"
  integrity sha1-86rvfBcZ8XDF6rHDK/eA2W4h8vA=
  dependencies:
    define-properties "^1.1.2"
    es-abstract "^1.4.3"
    function-bind "^1.0.2"

string_decoder@^1.0.0, string_decoder@^1.1.1:
  version "1.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/string_decoder/-/string_decoder-1.2.0.tgz#fe86e738b19544afe70469243b2a1ee9240eae8d"
  integrity sha1-/obnOLGVRK/nBGkkOyoe6SQOro0=
  dependencies:
    safe-buffer "~5.1.0"

string_decoder@~1.1.1:
  version "1.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/string_decoder/-/string_decoder-1.1.1.tgz#9cf1611ba62685d7030ae9e4ba34149c3af03fc8"
  integrity sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=
  dependencies:
    safe-buffer "~5.1.0"

strip-ansi@^3.0.0, strip-ansi@^3.0.1:
  version "3.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/strip-ansi/-/strip-ansi-3.0.1.tgz#6a385fb8853d952d5ff05d0e8aaf94278dc63dcf"
  integrity sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=
  dependencies:
    ansi-regex "^2.0.0"

strip-ansi@^4.0.0:
  version "4.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/strip-ansi/-/strip-ansi-4.0.0.tgz#a8479022eb1ac368a871389b635262c505ee368f"
  integrity sha1-qEeQIusaw2iocTibY1JixQXuNo8=
  dependencies:
    ansi-regex "^3.0.0"

strip-ansi@^5.0.0, strip-ansi@^5.1.0, strip-ansi@^5.2.0:
  version "5.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/strip-ansi/-/strip-ansi-5.2.0.tgz#8c9a536feb6afc962bdfa5b104a5091c1ad9c0ae"
  integrity sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4=
  dependencies:
    ansi-regex "^4.1.0"

strip-bom@3.0.0, strip-bom@^3.0.0:
  version "3.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/strip-bom/-/strip-bom-3.0.0.tgz#2334c18e9c759f7bdd56fdef7e9ae3d588e68ed3"
  integrity sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM=

strip-bom@^2.0.0:
  version "2.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/strip-bom/-/strip-bom-2.0.0.tgz#6219a85616520491f35788bdbf1447a99c7e6b0e"
  integrity sha1-YhmoVhZSBJHzV4i9vxRHqZx+aw4=
  dependencies:
    is-utf8 "^0.2.0"

strip-eof@^1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/strip-eof/-/strip-eof-1.0.0.tgz#bb43ff5598a6eb05d89b59fcd129c983313606bf"
  integrity sha1-u0P/VZim6wXYm1n80SnJgzE2Br8=

strip-indent@^1.0.1:
  version "1.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/strip-indent/-/strip-indent-1.0.1.tgz#0c7962a6adefa7bbd4ac366460a638552ae1a0a2"
  integrity sha1-DHlipq3vp7vUrDZkYKY4VSrhoKI=
  dependencies:
    get-stdin "^4.0.1"

strip-json-comments@^2.0.1, strip-json-comments@~2.0.1:
  version "2.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/strip-json-comments/-/strip-json-comments-2.0.1.tgz#3c531942e908c2697c0ec344858c286c7ca0a60a"
  integrity sha1-PFMZQukIwml8DsNEhYwobHygpgo=

style-loader@^0.23.0:
  version "0.23.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/style-loader/-/style-loader-0.23.1.tgz#cb9154606f3e771ab6c4ab637026a1049174d925"
  integrity sha1-y5FUYG8+dxq2xKtjcCahBJF02SU=
  dependencies:
    loader-utils "^1.1.0"
    schema-utils "^1.0.0"

supports-color@6.1.0, supports-color@^6.1.0:
  version "6.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/supports-color/-/supports-color-6.1.0.tgz#0764abc69c63d5ac842dd4867e8d025e880df8f3"
  integrity sha1-B2Srxpxj1ayELdSGfo0CXogN+PM=
  dependencies:
    has-flag "^3.0.0"

supports-color@^2.0.0:
  version "2.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/supports-color/-/supports-color-2.0.0.tgz#535d045ce6b6363fa40117084629995e9df324c7"
  integrity sha1-U10EXOa2Nj+kARcIRimZXp3zJMc=

supports-color@^3.1.2:
  version "3.2.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/supports-color/-/supports-color-3.2.3.tgz#65ac0504b3954171d8a64946b2ae3cbb8a5f54f6"
  integrity sha1-ZawFBLOVQXHYpklGsq48u4pfVPY=
  dependencies:
    has-flag "^1.0.0"

supports-color@^5.3.0, supports-color@^5.4.0:
  version "5.5.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/supports-color/-/supports-color-5.5.0.tgz#e2e69a44ac8772f78a1ec0b35b689df6530efc8f"
  integrity sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=
  dependencies:
    has-flag "^3.0.0"

symbol-observable@^1.2.0:
  version "1.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/symbol-observable/-/symbol-observable-1.2.0.tgz#c22688aed4eab3cdc2dfeacbb561660560a00804"
  integrity sha1-wiaIrtTqs83C3+rLtWFmBWCgCAQ=

symbol-tree@^3.2.2:
  version "3.2.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/symbol-tree/-/symbol-tree-3.2.2.tgz#ae27db38f660a7ae2e1c3b7d1bc290819b8519e6"
  integrity sha1-rifbOPZgp64uHDt9G8KQgZuFGeY=

table@^5.2.3:
  version "5.4.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/table/-/table-5.4.4.tgz#6e0f88fdae3692793d1077fd172a4667afe986a6"
  integrity sha1-bg+I/a42knk9EHf9FypGZ6/phqY=
  dependencies:
    ajv "^6.10.2"
    lodash "^4.17.14"
    slice-ansi "^2.1.0"
    string-width "^3.0.0"

tapable@^1.0.0, tapable@^1.1.0:
  version "1.1.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/tapable/-/tapable-1.1.3.tgz#a1fccc06b58db61fd7a45da2da44f5f3a3e67ba2"
  integrity sha1-ofzMBrWNth/XpF2i2kT186Pme6I=

tar@^2.0.0:
  version "2.2.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/tar/-/tar-2.2.2.tgz#0ca8848562c7299b8b446ff6a4d60cdbb23edc40"
  integrity sha1-DKiEhWLHKZuLRG/2pNYM27I+3EA=
  dependencies:
    block-stream "*"
    fstream "^1.0.12"
    inherits "2"

tar@^4:
  version "4.4.10"
  resolved "https://nexus.lanmaoly.com/repository/npm/tar/-/tar-4.4.10.tgz#946b2810b9a5e0b26140cf78bea6b0b0d689eba1"
  integrity sha1-lGsoELml4LJhQM94vqawsNaJ66E=
  dependencies:
    chownr "^1.1.1"
    fs-minipass "^1.2.5"
    minipass "^2.3.5"
    minizlib "^1.2.1"
    mkdirp "^0.5.0"
    safe-buffer "^5.1.2"
    yallist "^3.0.3"

terser-webpack-plugin@^1.1.0:
  version "1.3.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/terser-webpack-plugin/-/terser-webpack-plugin-1.3.0.tgz#69aa22426299f4b5b3775cbed8cb2c5d419aa1d4"
  integrity sha1-aaoiQmKZ9LWzd1y+2MssXUGaodQ=
  dependencies:
    cacache "^11.3.2"
    find-cache-dir "^2.0.0"
    is-wsl "^1.1.0"
    loader-utils "^1.2.3"
    schema-utils "^1.0.0"
    serialize-javascript "^1.7.0"
    source-map "^0.6.1"
    terser "^4.0.0"
    webpack-sources "^1.3.0"
    worker-farm "^1.7.0"

terser@^4.0.0:
  version "4.1.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/terser/-/terser-4.1.2.tgz#b2656c8a506f7ce805a3f300a2ff48db022fa391"
  integrity sha1-smVsilBvfOgFo/MAov9I2wIvo5E=
  dependencies:
    commander "^2.20.0"
    source-map "~0.6.1"
    source-map-support "~0.5.12"

test-exclude@^4.2.1:
  version "4.2.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/test-exclude/-/test-exclude-4.2.3.tgz#a9a5e64474e4398339245a0a769ad7c2f4a97c20"
  integrity sha1-qaXmRHTkOYM5JFoKdprXwvSpfCA=
  dependencies:
    arrify "^1.0.1"
    micromatch "^2.3.11"
    object-assign "^4.1.0"
    read-pkg-up "^1.0.1"
    require-main-filename "^1.0.1"

text-table@^0.2.0:
  version "0.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/text-table/-/text-table-0.2.0.tgz#7f5ee823ae805207c00af2df4a84ec3fcfa570b4"
  integrity sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=

throat@^4.0.0:
  version "4.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/throat/-/throat-4.1.0.tgz#89037cbc92c56ab18926e6ba4cbb200e15672a6a"
  integrity sha1-iQN8vJLFarGJJua6TLsgDhVnKmo=

through2@^2.0.0:
  version "2.0.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/through2/-/through2-2.0.5.tgz#01c1e39eb31d07cb7d03a96a70823260b23132cd"
  integrity sha1-AcHjnrMdB8t9A6lqcIIyYLIxMs0=
  dependencies:
    readable-stream "~2.3.6"
    xtend "~4.0.1"

through@^2.3.6:
  version "2.3.8"
  resolved "https://nexus.lanmaoly.com/repository/npm/through/-/through-2.3.8.tgz#0dd4c9ffaabc357960b1b724115d7e0e86a2e1f5"
  integrity sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=

thunky@^1.0.2:
  version "1.0.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/thunky/-/thunky-1.0.3.tgz#f5df732453407b09191dae73e2a8cc73f381a826"
  integrity sha1-9d9zJFNAewkZHa5z4qjMc/OBqCY=

timers-browserify@^2.0.4:
  version "2.0.10"
  resolved "https://nexus.lanmaoly.com/repository/npm/timers-browserify/-/timers-browserify-2.0.10.tgz#1d28e3d2aadf1d5a5996c4e9f95601cd053480ae"
  integrity sha1-HSjj0qrfHVpZlsTp+VYBzQU0gK4=
  dependencies:
    setimmediate "^1.0.4"

tiny-invariant@^1.0.2:
  version "1.0.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/tiny-invariant/-/tiny-invariant-1.0.3.tgz#91efaaa0269ccb6271f0296aeedb05fc3e067b7a"
  integrity sha1-ke+qoCacy2Jx8Clq7tsF/D4Ge3o=

tiny-warning@^1.0.0:
  version "1.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/tiny-warning/-/tiny-warning-1.0.2.tgz#1dfae771ee1a04396bdfde27a3adcebc6b648b28"
  integrity sha1-Hfrnce4aBDlr394no63OvGtkiyg=

tmp@^0.0.33:
  version "0.0.33"
  resolved "https://nexus.lanmaoly.com/repository/npm/tmp/-/tmp-0.0.33.tgz#6d34335889768d21b2bcda0aa277ced3b1bfadf9"
  integrity sha1-bTQzWIl2jSGyvNoKonfO07G/rfk=
  dependencies:
    os-tmpdir "~1.0.2"

tmpl@1.0.x:
  version "1.0.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/tmpl/-/tmpl-1.0.4.tgz#23640dd7b42d00433911140820e5cf440e521dd1"
  integrity sha1-I2QN17QtAEM5ERQIIOXPRA5SHdE=

to-arraybuffer@^1.0.0:
  version "1.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/to-arraybuffer/-/to-arraybuffer-1.0.1.tgz#7d229b1fcc637e466ca081180836a7aabff83f43"
  integrity sha1-fSKbH8xjfkZsoIEYCDanqr/4P0M=

to-fast-properties@^1.0.3:
  version "1.0.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/to-fast-properties/-/to-fast-properties-1.0.3.tgz#b83571fa4d8c25b82e231b06e3a3055de4ca1a47"
  integrity sha1-uDVx+k2MJbguIxsG46MFXeTKGkc=

to-fast-properties@^2.0.0:
  version "2.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/to-fast-properties/-/to-fast-properties-2.0.0.tgz#dc5e698cbd079265bc73e0377681a4e4e83f616e"
  integrity sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4=

to-object-path@^0.3.0:
  version "0.3.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/to-object-path/-/to-object-path-0.3.0.tgz#297588b7b0e7e0ac08e04e672f85c1f4999e17af"
  integrity sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68=
  dependencies:
    kind-of "^3.0.2"

to-regex-range@^2.1.0:
  version "2.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/to-regex-range/-/to-regex-range-2.1.1.tgz#7c80c17b9dfebe599e27367e0d4dd5590141db38"
  integrity sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg=
  dependencies:
    is-number "^3.0.0"
    repeat-string "^1.6.1"

to-regex@^3.0.1, to-regex@^3.0.2:
  version "3.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/to-regex/-/to-regex-3.0.2.tgz#13cfdd9b336552f30b51f33a8ae1b42a7a7599ce"
  integrity sha1-E8/dmzNlUvMLUfM6iuG0Knp1mc4=
  dependencies:
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    regex-not "^1.0.2"
    safe-regex "^1.1.0"

toggle-selection@^1.0.6:
  version "1.0.6"
  resolved "https://nexus.lanmaoly.com/repository/npm/toggle-selection/-/toggle-selection-1.0.6.tgz#6e45b1263f2017fa0acc7d89d78b15b8bf77da32"
  integrity sha1-bkWxJj8gF/oKzH2J14sVuL932jI=

toidentifier@1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/toidentifier/-/toidentifier-1.0.0.tgz#7e1be3470f1e77948bc43d94a3c8f4d7752ba553"
  integrity sha1-fhvjRw8ed5SLxD2Uo8j013UrpVM=

toposort@^1.0.0:
  version "1.0.7"
  resolved "https://nexus.lanmaoly.com/repository/npm/toposort/-/toposort-1.0.7.tgz#2e68442d9f64ec720b8cc89e6443ac6caa950029"
  integrity sha1-LmhELZ9k7HILjMieZEOsbKqVACk=

tough-cookie@>=2.3.3:
  version "3.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/tough-cookie/-/tough-cookie-3.0.1.tgz#9df4f57e739c26930a018184887f4adb7dca73b2"
  integrity sha1-nfT1fnOcJpMKAYGEiH9K233Kc7I=
  dependencies:
    ip-regex "^2.1.0"
    psl "^1.1.28"
    punycode "^2.1.1"

tough-cookie@^2.3.4:
  version "2.5.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/tough-cookie/-/tough-cookie-2.5.0.tgz#cd9fb2a0aa1d5a12b473bd9fb96fa3dcff65ade2"
  integrity sha1-zZ+yoKodWhK0c72fuW+j3P9lreI=
  dependencies:
    psl "^1.1.28"
    punycode "^2.1.1"

tough-cookie@~2.4.3:
  version "2.4.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/tough-cookie/-/tough-cookie-2.4.3.tgz#53f36da3f47783b0925afa06ff9f3b165280f781"
  integrity sha1-U/Nto/R3g7CSWvoG/587FlKA94E=
  dependencies:
    psl "^1.1.24"
    punycode "^1.4.1"

tr46@^1.0.1:
  version "1.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/tr46/-/tr46-1.0.1.tgz#a8b13fd6bfd2489519674ccde55ba3693b706d09"
  integrity sha1-qLE/1r/SSJUZZ0zN5VujaTtwbQk=
  dependencies:
    punycode "^2.1.0"

trim-newlines@^1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/trim-newlines/-/trim-newlines-1.0.0.tgz#5887966bb582a4503a41eb524f7d35011815a613"
  integrity sha1-WIeWa7WCpFA6QetST301ARgVphM=

trim-right@^1.0.1:
  version "1.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/trim-right/-/trim-right-1.0.1.tgz#cb2e1203067e0c8de1f614094b9fe45704ea6003"
  integrity sha1-yy4SAwZ+DI3h9hQJS5/kVwTqYAM=

"true-case-path@^1.0.2":
  version "1.0.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/true-case-path/-/true-case-path-1.0.3.tgz#f813b5a8c86b40da59606722b144e3225799f47d"
  integrity sha1-+BO1qMhrQNpZYGcisUTjIleZ9H0=
  dependencies:
    glob "^7.1.2"

tryer@^1.0.1:
  version "1.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/tryer/-/tryer-1.0.1.tgz#f2c85406800b9b0f74c9f7465b81eaad241252f8"
  integrity sha1-8shUBoALmw90yfdGW4HqrSQSUvg=

ts-loader@^5.2.2:
  version "5.4.5"
  resolved "https://r.cnpmjs.org/ts-loader/download/ts-loader-5.4.5.tgz#a0c1f034b017a9344cef0961bfd97cc192492b8b"
  integrity sha1-oMHwNLAXqTRM7wlhv9l8wZJJK4s=
  dependencies:
    chalk "^2.3.0"
    enhanced-resolve "^4.0.0"
    loader-utils "^1.0.2"
    micromatch "^3.1.4"
    semver "^5.0.1"

tslib@^1.10.0:
  version "1.14.1"
  resolved "https://r.cnpmjs.org/tslib/download/tslib-1.14.1.tgz#cf2d38bdc34a134bcaf1091c41f6619e2f672d00"
  integrity sha1-zy04vcNKE0vK8QkcQfZhni9nLQA=

tslib@^1.9.0:
  version "1.10.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/tslib/-/tslib-1.10.0.tgz#c3c19f95973fb0a62973fb09d90d961ee43e5c8a"
  integrity sha1-w8GflZc/sKYpc/sJ2Q2WHuQ+XIo=

tty-browserify@0.0.0:
  version "0.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/tty-browserify/-/tty-browserify-0.0.0.tgz#a157ba402da24e9bf957f9aa69d524eed42901a6"
  integrity sha1-oVe6QC2iTpv5V/mqadUk7tQpAaY=

tunnel-agent@^0.6.0:
  version "0.6.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/tunnel-agent/-/tunnel-agent-0.6.0.tgz#27a5dea06b36b04a0a9966774b290868f0fc40fd"
  integrity sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=
  dependencies:
    safe-buffer "^5.0.1"

tween-functions@^1.0.1:
  version "1.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/tween-functions/-/tween-functions-1.2.0.tgz#1ae3a50e7c60bb3def774eac707acbca73bbc3ff"
  integrity sha1-GuOlDnxguz3vd06scHrLynO7w/8=

tweetnacl@^0.14.3, tweetnacl@~0.14.0:
  version "0.14.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/tweetnacl/-/tweetnacl-0.14.5.tgz#5ae68177f192d4456269d108afa93ff8743f4f64"
  integrity sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q=

type-check@~0.3.2:
  version "0.3.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/type-check/-/type-check-0.3.2.tgz#5884cab512cf1d355e3fb784f30804b2b520db72"
  integrity sha1-WITKtRLPHTVeP7eE8wgEsrUg23I=
  dependencies:
    prelude-ls "~1.1.2"

type-is@~1.6.17, type-is@~1.6.18:
  version "1.6.18"
  resolved "https://nexus.lanmaoly.com/repository/npm/type-is/-/type-is-1.6.18.tgz#4e552cd05df09467dcbc4ef739de89f2cf37c131"
  integrity sha1-TlUs0F3wlGfcvE73Od6J8s83wTE=
  dependencies:
    media-typer "0.3.0"
    mime-types "~2.1.24"

typedarray@^0.0.6:
  version "0.0.6"
  resolved "https://nexus.lanmaoly.com/repository/npm/typedarray/-/typedarray-0.0.6.tgz#867ac74e3864187b1d3d47d996a78ec5c8830777"
  integrity sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c=

typings-for-css-modules-loader@^1.7.0:
  version "1.7.0"
  resolved "https://r.cnpmjs.org/typings-for-css-modules-loader/download/typings-for-css-modules-loader-1.7.0.tgz#a9b5c5a0e19b719d616edfc72855ab47dedd00ae"
  integrity sha1-qbXFoOGbcZ1hbt/HKFWrR97dAK4=
  dependencies:
    colour "0.7.1"
    graceful-fs "4.1.4"
    loader-utils "0.2.16"

ua-parser-js@^0.7.18:
  version "0.7.19"
  resolved "https://nexus.lanmaoly.com/repository/npm/ua-parser-js/-/ua-parser-js-0.7.19.tgz#94151be4c0a7fb1d001af7022fdaca4642659e4b"
  integrity sha1-lBUb5MCn+x0AGvcCL9rKRkJlnks=

uglify-js@3.4.x:
  version "3.4.10"
  resolved "https://nexus.lanmaoly.com/repository/npm/uglify-js/-/uglify-js-3.4.10.tgz#9ad9563d8eb3acdfb8d38597d2af1d815f6a755f"
  integrity sha1-mtlWPY6zrN+404WX0q8dgV9qdV8=
  dependencies:
    commander "~2.19.0"
    source-map "~0.6.1"

uglify-js@^3.1.4, uglify-js@^3.6.0:
  version "3.6.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/uglify-js/-/uglify-js-3.6.0.tgz#704681345c53a8b2079fb6cec294b05ead242ff5"
  integrity sha1-cEaBNFxTqLIHn7bOwpSwXq0kL/U=
  dependencies:
    commander "~2.20.0"
    source-map "~0.6.1"

uglifyjs-webpack-plugin@^2.2.0:
  version "2.2.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/uglifyjs-webpack-plugin/-/uglifyjs-webpack-plugin-2.2.0.tgz#e75bc80e7f1937f725954c9b4c5a1e967ea9d0d7"
  integrity sha1-51vIDn8ZN/cllUybTFoeln6p0Nc=
  dependencies:
    cacache "^12.0.2"
    find-cache-dir "^2.1.0"
    is-wsl "^1.1.0"
    schema-utils "^1.0.0"
    serialize-javascript "^1.7.0"
    source-map "^0.6.1"
    uglify-js "^3.6.0"
    webpack-sources "^1.4.0"
    worker-farm "^1.7.0"

unicode-canonical-property-names-ecmascript@^1.0.4:
  version "1.0.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-1.0.4.tgz#2619800c4c825800efdd8343af7dd9933cbe2818"
  integrity sha1-JhmADEyCWADv3YNDr33Zkzy+KBg=

unicode-match-property-ecmascript@^1.0.4:
  version "1.0.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/unicode-match-property-ecmascript/-/unicode-match-property-ecmascript-1.0.4.tgz#8ed2a32569961bce9227d09cd3ffbb8fed5f020c"
  integrity sha1-jtKjJWmWG86SJ9Cc0/+7j+1fAgw=
  dependencies:
    unicode-canonical-property-names-ecmascript "^1.0.4"
    unicode-property-aliases-ecmascript "^1.0.4"

unicode-match-property-value-ecmascript@^1.1.0:
  version "1.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/unicode-match-property-value-ecmascript/-/unicode-match-property-value-ecmascript-1.1.0.tgz#5b4b426e08d13a80365e0d657ac7a6c1ec46a277"
  integrity sha1-W0tCbgjROoA2Xg1lesemwexGonc=

unicode-property-aliases-ecmascript@^1.0.4:
  version "1.0.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-1.0.5.tgz#a9cc6cc7ce63a0a3023fc99e341b94431d405a57"
  integrity sha1-qcxsx85joKMCP8meNBuUQx1AWlc=

union-value@^1.0.0:
  version "1.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/union-value/-/union-value-1.0.1.tgz#0b6fe7b835aecda61c6ea4d4f02c14221e109847"
  integrity sha1-C2/nuDWuzaYcbqTU8CwUIh4QmEc=
  dependencies:
    arr-union "^3.1.0"
    get-value "^2.0.6"
    is-extendable "^0.1.1"
    set-value "^2.0.1"

unique-filename@^1.1.0, unique-filename@^1.1.1:
  version "1.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/unique-filename/-/unique-filename-1.1.1.tgz#1d69769369ada0583103a1e6ae87681b56573230"
  integrity sha1-HWl2k2mtoFgxA6HmrodoG1ZXMjA=
  dependencies:
    unique-slug "^2.0.0"

unique-slug@^2.0.0:
  version "2.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/unique-slug/-/unique-slug-2.0.2.tgz#baabce91083fc64e945b0f3ad613e264f7cd4e6c"
  integrity sha1-uqvOkQg/xk6UWw861hPiZPfNTmw=
  dependencies:
    imurmurhash "^0.1.4"

universalify@^0.1.0:
  version "0.1.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/universalify/-/universalify-0.1.2.tgz#b646f69be3942dabcecc9d6639c80dc105efaa66"
  integrity sha1-tkb2m+OULavOzJ1mOcgNwQXvqmY=

unpipe@1.0.0, unpipe@~1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/unpipe/-/unpipe-1.0.0.tgz#b2bf4ee8514aae6165b4817829d21b2ef49904ec"
  integrity sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=

unset-value@^1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/unset-value/-/unset-value-1.0.0.tgz#8376873f7d2335179ffb1e6fc3a8ed0dfc8ab559"
  integrity sha1-g3aHP30jNRef+x5vw6jtDfyKtVk=
  dependencies:
    has-value "^0.3.1"
    isobject "^3.0.0"

upath@^1.1.1:
  version "1.1.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/upath/-/upath-1.1.2.tgz#3db658600edaeeccbe6db5e684d67ee8c2acd068"
  integrity sha1-PbZYYA7a7sy+bbXmhNZ+6MKs0Gg=

upper-case@^1.1.1:
  version "1.1.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/upper-case/-/upper-case-1.1.3.tgz#f6b4501c2ec4cdd26ba78be7222961de77621598"
  integrity sha1-9rRQHC7EzdJrp4vnIilh3ndiFZg=

uri-js@^4.2.2:
  version "4.2.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/uri-js/-/uri-js-4.2.2.tgz#94c540e1ff772956e2299507c010aea6c8838eb0"
  integrity sha1-lMVA4f93KVbiKZUHwBCupsiDjrA=
  dependencies:
    punycode "^2.1.0"

urix@^0.1.0:
  version "0.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/urix/-/urix-0.1.0.tgz#da937f7a62e21fec1fd18d49b35c2935067a6c72"
  integrity sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI=

url-loader@^1.1.1, url-loader@^1.1.2:
  version "1.1.2"
  resolved "https://r.cnpmjs.org/url-loader/download/url-loader-1.1.2.tgz#b971d191b83af693c5e3fea4064be9e1f2d7f8d8"
  integrity sha1-uXHRkbg69pPF4/6kBkvp4fLX+Ng=
  dependencies:
    loader-utils "^1.1.0"
    mime "^2.0.3"
    schema-utils "^1.0.0"

url-parse@^1.4.3:
  version "1.4.7"
  resolved "https://nexus.lanmaoly.com/repository/npm/url-parse/-/url-parse-1.4.7.tgz#a8a83535e8c00a316e403a5db4ac1b9b853ae278"
  integrity sha1-qKg1NejACjFuQDpdtKwbm4U64ng=
  dependencies:
    querystringify "^2.1.1"
    requires-port "^1.0.0"

url@^0.11.0:
  version "0.11.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/url/-/url-0.11.0.tgz#3838e97cfc60521eb73c525a8e55bfdd9e2e28f1"
  integrity sha1-ODjpfPxgUh63PFJajlW/3Z4uKPE=
  dependencies:
    punycode "1.3.2"
    querystring "0.2.0"

use@^3.1.0:
  version "3.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/use/-/use-3.1.1.tgz#d50c8cac79a19fbc20f2911f56eb973f4e10070f"
  integrity sha1-1QyMrHmhn7wg8pEfVuuXP04QBw8=

util-deprecate@^1.0.1, util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/util-deprecate/-/util-deprecate-1.0.2.tgz#450d4dc9fa70de732762fbd2d4a28981419a0ccf"
  integrity sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=

util.promisify@1.0.0, util.promisify@^1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/util.promisify/-/util.promisify-1.0.0.tgz#440f7165a459c9a16dc145eb8e72f35687097030"
  integrity sha1-RA9xZaRZyaFtwUXrjnLzVocJcDA=
  dependencies:
    define-properties "^1.1.2"
    object.getownpropertydescriptors "^2.0.3"

util@0.10.3:
  version "0.10.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/util/-/util-0.10.3.tgz#7afb1afe50805246489e3db7fe0ed379336ac0f9"
  integrity sha1-evsa/lCAUkZInj23/g7TeTNqwPk=
  dependencies:
    inherits "2.0.1"

util@^0.11.0:
  version "0.11.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/util/-/util-0.11.1.tgz#3236733720ec64bb27f6e26f421aaa2e1b588d61"
  integrity sha1-MjZzNyDsZLsn9uJvQhqqLhtYjWE=
  dependencies:
    inherits "2.0.3"

utila@^0.4.0, utila@~0.4:
  version "0.4.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/utila/-/utila-0.4.0.tgz#8a16a05d445657a3aea5eecc5b12a4fa5379772c"
  integrity sha1-ihagXURWV6Oupe7MWxKk+lN5dyw=

utils-merge@1.0.1:
  version "1.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/utils-merge/-/utils-merge-1.0.1.tgz#9f95710f50a267947b2ccc124741c1028427e713"
  integrity sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=

uuid@^3.0.1, uuid@^3.3.2:
  version "3.3.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/uuid/-/uuid-3.3.2.tgz#1b4af4955eb3077c501c23872fc6513811587131"
  integrity sha1-G0r0lV6zB3xQHCOHL8ZROBFYcTE=

v8-compile-cache@2.0.3:
  version "2.0.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/v8-compile-cache/-/v8-compile-cache-2.0.3.tgz#00f7494d2ae2b688cfe2899df6ed2c54bef91dbe"
  integrity sha1-APdJTSritojP4omd9u0sVL75Hb4=

validate-npm-package-license@^3.0.1:
  version "3.0.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/validate-npm-package-license/-/validate-npm-package-license-3.0.4.tgz#fc91f6b9c7ba15c857f4cb2c5defeec39d4f410a"
  integrity sha1-/JH2uce6FchX9MssXe/uw51PQQo=
  dependencies:
    spdx-correct "^3.0.0"
    spdx-expression-parse "^3.0.0"

value-equal@^0.4.0:
  version "0.4.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/value-equal/-/value-equal-0.4.0.tgz#c5bdd2f54ee093c04839d71ce2e4758a6890abc7"
  integrity sha1-xb3S9U7gk8BIOdcc4uR1imiQq8c=

vary@~1.1.2:
  version "1.1.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/vary/-/vary-1.1.2.tgz#2299f02c6ded30d4a5961b0b9f74524a18f634fc"
  integrity sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=

verror@1.10.0:
  version "1.10.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/verror/-/verror-1.10.0.tgz#3a105ca17053af55d6e270c1f8288682e18da400"
  integrity sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA=
  dependencies:
    assert-plus "^1.0.0"
    core-util-is "1.0.2"
    extsprintf "^1.2.0"

vm-browserify@^1.0.1:
  version "1.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/vm-browserify/-/vm-browserify-1.1.0.tgz#bd76d6a23323e2ca8ffa12028dc04559c75f9019"
  integrity sha1-vXbWojMj4sqP+hICjcBFWcdfkBk=

w3c-hr-time@^1.0.1:
  version "1.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/w3c-hr-time/-/w3c-hr-time-1.0.1.tgz#82ac2bff63d950ea9e3189a58a65625fedf19045"
  integrity sha1-gqwr/2PZUOqeMYmlimViX+3xkEU=
  dependencies:
    browser-process-hrtime "^0.1.2"

walker@~1.0.5:
  version "1.0.7"
  resolved "https://nexus.lanmaoly.com/repository/npm/walker/-/walker-1.0.7.tgz#2f7f9b8fd10d677262b18a884e28d19618e028fb"
  integrity sha1-L3+bj9ENZ3JisYqITijRlhjgKPs=
  dependencies:
    makeerror "1.0.x"

warning@^3.0.0:
  version "3.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/warning/-/warning-3.0.0.tgz#32e5377cb572de4ab04753bdf8821c01ed605b7c"
  integrity sha1-MuU3fLVy3kqwR1O9+IIcAe1gW3w=
  dependencies:
    loose-envify "^1.0.0"

warning@^4.0.1, warning@^4.0.3:
  version "4.0.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/warning/-/warning-4.0.3.tgz#16e9e077eb8a86d6af7d64aa1e05fd85b4678ca3"
  integrity sha1-Fungd+uKhtavfWSqHgX9hbRnjKM=
  dependencies:
    loose-envify "^1.0.0"

watch@~0.18.0:
  version "0.18.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/watch/-/watch-0.18.0.tgz#28095476c6df7c90c963138990c0a5423eb4b986"
  integrity sha1-KAlUdsbffJDJYxOJkMClQj60uYY=
  dependencies:
    exec-sh "^0.2.0"
    minimist "^1.2.0"

watchpack@^1.5.0:
  version "1.6.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/watchpack/-/watchpack-1.6.0.tgz#4bc12c2ebe8aa277a71f1d3f14d685c7b446cd00"
  integrity sha1-S8EsLr6KonenHx0/FNaFx7RGzQA=
  dependencies:
    chokidar "^2.0.2"
    graceful-fs "^4.1.2"
    neo-async "^2.5.0"

wbuf@^1.1.0, wbuf@^1.7.3:
  version "1.7.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/wbuf/-/wbuf-1.7.3.tgz#c1d8d149316d3ea852848895cb6a0bfe887b87df"
  integrity sha1-wdjRSTFtPqhShIiVy2oL/oh7h98=
  dependencies:
    minimalistic-assert "^1.0.0"

webidl-conversions@^4.0.2:
  version "4.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/webidl-conversions/-/webidl-conversions-4.0.2.tgz#a855980b1f0b6b359ba1d5d9fb39ae941faa63ad"
  integrity sha1-qFWYCx8LazWbodXZ+zmulB+qY60=

webpack-bundle-analyzer@^3.4.1:
  version "3.4.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/webpack-bundle-analyzer/-/webpack-bundle-analyzer-3.4.1.tgz#430544c7ba1631baccf673475ca8300cb74a3c47"
  integrity sha1-QwVEx7oWMbrM9nNHXKgwDLdKPEc=
  dependencies:
    acorn "^6.0.7"
    acorn-walk "^6.1.1"
    bfj "^6.1.1"
    chalk "^2.4.1"
    commander "^2.18.0"
    ejs "^2.6.1"
    express "^4.16.3"
    filesize "^3.6.1"
    gzip-size "^5.0.0"
    lodash "^4.17.15"
    mkdirp "^0.5.1"
    opener "^1.5.1"
    ws "^6.0.0"

webpack-cli@^3.1.0:
  version "3.3.6"
  resolved "https://nexus.lanmaoly.com/repository/npm/webpack-cli/-/webpack-cli-3.3.6.tgz#2c8c399a2642133f8d736a359007a052e060032c"
  integrity sha1-LIw5miZCEz+Nc2o1kAegUuBgAyw=
  dependencies:
    chalk "2.4.2"
    cross-spawn "6.0.5"
    enhanced-resolve "4.1.0"
    findup-sync "3.0.0"
    global-modules "2.0.0"
    import-local "2.0.0"
    interpret "1.2.0"
    loader-utils "1.2.3"
    supports-color "6.1.0"
    v8-compile-cache "2.0.3"
    yargs "13.2.4"

webpack-dev-middleware@^3.7.0:
  version "3.7.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/webpack-dev-middleware/-/webpack-dev-middleware-3.7.0.tgz#ef751d25f4e9a5c8a35da600c5fda3582b5c6cff"
  integrity sha1-73UdJfTppcijXaYAxf2jWCtcbP8=
  dependencies:
    memory-fs "^0.4.1"
    mime "^2.4.2"
    range-parser "^1.2.1"
    webpack-log "^2.0.0"

webpack-dev-server@^3.1.7:
  version "3.7.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/webpack-dev-server/-/webpack-dev-server-3.7.2.tgz#f79caa5974b7f8b63268ef5421222a8486d792f5"
  integrity sha1-95yqWXS3+LYyaO9UISIqhIbXkvU=
  dependencies:
    ansi-html "0.0.7"
    bonjour "^3.5.0"
    chokidar "^2.1.6"
    compression "^1.7.4"
    connect-history-api-fallback "^1.6.0"
    debug "^4.1.1"
    del "^4.1.1"
    express "^4.17.1"
    html-entities "^1.2.1"
    http-proxy-middleware "^0.19.1"
    import-local "^2.0.0"
    internal-ip "^4.3.0"
    ip "^1.1.5"
    killable "^1.0.1"
    loglevel "^1.6.3"
    opn "^5.5.0"
    p-retry "^3.0.1"
    portfinder "^1.0.20"
    schema-utils "^1.0.0"
    selfsigned "^1.10.4"
    semver "^6.1.1"
    serve-index "^1.9.1"
    sockjs "0.3.19"
    sockjs-client "1.3.0"
    spdy "^4.0.0"
    strip-ansi "^3.0.1"
    supports-color "^6.1.0"
    url "^0.11.0"
    webpack-dev-middleware "^3.7.0"
    webpack-log "^2.0.0"
    yargs "12.0.5"

webpack-inline-manifest-plugin@^4.0.1:
  version "4.0.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/webpack-inline-manifest-plugin/-/webpack-inline-manifest-plugin-4.0.1.tgz#4fc843fcf076e8512f167dc705108b3eacafc098"
  integrity sha1-T8hD/PB26FEvFn3HBRCLPqyvwJg=
  dependencies:
    source-map-url "0.4.0"

webpack-log@^2.0.0:
  version "2.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/webpack-log/-/webpack-log-2.0.0.tgz#5b7928e0637593f119d32f6227c1e0ac31e1b47f"
  integrity sha1-W3ko4GN1k/EZ0y9iJ8HgrDHhtH8=
  dependencies:
    ansi-colors "^3.0.0"
    uuid "^3.3.2"

webpack-manifest-plugin@^2.0.3:
  version "2.0.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/webpack-manifest-plugin/-/webpack-manifest-plugin-2.0.4.tgz#e4ca2999b09557716b8ba4475fb79fab5986f0cd"
  integrity sha1-5MopmbCVV3Fri6RHX7efq1mG8M0=
  dependencies:
    fs-extra "^7.0.0"
    lodash ">=3.5 <5"
    tapable "^1.0.0"

webpack-sources@^1.1.0, webpack-sources@^1.3.0:
  version "1.3.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/webpack-sources/-/webpack-sources-1.3.0.tgz#2a28dcb9f1f45fe960d8f1493252b5ee6530fa85"
  integrity sha1-KijcufH0X+lg2PFJMlK17mUw+oU=
  dependencies:
    source-list-map "^2.0.0"
    source-map "~0.6.1"

webpack-sources@^1.4.0:
  version "1.4.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/webpack-sources/-/webpack-sources-1.4.3.tgz#eedd8ec0b928fbf1cbfe994e22d2d890f330a933"
  integrity sha1-7t2OwLko+/HL/plOItLYkPMwqTM=
  dependencies:
    source-list-map "^2.0.0"
    source-map "~0.6.1"

webpack@^4.17.1:
  version "4.37.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/webpack/-/webpack-4.37.0.tgz#3508ef10f7996612c2be6026076d89760f776f54"
  integrity sha1-NQjvEPeZZhLCvmAmB22Jdg93b1Q=
  dependencies:
    "@webassemblyjs/ast" "1.8.5"
    "@webassemblyjs/helper-module-context" "1.8.5"
    "@webassemblyjs/wasm-edit" "1.8.5"
    "@webassemblyjs/wasm-parser" "1.8.5"
    acorn "^6.2.0"
    ajv "^6.1.0"
    ajv-keywords "^3.1.0"
    chrome-trace-event "^1.0.0"
    enhanced-resolve "^4.1.0"
    eslint-scope "^4.0.0"
    json-parse-better-errors "^1.0.2"
    loader-runner "^2.3.0"
    loader-utils "^1.1.0"
    memory-fs "~0.4.1"
    micromatch "^3.1.8"
    mkdirp "~0.5.0"
    neo-async "^2.5.0"
    node-libs-browser "^2.0.0"
    schema-utils "^1.0.0"
    tapable "^1.1.0"
    terser-webpack-plugin "^1.1.0"
    watchpack "^1.5.0"
    webpack-sources "^1.3.0"

websocket-driver@>=0.5.1:
  version "0.7.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/websocket-driver/-/websocket-driver-0.7.3.tgz#a2d4e0d4f4f116f1e6297eba58b05d430100e9f9"
  integrity sha1-otTg1PTxFvHmKX66WLBdQwEA6fk=
  dependencies:
    http-parser-js ">=0.4.0 <0.4.11"
    safe-buffer ">=5.1.0"
    websocket-extensions ">=0.1.1"

websocket-extensions@>=0.1.1:
  version "0.1.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/websocket-extensions/-/websocket-extensions-0.1.3.tgz#5d2ff22977003ec687a4b87073dfbbac146ccf29"
  integrity sha1-XS/yKXcAPsaHpLhwc9+7rBRszyk=

weixin-js-sdk@^1.4.0-test:
  version "1.4.0-test"
  resolved "https://nexus.lanmaoly.com/repository/npm/weixin-js-sdk/-/weixin-js-sdk-1.4.0-test.tgz#612bbafcc97ad84f5afe6dc84701534cc59fc3ca"
  integrity sha1-YSu6/Ml62E9a/m3IRwFTTMWfw8o=

whatwg-encoding@^1.0.1, whatwg-encoding@^1.0.3:
  version "1.0.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/whatwg-encoding/-/whatwg-encoding-1.0.5.tgz#5abacf777c32166a51d085d6b4f3e7d27113ddb0"
  integrity sha1-WrrPd3wyFmpR0IXWtPPn0nET3bA=
  dependencies:
    iconv-lite "0.4.24"

whatwg-fetch@>=0.10.0:
  version "3.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/whatwg-fetch/-/whatwg-fetch-3.0.0.tgz#fc804e458cc460009b1a2b966bc8817d2578aefb"
  integrity sha1-/IBORYzEYACbGiuWa8iBfSV4rvs=

whatwg-mimetype@^2.1.0, whatwg-mimetype@^2.2.0:
  version "2.3.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/whatwg-mimetype/-/whatwg-mimetype-2.3.0.tgz#3d4b1e0312d2079879f826aff18dbeeca5960fbf"
  integrity sha1-PUseAxLSB5h5+Cav8Y2+7KWWD78=

whatwg-url@^6.4.1:
  version "6.5.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/whatwg-url/-/whatwg-url-6.5.0.tgz#f2df02bff176fd65070df74ad5ccbb5a199965a8"
  integrity sha1-8t8Cv/F2/WUHDfdK1cy7WhmZZag=
  dependencies:
    lodash.sortby "^4.7.0"
    tr46 "^1.0.1"
    webidl-conversions "^4.0.2"

whatwg-url@^7.0.0:
  version "7.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/whatwg-url/-/whatwg-url-7.0.0.tgz#fde926fa54a599f3adf82dff25a9f7be02dc6edd"
  integrity sha1-/ekm+lSlmfOt+C3/Jan3vgLcbt0=
  dependencies:
    lodash.sortby "^4.7.0"
    tr46 "^1.0.1"
    webidl-conversions "^4.0.2"

which-module@^2.0.0:
  version "2.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/which-module/-/which-module-2.0.0.tgz#d9ef07dce77b9902b8a3a8fa4b31c3e3f7e6e87a"
  integrity sha1-2e8H3Od7mQK4o6j6SzHD4/fm6Ho=

which@1, which@^1.2.12, which@^1.2.14, which@^1.2.9, which@^1.3.0, which@^1.3.1:
  version "1.3.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/which/-/which-1.3.1.tgz#a45043d54f5805316da8d62f9f50918d3da70b0a"
  integrity sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=
  dependencies:
    isexe "^2.0.0"

wide-align@^1.1.0:
  version "1.1.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/wide-align/-/wide-align-1.1.3.tgz#ae074e6bdc0c14a431e804e624549c633b000457"
  integrity sha1-rgdOa9wMFKQx6ATmJFScYzsABFc=
  dependencies:
    string-width "^1.0.2 || 2"

wordwrap@~0.0.2:
  version "0.0.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/wordwrap/-/wordwrap-0.0.3.tgz#a3d5da6cd5c0bc0008d37234bbaf1bed63059107"
  integrity sha1-o9XabNXAvAAI03I0u68b7WMFkQc=

wordwrap@~1.0.0:
  version "1.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/wordwrap/-/wordwrap-1.0.0.tgz#27584810891456a4171c8d0226441ade90cbcaeb"
  integrity sha1-J1hIEIkUVqQXHI0CJkQa3pDLyus=

worker-farm@^1.7.0:
  version "1.7.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/worker-farm/-/worker-farm-1.7.0.tgz#26a94c5391bbca926152002f69b84a4bf772e5a8"
  integrity sha1-JqlMU5G7ypJhUgAvabhKS/dy5ag=
  dependencies:
    errno "~0.1.7"

wrap-ansi@^2.0.0:
  version "2.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/wrap-ansi/-/wrap-ansi-2.1.0.tgz#d8fc3d284dd05794fe84973caecdd1cf824fdd85"
  integrity sha1-2Pw9KE3QV5T+hJc8rs3Rz4JP3YU=
  dependencies:
    string-width "^1.0.1"
    strip-ansi "^3.0.1"

wrap-ansi@^5.1.0:
  version "5.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/wrap-ansi/-/wrap-ansi-5.1.0.tgz#1fd1f67235d5b6d0fee781056001bfb694c03b09"
  integrity sha1-H9H2cjXVttD+54EFYAG/tpTAOwk=
  dependencies:
    ansi-styles "^3.2.0"
    string-width "^3.0.0"
    strip-ansi "^5.0.0"

wrappy@1:
  version "1.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/wrappy/-/wrappy-1.0.2.tgz#b5243d8f3ec1aa35f1364605bc0d1036e30ab69f"
  integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=

write-file-atomic@^2.1.0:
  version "2.4.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/write-file-atomic/-/write-file-atomic-2.4.3.tgz#1fd2e9ae1df3e75b8d8c367443c692d4ca81f481"
  integrity sha1-H9Lprh3z51uNjDZ0Q8aS1MqB9IE=
  dependencies:
    graceful-fs "^4.1.11"
    imurmurhash "^0.1.4"
    signal-exit "^3.0.2"

write@1.0.3:
  version "1.0.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/write/-/write-1.0.3.tgz#0800e14523b923a387e415123c865616aae0f5c3"
  integrity sha1-CADhRSO5I6OH5BUSPIZWFqrg9cM=
  dependencies:
    mkdirp "^0.5.1"

ws@^5.2.0:
  version "5.2.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/ws/-/ws-5.2.2.tgz#dffef14866b8e8dc9133582514d1befaf96e980f"
  integrity sha1-3/7xSGa46NyRM1glFNG++vlumA8=
  dependencies:
    async-limiter "~1.0.0"

ws@^6.0.0:
  version "6.2.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/ws/-/ws-6.2.1.tgz#442fdf0a47ed64f59b6a5d8ff130f4748ed524fb"
  integrity sha1-RC/fCkftZPWbal2P8TD0dI7VJPs=
  dependencies:
    async-limiter "~1.0.0"

xml-name-validator@^3.0.0:
  version "3.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/xml-name-validator/-/xml-name-validator-3.0.0.tgz#6ae73e06de4d8c6e47f9fb181f78d648ad457c6a"
  integrity sha1-auc+Bt5NjG5H+fsYH3jWSK1FfGo=

xtend@^4.0.0, xtend@~4.0.1:
  version "4.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/xtend/-/xtend-4.0.2.tgz#bb72779f5fa465186b1f438f674fa347fdb5db54"
  integrity sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q=

y18n@^3.2.1:
  version "3.2.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/y18n/-/y18n-3.2.1.tgz#6d15fba884c08679c0d77e88e7759e811e07fa41"
  integrity sha1-bRX7qITAhnnA136I53WegR4H+kE=

"y18n@^3.2.1 || ^4.0.0", y18n@^4.0.0:
  version "4.0.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/y18n/-/y18n-4.0.0.tgz#95ef94f85ecc81d007c264e190a120f0a3c8566b"
  integrity sha1-le+U+F7MgdAHwmThkKEg8KPIVms=

yallist@^2.1.2:
  version "2.1.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/yallist/-/yallist-2.1.2.tgz#1c11f9218f076089a47dd512f93c6699a6a81d52"
  integrity sha1-HBH5IY8HYImkfdUS+TxmmaaoHVI=

yallist@^3.0.0, yallist@^3.0.2, yallist@^3.0.3:
  version "3.0.3"
  resolved "https://nexus.lanmaoly.com/repository/npm/yallist/-/yallist-3.0.3.tgz#b4b049e314be545e3ce802236d6cd22cd91c3de9"
  integrity sha1-tLBJ4xS+VF486AIjbWzSLNkcPek=

yargs-parser@^11.1.1:
  version "11.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/yargs-parser/-/yargs-parser-11.1.1.tgz#879a0865973bca9f6bab5cbdf3b1c67ec7d3bcf4"
  integrity sha1-h5oIZZc7yp9rq1y987HGfsfTvPQ=
  dependencies:
    camelcase "^5.0.0"
    decamelize "^1.2.0"

yargs-parser@^13.1.0:
  version "13.1.1"
  resolved "https://nexus.lanmaoly.com/repository/npm/yargs-parser/-/yargs-parser-13.1.1.tgz#d26058532aa06d365fe091f6a1fc06b2f7e5eca0"
  integrity sha1-0mBYUyqgbTZf4JH2ofwGsvfl7KA=
  dependencies:
    camelcase "^5.0.0"
    decamelize "^1.2.0"

yargs-parser@^13.1.2:
  version "13.1.2"
  resolved "https://r.cnpmjs.org/yargs-parser/download/yargs-parser-13.1.2.tgz#130f09702ebaeef2650d54ce6e3e5706f7a4fb38"
  integrity sha1-Ew8JcC667vJlDVTObj5XBvek+zg=
  dependencies:
    camelcase "^5.0.0"
    decamelize "^1.2.0"

yargs-parser@^9.0.2:
  version "9.0.2"
  resolved "https://nexus.lanmaoly.com/repository/npm/yargs-parser/-/yargs-parser-9.0.2.tgz#9ccf6a43460fe4ed40a9bb68f48d43b8a68cc077"
  integrity sha1-nM9qQ0YP5O1Aqbto9I1DuKaMwHc=
  dependencies:
    camelcase "^4.1.0"

yargs@12.0.5:
  version "12.0.5"
  resolved "https://nexus.lanmaoly.com/repository/npm/yargs/-/yargs-12.0.5.tgz#05f5997b609647b64f66b81e3b4b10a368e7ad13"
  integrity sha1-BfWZe2CWR7ZPZrgeO0sQo2jnrRM=
  dependencies:
    cliui "^4.0.0"
    decamelize "^1.2.0"
    find-up "^3.0.0"
    get-caller-file "^1.0.1"
    os-locale "^3.0.0"
    require-directory "^2.1.1"
    require-main-filename "^1.0.1"
    set-blocking "^2.0.0"
    string-width "^2.0.0"
    which-module "^2.0.0"
    y18n "^3.2.1 || ^4.0.0"
    yargs-parser "^11.1.1"

yargs@13.2.4:
  version "13.2.4"
  resolved "https://nexus.lanmaoly.com/repository/npm/yargs/-/yargs-13.2.4.tgz#0b562b794016eb9651b98bd37acf364aa5d6dc83"
  integrity sha1-C1YreUAW65ZRuYvTes82SqXW3IM=
  dependencies:
    cliui "^5.0.0"
    find-up "^3.0.0"
    get-caller-file "^2.0.1"
    os-locale "^3.1.0"
    require-directory "^2.1.1"
    require-main-filename "^2.0.0"
    set-blocking "^2.0.0"
    string-width "^3.0.0"
    which-module "^2.0.0"
    y18n "^4.0.0"
    yargs-parser "^13.1.0"

yargs@^11.0.0:
  version "11.1.0"
  resolved "https://nexus.lanmaoly.com/repository/npm/yargs/-/yargs-11.1.0.tgz#90b869934ed6e871115ea2ff58b03f4724ed2d77"
  integrity sha1-kLhpk07W6HERXqL/WLA/RyTtLXc=
  dependencies:
    cliui "^4.0.0"
    decamelize "^1.1.1"
    find-up "^2.1.0"
    get-caller-file "^1.0.1"
    os-locale "^2.0.0"
    require-directory "^2.1.1"
    require-main-filename "^1.0.1"
    set-blocking "^2.0.0"
    string-width "^2.0.0"
    which-module "^2.0.0"
    y18n "^3.2.1"
    yargs-parser "^9.0.2"

yargs@^13.3.2:
  version "13.3.2"
  resolved "https://r.cnpmjs.org/yargs/download/yargs-13.3.2.tgz#ad7ffefec1aa59565ac915f82dccb38a9c31a2dd"
  integrity sha1-rX/+/sGqWVZayRX4Lcyzipwxot0=
  dependencies:
    cliui "^5.0.0"
    find-up "^3.0.0"
    get-caller-file "^2.0.1"
    require-directory "^2.1.1"
    require-main-filename "^2.0.0"
    set-blocking "^2.0.0"
    string-width "^3.0.0"
    which-module "^2.0.0"
    y18n "^4.0.0"
    yargs-parser "^13.1.2"

zscroller@~0.4.0:
  version "0.4.8"
  resolved "https://nexus.lanmaoly.com/repository/npm/zscroller/-/zscroller-0.4.8.tgz#69eed68690808eedf81f9714014356b36cdd20f4"
  integrity sha1-ae7WhpCAju34H5cUAUNWs2zdIPQ=
  dependencies:
    babel-runtime "6.x"
