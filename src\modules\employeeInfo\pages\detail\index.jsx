import React, { Component } from "react";
import { Tabs, <PERSON>, Button ,Toast} from "antd-mobile";
import "./index.scss";
import { connect } from "react-redux";
import Header from "components/Header";
import CompList from "../../components/List";
import Footer from "components/Footer";


class Detail extends Component {
  constructor(props) {
    super(props);
    this.state = {
    };
  }
  
  componentDidMount() {
    //this.props.history.push({ pathname:'/employee-info/index',search:this.props.history.search});
  }
  handleSubmit(){
    const { groupRequired } = this.props;
    for(let i in groupRequired){
      let item = groupRequired[i]
      if(!item.isRequired){
        Toast.info(`请填写${item.name}必填项`, 1, () => {
          return
        });
        return
      }
    }
    Toast.info('提交成功！');
  }
  render() {
    return (
      <div className="employee-info">
        <CompList history={this.props.history}/>
        <div style={{height:'80px'}}></div>
        <Footer fun={this.handleSubmit.bind(this)} text={'提交个人信息'} />
      </div>
    );
  }
}

const mapStateToProps = (state) => ({
  groupRequired: state.employeeInfo.groupRequired,
});

const mapDispatchToProps = {

};

export default connect(mapStateToProps, mapDispatchToProps)(Detail);

