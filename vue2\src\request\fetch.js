/* eslint-disable no-unused-vars */
// 初始化 old-fetch
import { createFetch } from 'old-fetch';
import { Toast } from 'antd-mobile';

const options = {
  isMock: false,
  needStartLimit: true,
  handleResponseData(res) {
    if (res.success || options.isMock) {
      Toast.hide();
    }
  },
  handleResponseError: (err, config) => {
    Toast.info(err.message || '接口错误');
  },
  customerHeaders: {
    //AgentClientDomain: window.location.host.includes('localhost') ? 'olading.com' : window.location.host,
    AgentClientDomain: location.hostname,
    //Source: "H5",
  },
};
const fetch = createFetch({baseURL: window.env.domain + "/api/"}, options);
export default fetch;
