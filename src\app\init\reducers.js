/* eslint-disable no-param-reassign */
/* eslint-disable no-unused-vars */
import { combineReducers } from 'redux';
import entryRegister from 'modules/entryRegister/redux/reducer';
import employeeInfo from 'modules/employeeInfo/redux/reducer';
import document from 'modules/document/redux/reducer';
import personInfoAgreement from 'modules/personInfoAgreement/redux/reducer';
import app from '../reducer';

export const makeRootReducer = asyncReducers => combineReducers({
  entryRegister,
  employeeInfo,
  document,
  personInfoAgreement,
  app,
});

export const injectReducer = (store, { key, reducer }) => {
  if (!store.asyncReducers[key]) {
    store.asyncReducers[key] = reducer;
    store.replaceReducer(makeRootReducer(store.asyncReducers));
  }
};
