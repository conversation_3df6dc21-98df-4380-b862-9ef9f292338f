import React, { Component } from "react";
import {  <PERSON>, <PERSON><PERSON> ,<PERSON><PERSON>,Picker,ActivityIndicator } from "antd-mobile";
// import {withRouter} from "react-router-dom";
//import "./index.scss";
import { connect } from "react-redux";
//import action from "../../entryRegister/redux/action";
import action from "../redux/action";

window.onload = function () {
	var isPageHide = false;
	// 页面加载（是没有刷新页面的，跟页面刷新有着本质的不同）
	window.addEventListener('pageshow', function () {
		if (isPageHide) {
			window.location.reload();
		}
	});
	window.addEventListener('pagehide', function () { 
		isPageHide = true;
	});
};
class CompList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      entryInfoList: [],//登记详情数据
      empId:null,
      compId:null,
      editYN:true,//是否可以编辑
      employeeDetailLableDto:this.props.employeeDetailLableDto,//个人信息or岗位信息
      empTaxSubject:[],//员工所在公司
      empRecordId:sessionStorage.getItem("def_empRecordId"),//value
      taxSubjectName:sessionStorage.getItem("def_taxSubjectName"),//label
      animating:true,
    };
  }
  //获取入职登记列表信息
  handleRegistDetail(){
    const { acgetEmpDetail,acGetEmpTaxSubject, token,employeeDetailLableDto,acgetcompId} = this.props;
    const { empId,compId,empRecordId } = this.state;
    acGetEmpTaxSubject().then(res=>{
      if(Object.prototype.toString.call(res.data) == '[object Array]'){
        let arr = [],
            idArr = [],
            nameArr = [],
            boo = false;
        res.data.map(v=>{
          arr.push({
            value:v.empRecordId,
            label:v.taxSubjectName
          })
          idArr.push(v.empRecordId)
          nameArr.push(v.taxSubjectName)
        });
        let def_empRecordId = idArr.includes(Number(sessionStorage.getItem("def_empRecordId"))) ? Number(sessionStorage.getItem("def_empRecordId")) : res.data[0].empRecordId
        
        this.setState({
          empTaxSubject:arr
        },()=>{
          this.handleChooseEmp([def_empRecordId])
        });
        acgetEmpDetail({empRecordId:def_empRecordId}).then(res => {
          if (res.success) {
            this.setState({
              //entryInfoList: res.data.employeeDetailLableDto[employeeDetailLableDto].groupModels,
              entryInfoList: this.handleDeailList(res.data.employeeDetailLableDto),
              empId: res.data.empId,
              editYN: res.data.editYN,
              animating:false
            },()=>{
              console.log(this.state.entryInfoList)
              this.handleCheckRequired();
            })
          }else{
            Toast.info(res.message);
          }
        })
      }else{
        Toast.info("员工所在公司数据查询失败");
      }
    })
  }
  handleDeailList(items){
    let arr = [];
    for(let i = 0;i<items.length;i++){
      arr = [...arr,...this.handleListData(items[i])]
    }
    return arr
    // [...this.handleListData(res.data.employeeDetailLableDto[0]),...this.handleListData(res.data.employeeDetailLableDto[1])]
  }
  
  //检查必填字段是否填写
  handleCheckRequired(){
    const { entryInfoList } = this.state;
    const { acSetGroupRequired} = this.props;
    let state = []
    entryInfoList.forEach(item=>{
      let groupCode = item.groupCode;
      let groupName = item.groupName;
      let baseInfo = item.models[0].fieldModels;
      let baseInfoResult = baseInfo.every((v)=>{
        if(v["isRequired"]&&!v["fieldValue"]&&item["def_lableCode"]!=="POST_INFO"){//岗位信息 暂不校验
          return false
        }else{
          return true
        }
      })
      state.push({
        id:groupCode,
        name:groupName,
        isRequired:baseInfoResult,
      })
    })
    acSetGroupRequired(state)
  }
  componentDidMount() {
    this.handleRegistDetail();
    const { acSetSource} = this.props;
    acSetSource('EMPLOYEE');
  }
  //拼接lableCode
  handleListData(data){
    return data.groupModels.map((v,k)=>{
      v["def_lableCode"] = data.lableCode
      return v
    })
  }

  // 由目录页跳转到对应详情页
  handleClick (item) {
    console.log(item)
    const groupName = item.groupName,
          groupCode = item.groupCode,
          addMultipleYn = item.addMultipleYn,//是否支持多条
          def_lableCode = item.def_lableCode,//lableCode
          empId = this.state.empId,
          compId = this.state.compId,
          editYN = this.state.editYN,
          id = item.models[0].id;
    let getCountry,getCity,getProvince;
    const { empRecordId } = this.state;
    const {acGetCountries,acGetCity,acGetProvince} = this.props;
    if(['BASIC_INFO','POSITION_INFO'].includes(groupCode)){//进入基本信息,任职信息前调取国籍/城市接口
      getCountry = acGetCountries()
      getCity = acGetCity()
      getProvince = acGetProvince()
    }
    Promise.all([getCountry, getCity,getProvince]).then((res)=>{
      console.log(res) 
      //item = item.models[0].fieldModels
      let list = item.models
      this.props.history.push({ pathname:'/employee-info/form',state:{list,groupName,groupCode,empId,compId,id,addMultipleYn,def_lableCode,editYN,empRecordId:empRecordId}});
      // this.props.history.replace({ pathname:'/employee-info/form',state:{list,groupName,groupCode,empId,compId,id,addMultipleYn,def_lableCode,editYN}});
    }).catch((error) => {
      console.log(error)
    })
  };
  handleChooseEmp(val){
    const { empTaxSubject,empRecordId,taxSubjectName } = this.state;
    let def_taxSubjectName = empTaxSubject.filter(v=>v.value == val.join(""))[0].label
    this.setState({
      empRecordId:val.join(""),
      taxSubjectName:def_taxSubjectName
    });
    sessionStorage.setItem("def_empRecordId",val.join(""));
    sessionStorage.setItem("def_taxSubjectName",def_taxSubjectName);
  }
  handleChange(val){
    this.setState({animating:true})
    this.handleChooseEmp(val);
    this.handleRegistDetail();
  }

  render() {
    const { entryInfoList,empTaxSubject,taxSubjectName,animating } = this.state;
    const Item = List.Item;
    const empStyle={
      display: 'flex',
      justifyContent: 'space-between',
      margin:"10px 15px"
    };
    const empNameStyle={
      fontSize: '12px',
      color: 'gray',
    }
    const activityIndicatorStyle={
      height: '50vh',
      display: 'flex',
      justifyContent: 'center',
    }
    return (
        <section className="section">
          <section style={empStyle}>
            <section>
              <span style={empNameStyle}>当前公司：{taxSubjectName}</span>
            </section>
            <section>
              <Picker 
                data={empTaxSubject} 
                cols={1} 
                className="forss"
                onChange={(value)=>{this.handleChange(value)}}
              >
                <span style={{color:"#2C7CFF",fontSize:"15px"}}>切换</span>
              </Picker>
            </section>
            
          </section>
          {
            animating ?
            <section style={activityIndicatorStyle}>
              <ActivityIndicator
                text="Loading..."
                size="large"
              />
            </section>
             :
            <List>
            {
              entryInfoList.map((item,index) =>(
                <Item arrow="horizontal" onClick={(e)=>this.handleClick(item,e)} key={index}>{item.groupName}</Item>
              ))
            }
          </List>
          }
          <div style={{height:'80px'}}></div>
        </section>
    );
  }
}

const mapStateToProps = (state) => ({
  token: state.entryRegister.token,
});

const mapDispatchToProps = {
  acgetEmpDetail:action.acgetEmpDetail,//员工详情查询
  acGetCountries:action.acGetCountries,//国家列表
  acGetCity:action.acGetCity,//城市列表
  acGetProvince:action.acGetProvince,//城市联动列表
  getUrlParams:action.getUrlParams,//url参数
  acSetSource:action.acSetSource,//来源
  acgetcompId:action.acgetcompId,//企业ID
  acSetGroupRequired:action.acSetGroupRequired,//各个分组是否编辑保存必填字段
  acGetEmpTaxSubject:action.acGetEmpTaxSubject,//获取员工所在公司
};
export default connect(mapStateToProps, mapDispatchToProps)(CompList);
// export default connect(mapStateToProps, mapDispatchToProps)(withRouter(CompList));

