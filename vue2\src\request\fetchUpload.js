import axios from "axios"

export default function fetchUpload(options) {
	return new Promise((resolve, reject) => {
		const instance = axios.create({
			baseURL: window.env.domain + "/api/",
			timeout: 60000, // 请求超时时间
			headers: {// 这里可设置所有的请求头
				'Content-Type': 'application/x-www-form-urlencoded', //该项建议设置 如果未 POST请求时 数据未做处理时会出现错误，最理想的解决方法就是 直接设置该项
			},
			withCredentials: true,//是否允许发送Cookie 如果为true 则服务器的 Access-control-Allow-Credentials 必须为 true 来源为 XMLHttpRequest的withCredentials配置项

			// 以下两项为拦截器配置
			transformRequest: [function (data) {//准备发送请求触发的事件 类型：Array || Function 可以是一个函数或数组 data 为发送的数据 get 为undefined
				// console.log('准备发送请求：',data)
				// 此处可对发送的数据进行处理
				return data; //最终数据
			}],
			// transformResponse: [function (data) {//接收到数据首先处理的函数 ，data 为服务器返回的数据（// 1.响应结构 里的  rs.data）
			//   console.log('接收到数据：',data)
			//   return data;//最终数据
			// },function (data) {//当为数组时 窜行操作  data 为上一步中的最终数据
			//   console.log('before',data)
			//   return data;
			// }],
		})
		//请求拦截器
		instance.interceptors.request.use(config => {
			let token = sessionStorage.getItem("token");
			if (token) {
				config.headers.common['Authorization'] = token;
        config.headers.common['AgentClientDomain'] = location.hostname;
        config.headers.common['Source'] = 'H5';
				//config.headers.common['Channel'] = "WeChatSubscription";
			}
			else{
				config.headers.common['AgentClientDomain'] = location.hostname;
			}
			return config;
		}, err => {
			return Promise.reject(err);
		});
		//响应拦截器
		instance.interceptors.response.use(
			response => {
				// console.log("response拦截器开始工作")
				return response
			},
			error => {
				return Promise.reject(error.response)
			})

		instance(options).then(response => {
			//请求成功后操作
			resolve(response.data); //返回请求成功的数据
		}).catch(error => {
			// console.log(error)
			// console.log('请求异常信息：' + error);
			reject(error);
		})
	})
}
