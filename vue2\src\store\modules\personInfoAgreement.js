const state = {
  agreementContent: '',
  hasAgreed: false,
  loading: false
};

const mutations = {
  SET_AGREEMENT_CONTENT(state, content) {
    state.agreementContent = content;
  },
  SET_HAS_AGREED(state, status) {
    state.hasAgreed = status;
  },
  SET_LOADING(state, status) {
    state.loading = status;
  }
};

const actions = {
  setAgreementContent({ commit }, content) {
    commit('SET_AGREEMENT_CONTENT', content);
  },
  setHasAgreed({ commit }, status) {
    commit('SET_HAS_AGREED', status);
  },
  setLoading({ commit }, status) {
    commit('SET_LOADING', status);
  }
};

const getters = {
  agreementContent: state => state.agreementContent,
  hasAgreed: state => state.hasAgreed,
  loading: state => state.loading
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
};
