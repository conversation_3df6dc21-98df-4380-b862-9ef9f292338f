//手机号格式验证
export const validPhoneReg = (value) => {
    if(!value){
        return {boo:true,data:'不校验空数据'}
    }
    let reg = /^1\d{10}$/;
    if (reg.test(value)) {
        return {boo:true,data:'验证通过'}
    } else {
        //callback(new Error('请输入正确的手机号'));
        return {boo:false,data:'请输入正确的手机号'}
    }
};
//邮箱格式验证
export const valideEmail = (value) => {
    if(!value){
        return {boo:true,data:'不校验空数据'}
    }
    let reg = /^[A-Za-z\d]+([-_.][A-Za-z\d]+)*@([A-Za-z\d]+[-.])+[A-Za-z\d]{2,4}$/;
    if (reg.test(value)) {
        return {boo:true,data:'验证通过'}
    } else {
        return {boo:false,data:'请输入正确的联系邮箱'}
    }
};


//证件号码格式校验
export const validIdNo = (value,type) =>{
if (value) {
    switch (type) {
        case "PRC_ID": //居民身份证
            var regIdNo = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
            if (!regIdNo.test(value)) {
            return {boo:false,data:'居民身份证号录入不正确'}
            } else {
            return {boo:true,data:'验证通过'}
            }
            break;
        case "CHINA_PASSPORT": //中国护照
            if (value.length != 9) {
            return {boo:false,data:'中国护照的证件号码必须9位，且只含数字和字母'}
            } else {
            return {boo:true,data:'验证通过'}
            }
            break;
        case "COMPATRIOTS_CARD": //港澳居民来往内地通行证
            if (!(value.length == 9 || value.length == 11)) {
            return {boo:false,data:'证件号码长度不对，且必须是数字和字母组合'}
            } else {
            return {boo:true,data:'验证通过'}
            }
            break;
        case "FORMOSA_CARD": //台湾居民来往大陆通行证
            if (value.length != 8) {
            return {boo:false,data:'台湾居民来往大陆通行证的证件号码必须8位数字'}
            } else {
            return {boo:true,data:'验证通过'}
            }
            break;
        case "MACAU_PRC_ID": //港澳居民居住证
            if (value.length != 18) {
            return {boo:false,data:'港澳居民居住证的证件号码必须18位'}
            } else {
            return {boo:true,data:'验证通过'}
            }
            break;
        case "FORMOSA_PRC_ID": //台湾居民居住证
            if (value.length != 18) {
            return {boo:false,data:'台湾居民居住证的证件号码必须18位'}
            } else {
            return {boo:true,data:'验证通过'}
            }
            break;
        case "FOREIGN_PRC_ID": //外国人永久居留身份证
            if (value.length != 15) {
            return {boo:false,data:'外国人永久居留身份证的证件号码必须15位'}
            } else {
            return {boo:true,data:'验证通过'}
            }
            break;
        default:
            // return {boo:false,data:'证件类型不能为空'}
            return {boo:true,data:'验证通过'}
        }
    } else {
        return {boo:false,data:'证件号码不能为空'}
    }
}

//验证密码
export const validCodeReg = (rule, value, callback) => {
    if (value === '') {
        callback(new Error('请输入密码'));
    } else {
    let reg = /(?!^(\d+|[a-zA-Z]+|[~!@#$%^&*?]+|\\s+)$)^[\w~!@#$%^&*?./]{6,20}$/;
        if (reg.test(value)) {
            callback();
        } else {
            callback(new Error('6-20位字母、数字、字符组合'));
        }
    }
};