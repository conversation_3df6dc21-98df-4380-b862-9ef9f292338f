import React, { Component } from "react";
import "./index.scss";
import {
  NavBar,
  Icon,
} from "antd-mobile";
import { connect } from "react-redux";
import dayjs from "dayjs";

class UploadCard extends Component {
  constructor(props) {
    super(props);
    this.state = {
      detail: [
        {
          key: "name",
          label: "姓名",
        },
        {
          key: "sex",
          label: "性别",
        },
        {
          key: "birthday",
          label: "出生日期",
        },
        {
          key: "nation",
          label: "民族",
        },
        {
          key: "type",
          label: "证件类型",
        },
        {
          key: "idCardNo",
          label: "身份证号",
        },
        {
          key: "idCardValidityPeriod",
          label: "有效期至",
        },
        {
          key: "birthAddress",
          label: "户口所在地",
        },
        {
          key: "idCardSigningOrgans",
          label: "签发机关",
        },  
      ],
      info:{}
    };
  }
  componentDidMount() {
    let { cardInfo } = this.props;
    console.log('cardInfo：',cardInfo)    
    cardInfo = {
      ...cardInfo,
      birthday :dayjs(cardInfo.birthday).format("YYYY年MM月DD日"),
      idCardValidityPeriod:dayjs(cardInfo.idCardValidityPeriod.match(/\-(.*)/)[1]).format("YYYY年MM月DD日"),  
      type:'身份证',
     };
    this.setState({
      info:cardInfo
    })
  }


  //back
  handleBack() {
    window.history.back(-1);
  }
  handleClick() {
    this.props.history.go(-2)  
  }

  render() {
    const { detail ,info} = this.state;
    return (
      <div className="upload-detail">
        <NavBar
          key={1}
          mode="light"
          icon={<Icon type="left" />}
          style={{ color: "black" }}
          onLeftClick={this.handleBack.bind(this)}
        >
          身份基本信息
        </NavBar>

        <p className="tips">请确认身份信息，如有误可点击修改</p>
        <div className="container">
          {detail.map((item, index) => {
            return (
              <div key={index} className={["item", index == 3 ? "m-b-10" : null].join(" ")}>
                <span>
                  {item.label} 
                </span>
                <span>{ info[item.key]}</span>
              </div>
            );
          })}
        </div>
        <div className="next">
          <div className="btn" onClick={(e) => this.handleClick(e)}>确定</div>
        </div>
      </div>
    );
  }
}

const mapStateToProps = (state) => ({
  cardInfo: state.entryRegister.cardInfo,

});

export default connect(mapStateToProps)(UploadCard);
