const state = {
  documentList: [],
  currentDocument: null,
  loading: false
};

const mutations = {
  SET_DOCUMENT_LIST(state, list) {
    state.documentList = list;
  },
  SET_CURRENT_DOCUMENT(state, document) {
    state.currentDocument = document;
  },
  SET_LOADING(state, status) {
    state.loading = status;
  }
};

const actions = {
  setDocumentList({ commit }, list) {
    commit('SET_DOCUMENT_LIST', list);
  },
  setCurrentDocument({ commit }, document) {
    commit('SET_CURRENT_DOCUMENT', document);
  },
  setLoading({ commit }, status) {
    commit('SET_LOADING', status);
  }
};

const getters = {
  documentList: state => state.documentList,
  currentDocument: state => state.currentDocument,
  loading: state => state.loading
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
};
