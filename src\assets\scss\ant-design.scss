.am-checkbox.am-checkbox-checked .am-checkbox-inner {
  background: $main-color !important;
  border-color: $main-color !important;
}

.am-modal-button-group-h .am-modal-button {
  color: $main-color !important;
}

.am-modal-button-group-h .am-modal-button:first-child {
  color: #333 !important;
}

.am-list-body div:not(:last-child) .am-list-line {
  border: none !important;
}

.am-list-body {
  border: none !important;
}

.file-list_item {
  border-bottom: 1px solid #ddd !important;
}

//list组件
.am-list-body::before {
  background-color: white !important;
  content: "" !important;
  border: none !important;
}

.am-list-body::after {
  background-color: white !important;
  content: "" !important;
  border: none !important;
}

.am-list-line::after {
  background-color: white !important;
  content: "" !important;
  border: none !important;
}

.am-list-item .am-list-line .am-list-content {
  color: #333333 !important;
  font-size: 14px !important;
}

.form-item .am-list-item .am-list-line .am-list-content {
  padding-left: 8px;
}

.am-checkbox-inner {
  border-radius: 2px !important;
  width: 16px !important;
  height: 16px !important;
}

.am-checkbox.am-checkbox-checked .am-checkbox-inner {
  background: $main-color !important;
  border-color: $main-color !important;
}

// .am-list-item .am-input-label.am-input-label-5{
//   min-width: 120px !important;
// }
.am-list-item .am-input-label {
  color: #333333 !important;
  font-size: 14px !important;
  padding-left: 8px !important;
}

.am-list-item .am-input-control input {
  font-size: 14px !important;
  color: #a5a5a5 !important;

}

.am-checkbox-inner:after {
  top: 0px !important;
  right: 4px !important;
}

.am-list-item .am-list-line .am-list-extra {
  font-size: 14px !important;
  color: #a5a5a5 !important;
}

.am-modal-button-group-v .am-modal-button {
  color: $main-color !important;
}

.am-button-primary {
  background-color: #4185f8;
}