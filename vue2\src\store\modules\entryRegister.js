const state = {
  formData: {},
  currentStep: 0,
  isSubmitting: false
};

const mutations = {
  SET_FORM_DATA(state, payload) {
    state.formData = { ...state.formData, ...payload };
  },
  SET_CURRENT_STEP(state, step) {
    state.currentStep = step;
  },
  SET_SUBMITTING(state, status) {
    state.isSubmitting = status;
  },
  RESET_FORM(state) {
    state.formData = {};
    state.currentStep = 0;
    state.isSubmitting = false;
  }
};

const actions = {
  setFormData({ commit }, payload) {
    commit('SET_FORM_DATA', payload);
  },
  setCurrentStep({ commit }, step) {
    commit('SET_CURRENT_STEP', step);
  },
  setSubmitting({ commit }, status) {
    commit('SET_SUBMITTING', status);
  },
  resetForm({ commit }) {
    commit('RESET_FORM');
  }
};

const getters = {
  formData: state => state.formData,
  currentStep: state => state.currentStep,
  isSubmitting: state => state.isSubmitting
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
};
