import Index from 'modules/entryRegister';
import Document from 'modules/document';
import PersonInfoAgreement from 'modules/personInfoAgreement';
import employeeInfoRoutes from 'modules/employeeInfo/routerList';
import entryRegisterRoutes from 'modules/entryRegister/routerList';

const routerList = [
  ...employeeInfoRoutes,
  ...entryRegisterRoutes,
  {
    path: '/document',
    exact: true,
    component: Document,
  },
  {
    path: '/personInfoAgreement',
    exact: true,
    component: PersonInfoAgreement,
  },
  {
    path: '/',
    exact: true,
    component: Index,
  },

];
// routerList.forEach(v => {
//   v.path = '/gd/hrsaas/h5-hrsaas' + v.path
// })
// console.log(routerList)
export default routerList;
