import React, { Component } from "react";
import { connect } from "react-redux";
import { Drawer, List, NavBar, Icon } from 'antd-mobile';
import DrawerForm from "./DrawerForm";

//抽屉组件--支持内部插槽
class CompDrawer extends Component {
  constructor(props) {
    super(props);
    this.state = {
      open: false,
      submit:{},//调用保存方法
    }
  }
  componentDidMount() {
    // const DrawerState = this.props.DrawerState;
    // this.setState({ open: DrawerState });
  }
  onOpenChange = (...args) => {
    console.log(args);
    this.setState({ open: !this.state.open });
  }

  //保存
  submit(){
    console.log(this.state.submit)
    const { submit } = this.state
    for(let i in submit){
      submit[i].submit()
    }
  }
  //绑定组件的状态和方法
  handleForm(ref,index){
    let newSubmit = Object.assign(this.state.submit,{[index]: ref});
    this.setState({
      submit:newSubmit,
    });
  }

  handlegetDrawerFormMsg(val){
    console.log(val)
  }

  render() {
    const groupModels = this.props.groupModels;
    const DrawerState = this.props.DrawerState;
    const DrawerAddForm = (
      <div>
        <DrawerForm data={this.props.data} id={groupModels.id} list={groupModels.fieldModels} 
          getDrawerFormMsg={this.handlegetDrawerFormMsg}
          onRef={(ref)=>{this.handleForm(ref,0)}}//获取子组件的方法-用于调用
        />
      </div>
    );
    return (<div>
      <Drawer
        className="my-drawer"
        style={{ minHeight: document.documentElement.clientHeight -80 }}
        enableDragHandle
        // contentStyle={{ color: '#A6A6A6', textAlign: 'center', paddingTop: 42 }}
        sidebar={DrawerAddForm}
        open={DrawerState}
        onOpenChange={this.onOpenChange}
        //docked={DrawerState}
      >
        {this.props.children}
      </Drawer>
    </div>);
  }
}

const mapStateToProps = (state) => ({

});

const mapDispatchToProps = {

};

export default connect(mapStateToProps, mapDispatchToProps)(CompDrawer);

