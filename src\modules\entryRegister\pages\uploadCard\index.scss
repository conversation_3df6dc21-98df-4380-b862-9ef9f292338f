@import "../../../../assets/scss/helpers";

@mixin x-c {
  display: flex;
  justify-content: center;
  align-items: center;
}

.upload-card {
  .container {
    padding: 30px 27px;
    box-sizing: border-box;
    .card_front {
      margin-bottom: 20px;
      .am-flexbox-item {
        height: 208px;
        .am-image-picker-item {      
          background: url('../../../../assets/images/card/card_front.png');
          background-size: 100% 100%;
        }
       
      }
    }
    .card_back {
      .am-flexbox-item {
        height: 208px;
        .am-image-picker-item {  
          background: url('../../../../assets/images/card/card_back.png');
          background-size: 100% 100%;
        }       
      }
      }

     .am-image-picker-item-content {
       background-size: 100% 100% !important;
     }

      .am-image-picker-upload-btn {
        border: none;
        &::before {
          display: none;
        }
        &::after {
          display: none;

        }
      }
    .tips {
      font-size: 14px;
      color: #6A6F7F;
      margin: 16px auto 12px;
    }

    .item-box {
      height: 84px;
      background: #FAFAFA;
      border-radius: 8px;
      display: flex;
      justify-content: space-between;
      padding: 10px;
      margin-bottom: 64px;
      box-sizing: border-box;

      .item {
        img {
          width: 68px;
          height: 43px;
          margin-bottom: 5px;
        }

        .icon {
          display: inline-block;
          width: 12px;
          height: 12px;
          margin-right: 5px;
        }

        .success {
          background: url('../../../../assets/images/card/success.png');
          background-size: 100% 100%;
        }

        .error {
          background: url('../../../../assets/images/card/error.png');
          background-size: 100% 100%;
        }

        p {
          font-size: 12px;
          color: #6A6F7F;
          @include x-c;

        }

      }

    }

  }

  .next {
    position: fixed;
    bottom: 0;
    width: 100%;
    height: 64px;
    background: #fff;
    box-shadow: 0 -1px 4px 0 rgba(224, 224, 224, 0.60);
    padding: 0 25px;
    box-sizing: border-box;
    @include x-c;

    .btn {
      width: 100%;
      height: 41px;
      background: #4185F7;
      border-radius: 20px;
      font-weight: 600;
      font-size: 14px;
      color: #FFF;
      @include x-c;
    }
  }




}