import React, { Component } from "react";
import { connect } from "react-redux";
import { ImagePicker ,Toast,WhiteSpace,WingBlank,Modal } from "antd-mobile";
// import { MobilePDFReader,PDFReader } from 'react-read-pdf';
import { Document, Page,pdfjs  } from 'react-pdf';
import pdfjsWorker from "pdfjs-dist/build/pdf.worker.entry";

pdfjs.GlobalWorkerOptions.workerSrc = pdfjsWorker;
import action from "../modules/entryRegister/redux/action";

const alert = Modal.alert;
class CompUpLoad extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isFile:false,
      imgShow:false,
      imgUrl:"",
      historyLength:window.history.length,
      numPages:null,
      pageNumber: 1,
    }
  }
  componentDidMount() {
  }
  //处理图片
  handleFilesonChange(file, type, index,fieldCode){
    console.log(file, type, index,fieldCode)
    if(type==="add"){
      let name = file[file.length-1].file.name
      Toast.loading('上传中...')
      let handleFile = file[file.length-1]
      if(handleFile.file.type == "application/pdf"){
        // this.handleUpload(handleFile.file,type,fieldCode,name)
        this.handleFile(file).then((res)=>{this.handleUpload(res,type,fieldCode,name,index)}).catch((err)=>{
          Toast.info(err, 1);
        })
      }else if(handleFile.file.type.includes('image')){
        // if(fieldCode=="profilePicture"){
        //   this.handleUpload(handleFile.file,type,fieldCode,name,index)
        // }else{
          this.handleImg(file).then((res)=>{this.handleUpload(res,type,fieldCode,name,index)}).catch((err)=>{
            Toast.info(err, 1);
          })
        // }
      }else{
        this.handleOther(file).then((res)=>{}).catch((err)=>{
          Toast.info(err, 1);
        })
      }
    }
    if(type==="remove"){
      this.props.removeResult(file[0],fieldCode,index);
    }
  }
  handleOther(file){
    return new Promise((resolve, reject) => {
      reject('仅允许上传图片与PDF');
    });
  }
  handleFile(file){
    return new Promise((resolve, reject) => {
      let handleFile = file[file.length-1]
      let isLt2M = handleFile.file.size / 1024 / 1024 < 10; // 判定图片大小是否小于10MB
      if (!isLt2M) {
        reject('附件大小不得超过10MB');
      }
      resolve(handleFile.file)
    });
  }
  handleImg(file){
    return new Promise((resolve, reject) => {
      let handleFile = file[file.length-1]
      let isLt2M = handleFile.file.size / 1024 / 1024 < 10; // 判定图片大小是否小于10MB
      if (!isLt2M) {
          reject('图片大小不得超过10MB');
      }
      let image = new Image(),
          resultBlob = "";
      image.src = URL.createObjectURL(handleFile.file);
      image.onload = () => {
          // 调用方法获取blob格式，方法写在下边
          resultBlob = this.compressUpload(image);
          resolve(resultBlob);
      };
      image.onerror = () => {
          reject();
      };
    });
  }
  //压缩
  compressUpload(image){
    let canvas = document.createElement("canvas");
    let ctx = canvas.getContext("2d");
    let initSize = image.src.length;
    let { width } = image,
        { height } = image;
    canvas.width = width;
    canvas.height = height;
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    ctx.drawImage(image, 0, 0, width, height);

    // 进行最小压缩0.1
    let compressData = canvas.toDataURL("image/jpeg", 0.1);

    // 压缩后调用方法进行base64转Blob，方法写在下边
    let blobImg = this.dataURItoBlob(compressData);
    return blobImg;
  }
  /* base64转Blob对象 */
  dataURItoBlob(data) {
    let byteString;
    if (data.split(",")[0].indexOf("base64") >= 0) {
        byteString = atob(data.split(",")[1]);
    } else {
        byteString = unescape(data.split(",")[1]);
    }
    let mimeString = data.split(",")[0].split(":")[1].split(";")[0];
    let ia = new Uint8Array(byteString.length);
    for (let i = 0; i < byteString.length; i += 1) {
        ia[i] = byteString.charCodeAt(i);
    }
    return new Blob([ia], { type: mimeString });
  }
  //图片上传
  handleUpload(file,type,fieldCode,name,index){
    console.log(file,type,fieldCode)
    const { acUploadFile } = this.props;
    acUploadFile(file,name).then(res=>{
      if(res.success){
        this.props.uploadResult(res,fieldCode,index);
      }else{
        Toast.hide();
        Toast.info(res.message, 1);
      }
    })
  }
  handleClickImg(index,file){
    const { acDownload } = this.props;
    let fileData = file[index]
    // if(fileData.url.slice(-3)=="pdf"){
    if(fileData.newUrl){
      this.setState({
        isFile:true,
        imgShow:true,
        imgUrl:file[index].newUrl||""
      },()=>{
      })
    }else{
      this.setState({
        isFile:false,
        imgShow:true,
        imgUrl:file[index].url||""
      },()=>{
      })
    }
    // window.open(file[index].url)
    // console.log(index,file)
    // window.open(file[0].url)
  }
  handleImgHide(){
    const { historyLength,isFile } = this.state;
    // if(isFile){
    //   let _length = window.history.length - historyLength
    //   if(_length){
    //     window.history.go(-_length)
    //     this.setState({
    //       imgShow:false,
    //     },()=>{
          
    //     })
    //   }
    //   // window.history.go(-_length)
    // }
    this.setState({
      imgShow:false,
    })
  }
  onDocumentLoadSuccess(a){
    this.setState({ 
      pageNumber:a.numPages
    });
  }

  render() {
    const { files,fieldCode,isDisable,disabled} = this.props;
    const { imgShow,imgUrl,isFile ,pageNumber} = this.state;
    let imgbackGround = {
      zIndex: '9999',
      height: '80vh',
      // width: '100%',
      position: 'fixed',
      left: '0px',
      top: '0px',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center'
    }
    let imgSize = {
      width:'100%',
    }
    let imgPicker={
      height: '25vw',
      width: '25vw',
    

    }
    let boo0 = disabled ? files.length < 0 : (files[0]?[files[0]]:[]).length < 1;
    let boo1 = disabled ? files.length < 0 : (files[1]?[files[1]]:[]).length < 1;
    let tip = !disabled;
    return (
      <div>
        {
          fieldCode == "profilePicture" ?
          <section style={{display:'flex'}}>
            <div style={{textAlign:'center'}}>
              <ImagePicker
              accept="image/*,.pdf"
              files={files[0]?[files[0]]:[]}
              onChange={(value,type,index)=>{this.handleFilesonChange(value,type,0,fieldCode)}}
              onImageClick={(index,file)=>{this.handleClickImg(0,file)}}
              length="1"
              selectable={boo0}
              disableDelete={isDisable}//显示隐藏删除按钮
              style={imgPicker}
              ></ImagePicker>
              {
                tip ?
                <p style={{color:"rgb(100, 100, 100)",fontSize:"12px"}}>人像面</p> : 
                null
              }
            </div>
            <div style={{textAlign:'center'}}>
              <ImagePicker
                accept="image/*,.pdf"
                files={files[1]?[files[1]]:[]}
                onChange={(value,type,index)=>{this.handleFilesonChange(value,type,1,fieldCode)}}
                onImageClick={(index,file)=>{this.handleClickImg(0,file)}}
                length="1"
                selectable={boo1}
                disableDelete={isDisable}//显示隐藏删除按钮
                style={imgPicker}
                ></ImagePicker>
                {
                  tip ?
                  <p style={{color:"rgb(100, 100, 100)",fontSize:"12px"}}>国徽面</p> : 
                  null
                }
            </div>
          </section> :
          <ImagePicker
         
            accept="image/*,.pdf"
            files={files[0]?[files[0]]:[]}
            onChange={(value,type,index)=>{this.handleFilesonChange(value,type,index,fieldCode)}}
            onImageClick={(index,file)=>{this.handleClickImg(index,file)}}
            selectable={
              disabled ? files.length < 0 :
              (fieldCode == "profilePicture" && files.length < 2) || files.length < 1
            }
            disableDelete={isDisable}//显示隐藏删除按钮
            ></ImagePicker>
        }
        {/* <ImagePicker
          accept="image/*,.pdf"
          files={files}
          onChange={(value,type,index)=>{this.handleFilesonChange(value,type,index,fieldCode)}}
          onImageClick={(index,file)=>{this.handleClickImg(index,file)}}
          selectable={
            disabled ? files.length < 0 :
            (fieldCode == "profilePicture" && files.length < 2) || files.length < 1
          }
          disableDelete={isDisable}//显示隐藏删除按钮
          ></ImagePicker> */}
          {/* {
          imgShow ? 
          <section style={imgbackGround} onClick={()=>{this.handleImgHide()}}>
            <img src={imgUrl} alt="" style={imgSize} />
          </section> :
          null
          } */}
          <WingBlank>
            <WhiteSpace/>
              <Modal
                popup
                animationType='fade'
                visible={imgShow}
                transparent
                closable
                onClose={()=>{this.handleImgHide()}}
                className="modalClass"
                wrapClassName="wrapModalClass"
              >
                {
                  isFile?
                  <div style={{overflow:'auto',height:'100vh',display:'flex',justifyContent:'center'}}>
                    {/* <MobilePDFReader
                      url={imgUrl} 
                      isShowHeader={false}
                      isShowFooter={false}
                    /> */}
                    <Document
                      file={imgUrl}
                      onLoadSuccess={(a)=>{this.onDocumentLoadSuccess(a)}}
                      loading="正在努力加载中"
                    >
                      {/* <Page pageNumber={1} /> */}
                      {
                    new Array(pageNumber+1).fill('').map((item, index) => {
                      return <div><Page scale={0.5} noData={null} width={600} key={index} pageNumber={index}/><br/></div>;
                    })}
                    </Document>
                  </div> :
                  // <iframe src={`/static/pdfjs/web/viewer.html?file=${imgUrl}`} frameborder="0" style={{width: '100%',height:'90vh',marginTop:'20px'}}></iframe>:
                  <div style={{maxHeight: '90vh'}}>
                    <img src={imgUrl} alt="" style={imgSize} />
                  </div>
                }
              </Modal>
            <WhiteSpace/>
          </WingBlank>
      </div>
      
    );
  }
}

const mapStateToProps = (state) => ({

});

const mapDispatchToProps = {
  acUploadFile:action.acUploadFile,
  acDownload:action.acDownload
};

export default connect(mapStateToProps, mapDispatchToProps)(CompUpLoad);

