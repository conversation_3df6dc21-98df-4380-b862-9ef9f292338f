import React, { Component } from "react";
import {with<PERSON>outer} from "react-router-dom";
import { Tabs, List, Button ,Toast} from "antd-mobile";
import action from "./redux/action";
import "./index.scss";
import { connect } from "react-redux";
import Detail from "./pages/detail";

class EmployeeInfo extends Component {
  constructor(props) {
    super(props);
    this.state = {

    };
  }
  
  componentDidMount() {
    this.handleGetUrlMsg();//获取小程序传参
    this.handleRecord();
    // this.props.history.replace("/employee-info/index");
  }

  //获取小程序传参
  handleGetUrlMsg(){
    //if(!!sessionStorage.getItem("token")) return;
    const { getUrlParams } = this.props;
    const token = getUrlParams ('token',this.props.history.location.search)
    if(!sessionStorage.getItem("token")){
      sessionStorage.setItem("token",token)
      this.setState({
        empId:'',
      },()=>{
        //跳转员工详情
        //this.props.history.push({ pathname:'/employee-info/Detail'});
      })
    }
    // this.props.history.replace("/employee-info/index");
  }

  handleRecord(){
    let url = window.env.domain+'/api/olading-user/user/operate/record?operateType=EMPLOYEE_INFO'
    let token = sessionStorage.getItem("token"); 
    fetch(url, { method: 'GET',
      headers:  {
        'Authorization': token||"",
        'Content-Type': 'application/json'
      },
    }).then(res => res.json())
    .catch(error => console.error('Error:', error))
    .then(response => console.log('Success:', response));
  }

  render() {
    return (
      <div className="employee-info">
        <Detail history={this.props.history}></Detail>
      </div>
    );
  }
}

const mapStateToProps = (state) => ({
});

const mapDispatchToProps = {
  getUrlParams:action.getUrlParams,//url参数
};

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(EmployeeInfo));

