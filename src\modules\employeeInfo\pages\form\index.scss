.entry-form {
    //height: calc(100vh - 50px);
    // .submit-btn {
    //     width: calc(100% - 40px);
    //     position: fixed !important;
    //     bottom: 0;
    //     left: 0;
    //     margin: 0 20px;
    //     border-radius: 20px;
    // }
    .label-name {
        margin-left: 7px;
    }
    .am-list-item .am-list-line .am-list-content {
        margin: 10px 20px 0 20px;
        padding-bottom: 0 !important;
        position: relative;
        //border-bottom: 1px solid #e6e7e9 !important;
        line-height: 1;
    }
    .am-list-item.am-input-item {
        height: 30px;
        padding-left: 0;
    }
    .am-list-item .am-list-line {
        padding-right: 0;
    }
    .am-list-item {
        padding-left: 0;
    }
    .am-list-item .am-list-line .am-list-arrow-horizontal {
        position: absolute;
        right: 0;
    }
    .am-list-item .am-list-line .am-list-extra {
        text-align: left;
    }
    .am-list-item{
        .am-list-line{
            .am-list-content{
                .am-list-item{
                    border-bottom: 1px solid #e6e7e9 !important;
                }
            }
        }
    }
    .footer{
        height: 80px;
        width: 100%;
        position: fixed;
        bottom: 0;
        .submit-btn{
            margin: 0 26px;
            border-radius: 20px;
            height: 40px;
            line-height: 40px !important;
        }
    }
    .requiredNode{
        color: red;
        font-size: 12px;
        // display: none;
    }
    .groupTitle{
        height: 30px;
        background: #F2F2F2;
        margin-top:10px;
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #757F8C;
        //line-height: 30px;
        padding: 0 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        button{
            height: 20px;
            padding: 0 5px;
        }
    }
    .groupTitleP::before{
        content: "|";
        padding-right: 5px;
        font-weight: bold;
        font-size: 13px;
        color: #4185f8;
    }
    .my-drawer {
        position: relative;
        overflow: auto;
        -webkit-overflow-scrolling: touch;
    }
    .my-drawer .am-drawer-sidebar {
        background-color: #fff;
        overflow: auto;
        -webkit-overflow-scrolling: touch;
    }
    .my-drawer .am-drawer-sidebar .am-list {
        width: 300px;
        padding: 0;
    }
    .windowDisable{
        z-index: 1000; 
        position: fixed;
        top: 0;
        right: 0;
        left: 0;
        bottom: 0;
    }
}
