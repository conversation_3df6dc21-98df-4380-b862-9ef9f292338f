import React, { Component } from "react";
import { withRouter } from "react-router-dom";
import {
  List,
  Button,
  Toast,
  InputItem,
  DatePicker,
  Picker,
  ImagePicker,
  NavBar,
  Icon,
  Modal,
} from "antd-mobile";
import "./scss/CompForm.scss";
import { createForm } from "rc-form";
import { connect } from "react-redux";
import action from "modules/entryRegister/redux/action";
import { validPhoneReg, valideEmail, validIdNo } from "utils/validReg";
import { checkMobileField, checkEmailField } from "utils/constData";
import { tipsUpload } from "utils/tipsUpload";
import Footer from "components/Footer";
import Upload from "components/UpLoad";
import take_pictures from "../../../assets/images/take_pictures.png";


const alert = Modal.alert;

class CompForm extends Component {
  constructor(props) {
    super(props);
    this.state = {
      list: [], //详情
      formStart: {}, //初始化表单数据
      form: {}, //待提交表单数据
      checkform: {}, //校验字段
      files: {}, //附件信息
      groupName: "", //分组名称
      groupCode: "", //分组编码
      addMultipleYn: false, //是否支持多条
      empId: "", //企业员工ID
      id: "", //操作ID
      operation: {
        //操作类型
        ADD: "ADD",
        EDIT: "EDIT",
        DELETE: "DELETE",
      }, //操作名
      isRequired: {}, //必填校验
      isChecked: {}, //手机格式校验
      isCheckedEmail: {}, //邮箱格式校验
      isCheckedNum: {}, //数字格式校验
      date: {}, //存放日期值
      isSave: true, //是否保存成功
      isChange: false, //是否编辑过
      isDisable: false, //页面是否禁止编辑 true:全屏禁止操作
      empRecordId: "", //用工记录id
    };
  }
  //初始化数据
  componentDidMount() {
    this.props.onRef(this); //向父组件抛出方法
    const { groupName, groupCode, empId, addMultipleYn, def_lableCode, empRecordId } =
      this.props.data;
    const isDisable = def_lableCode == "POST_INFO" ? true : false; //POST_INFO:岗位信息-岗位信息中的分组不允许编辑
    const list = this.props.list;
    console.log('list', list)


    const id = this.props.id;
    let form = {};
    let files = {};
    let date = {};
    let isSave = id === null ? false : true;
    let isChange = id === null ? true : false;
    list.map((item, index) => {
      form[item.fieldCode] = item.fieldValue || "";

      if (item.fieldType === "OPTION") {
        this.handleOption(item); //初始化option
      }
      if (item.fieldType === "FILE") {
        let filedata = JSON.parse(item.fieldValue) || [],
          fileArr = [],
          formArr = [];
        if (item.fieldCode == "profilePicture" && filedata.length == 0) {
          fileArr.push("", "");
          formArr.push("", "");
        }
        filedata.forEach((value, index, array) => {
          let fileObj = {};
          if (!!value["archiveId"]) {
            if (value["url"] && value["url"].slice(-3) == "pdf") {
              fileObj["id"] = value["archiveId"];
              fileObj["url"] = "static/fujian.webp";
              fileObj["newUrl"] = value["url"];
            } else {
              fileObj["id"] = value["archiveId"] || "";
              fileObj["url"] = value["url"] || "";
            }
            fileArr.push(fileObj);
            formArr.push(value["archiveId"]);
          } else {
            fileArr.push("");
            formArr.push("");
          }
        });
        files[item.fieldCode] = fileArr || [];
        form[item.fieldCode] = formArr || [];
      }
      if (item.fieldType === "DATE") {
        date[item.fieldCode] = (item.fieldValue || "") == "" ? "" : new Date(item.fieldValue); //初始化时间格式
      }
    });
    console.log('form', form)
    let cardInfo = this.props.cardInfo;
    const baseInfo = this.props.baseInfo
    console.log(cardInfo, baseInfo)
    if (groupCode == "BASIC_INFO" && baseInfo) {
      form = baseInfo
    }

    //上传身份证回显
    if (cardInfo) {
      form.idNo = cardInfo.idCardNo;
      form.empName = cardInfo.name;
      form.nationality = cardInfo.nation;
    }

    let formStart = JSON.parse(JSON.stringify(form));
    this.setState({
      form,
      files,
      date,
      list,
      groupName,
      groupCode,
      empId,
      id,
      addMultipleYn,
      formStart,
      isSave,
      isChange,
      isDisable,
      empRecordId,
    });
  }

  //对list里面type是option的数据处理
  handleOption(item) {
    const { selectedInfo, countrylist, citylist, provincelist } = this.props;
    let optionArr = [],
      cols = 1;
    switch (item.fieldCode) {
      case "country":
        countrylist.map((it, index) => {
          optionArr.push({
            label: it,
            value: it,
          });
        });
        cols = 1;
        break;
      case "householdCountry":
      case "workCity":
        // provincelist.map((it,index)=>{
        // optionArr.push({
        //   label: it.name,
        //   value: it.code,
        // });
        // })
        provincelist.map((it, index) => {
          optionArr.push(this.handleTreeOption(it));
        });
        cols = 3;
        break;
      default:
        item.options.map((it) => {
          optionArr.push({
            label: it.optionEnumName,
            value: it.optionEnumCode,
            enableYn: it.enableYn,
          });
        });
        cols = 1;
        break;
    }
    item.optionList = optionArr;
    item.cols = cols;
    return item;
  }
  handleTreeOption(item) {
    let arr = item.children;
    return {
      label: item.dictName,
      value: item.dictCode,
      children: arr ? this.handleTreeOptionChildren(arr, item) : [],
    };
  }
  handleTreeOptionChildren(item, items) {
    let arr = [];
    // console.log(item, items);
    arr.push(...this.handleAllArea(items));
    if (
      items.dictCode.slice(2) == "0000" &&
      item.length !== 0 &&
      item[0].dictCode.slice(4) !== "00" &&
      item[0].dictCode.slice(2, 4) !== "90"
    ) {
      let obj = {
        label: items.dictName,
        value: `${items.dictCode.slice(0, 2)}0100`,
      };
      arr.push({
        ...obj,
        children: this.handleTreeOptionChildren(items.children, {
          dictCode: "999999",
          oldData: { dictCode: `${items.dictCode.slice(0, 2)}0100` },
        }),
      });
    } else if (item[0].dictCode.slice(2, 4) == "90") {
      item.map((v) => {
        arr.push({
          label: v.dictName,
          value: v.dictCode,
          children: v.children ? this.handleTreeOptionChildren(v.children, v) : [],
        });
      });
    } else {
      item.map((v) => {
        arr.push({
          label: v.dictName,
          value: v.dictCode,
          children: v.children ? this.handleTreeOptionChildren(v.children, v) : [],
        });
      });
    }
    return arr;
  }
  handleAllArea(item) {
    if (item.dictCode.slice(2) == "0000") {
      //市
      return [
        {
          label: "全部",
          value: `${item.dictCode.slice(0, 2)}0000`,
          children: [
            {
              label: "全部",
              value: `${item.dictCode.slice(0, 2)}0000`,
            },
          ],
        },
      ];
    }
    if (item.oldData) {
      return [
        {
          label: "全部",
          value: `${item.oldData.dictCode.slice(0, 4)}00`,
          children: [],
        },
      ];
    }
    if (item.dictCode.slice(2) !== "0000" && item.dictCode.slice(4) == "00") {
      //区
      return [
        {
          label: "全部",
          value: `${item.dictCode.slice(0, 4)}00`,
          children: [],
        },
      ];
    }
    return [];
  }

  //校验某字段是否存在
  handleCheckOwnProp(item) {
    return this.state.form.hasOwnProperty(item);
  }
  //证件号码格式校验
  handleCheckIdNo() {
    if (this.handleCheckOwnProp("idNo") && this.handleCheckOwnProp("idType")) {
      const { idNo, idType } = this.state.form;
      const resdata = validIdNo(idNo, idType);
      console.log("已校验证件号手机号");
      if (!resdata.boo) Toast.info(resdata.data);
      return resdata.boo;
    } else {
      console.log("未校验证件号手机号");
      return true;
    }
  }

  //手机号格式校验
  handleCheckMobile() {
    const { form, list, groupCode } = this.state;
    const checkField = checkMobileField[groupCode] || [];
    let newisChecked = {},
      boo = true;
    checkField.map((item, index) => {
      if (this.handleCheckOwnProp(item)) {
        let resdata = validPhoneReg(form[item]);
        resdata.boo ? (newisChecked[item] = false) : (newisChecked[item] = true) && (boo = false);
      }
    });
    newisChecked = Object.assign(this.state.isChecked, newisChecked);
    this.setState({
      isChecked: newisChecked,
    });
    console.log("已校验手机格式");
    return boo;
  }

  //邮箱格式校验
  handleCheckEmail() {
    const { form, list, groupCode } = this.state;
    const checkField = checkEmailField[groupCode] || [];
    let newisCheckedEmail = {},
      boo = true;
    checkField.map((item, index) => {
      if (this.handleCheckOwnProp(item)) {
        let resdata = valideEmail(form[item]);
        resdata.boo
          ? (newisCheckedEmail[item] = false)
          : (newisCheckedEmail[item] = true) && (boo = false);
      }
    });
    newisCheckedEmail = Object.assign(this.state.isCheckedEmail, newisCheckedEmail);
    this.setState({
      isCheckedEmail: newisCheckedEmail,
    });
    console.log("已校验邮箱格式");
    return boo;
  }
  //Ocr识别
  async handleCheckOcr() {
    const { acOcrCheck, acSetCardInfo } = this.props;
    const { empId, form } = this.state;
    await acOcrCheck({
      compEmpId: empId,
      nationalPhoto: form.profilePicture[1],
      personalPhoto: form.profilePicture[0],
    }).then((res) => {
      console.log("res=========>", res)
      // this.handleOcrAlert()
      if (res.success) {
        res.data.ocrInfo ?
          acSetCardInfo({
            ...res.data.ocrInfo,
            nationalPhoto: form.profilePicture[1],
            personalPhoto: form.profilePicture[0]
          }) : acSetCardInfo(null)

        if (res.data.ocrIdNo !== res.data.originalIdNo) {
          this.handleOcrAlert(res.data.flowNo);
        } else {

          this.submit({
            isCheckOk: true,
            isUpdate: false,
          });
        }
      } else {
        Toast.info(res.message);
      }
    });
  }
  handleOcrAlert(flowNo) {
    alert("证件号码不一致", "系统检测到您上传的身份证照片和已录入的证件号码不一致，请检查！", [
      {
        text: "自行修改",
        onPress: () =>
          this.submit({
            isCheckOk: true,
            isUpdate: false,
          }),
      },
      {
        text: "直接更新",
        onPress: () =>
          this.submit({
            isCheckOk: true,
            isUpdate: true,
            flowNo: flowNo,
          }),
      },
    ]);
  }
  //数字格式校验
  handleCheckNumber() {
    const { form, list } = this.state;
    let newisCheckedNum = {},
      boo = true;
    list.map((item, index) => {
      if (item.fieldType === "NUMBER") {
        this.handleReg(form[item.fieldCode], item["length"]) || form[item.fieldCode] == ""
          ? (newisCheckedNum[item.fieldCode] = false)
          : (newisCheckedNum[item.fieldCode] = true) && (boo = false);
      }
    });
    newisCheckedNum = Object.assign(this.state.isCheckedNum, newisCheckedNum);
    this.setState({
      isCheckedNum: newisCheckedNum,
    });
    console.log("已校数字格式");
    return boo;
  }

  //失去焦点：数据校验 -证件号+手机号 -必填项
  checkIdCard(v, type, isrequired) {
    const { idNo, mobile, idType } = this.state.form;
    const { empId } = this.state;
    const { acCheckIdAndMobile } = this.props;
    if ((type === "idNo" || type === "mobile") && idNo !== "" && mobile !== "" && idType !== "") {
      acCheckIdAndMobile({
        compEmpId: empId,
        // compId:compId,
        idNo: idNo,
        idType: idType,
        mobile: mobile,
      }).then((res) => {
        if (res.success) {
          if (Object.keys(res.data).length !== 0) {
            let newForm = {},
              newDate = {};
            newForm = Object.assign({}, this.state.form, {
              ["empAge"]: res.data.age,
              ["birthdayDate"]: res.data.birthday,
              ["birthday"]: res.data.birthday,
              ["empSex"]: res.data.sex,
            });
            newDate = Object.assign({}, this.state.date, {
              ["birthdayDate"]: new Date(res.data.birthday),
              ["birthday"]: new Date(res.data.birthday),
            });
            this.setState({ form: newForm, date: newDate }, () => {
              console.log("自动计算成功");
            });
          }
        } else {
          Toast.info(res.message);
        }
      });
    }
    if (isrequired) {
      this.setState(
        {
          isRequired: Object.assign(this.state.isRequired, { [type]: v !== "" ? false : true }),
        },
        () => {
          console.log("校验必填字段：" + type);
        }
      );
    }
  }
  //失去焦点：数据校验 -小数点个数
  checkNumber(v, type, isrequired, numLength) {
    let val = Number(v).toFixed(numLength);
    let newForm = Object.assign({}, this.state.form, { [type]: val });
    this.setState({
      form: newForm,
    });
    // this.setState({
    //   isCheckedNum:Object.assign(this.state.isCheckedNum,{[type]: this.handleReg(v,numLength)||v=='' ? false : true})
    // },()=>{
    //   console.log("校验数字字段："+type)
    // });
  }
  //双向绑定
  handleChange(val, formIndex, type, isrequired) {
    console.log(val, formIndex);
    let newForm = {},
      newDate = {};
    switch (type) {
      case "OPTION":
        if (["householdCountry", "workCity"].includes(formIndex)) {
          val = val[val.length - 1];
          newForm = Object.assign({}, this.state.form, { [formIndex]: val });
        } else {
          newForm = Object.assign({}, this.state.form, { [formIndex]: val.join("") });
          //证件类型选择居民身份证，国籍默认中国
          if (formIndex == "idType") {
            if (newForm.idType == "PRC_ID") {
              newForm.country = "中国";
            } else {
              newForm.country = "";
            }
          }
        }
        // newForm = Object.assign({}, this.state.form, { [formIndex]: val.join('') });
        this.setState({ form: newForm }, () => {
          this.handleCheckChange();
        });
        break;
      case "NUMBER":
        newForm = Object.assign({}, this.state.form, { [formIndex]: val });
        this.setState({ form: newForm }, () => {
          this.handleCheckChange();
        });
        break;
      case "DATE":
        // if(formIndex=="birthday"){//自动计算年龄
        //   newForm = Object.assign({}, this.state.form, { [formIndex]: this.handleDate(val),["empAge"]:this.handleMathAge(val,formIndex) })
        // }else{
        newForm = Object.assign({}, this.state.form, { [formIndex]: this.handleDate(val) });
        // }
        newDate = Object.assign({}, this.state.date, { [formIndex]: val });
        this.setState({ form: newForm, date: newDate }, () => {
          this.handleCheckChange();
        });
        break;
      default:
        newForm = Object.assign({}, this.state.form, { [formIndex]: val });
        this.setState({ form: newForm }, () => {
          this.handleCheckChange();
        });
    }
    if (isrequired) {
      this.setState(
        {
          isRequired: Object.assign(this.state.isRequired, {
            [formIndex]: val !== "" ? false : true,
          }),
        },
        () => {
          console.log("校验必填字段：" + formIndex);
        }
      );
    }
  }

  //跳转身份证
  handleUploadClick() {
    const { acSetBaseInfo } = this.props;
    acSetBaseInfo(this.state.form)
    this.props.history.push({ pathname: "/entry-register/card" });
  }

  //表单编辑与否
  handleCheckChange() {
    this.setState({ isSave: false, isChange: true }, () => {
      this.props.onRef(this); //向父组件抛出方法-->父组件统一判断子组件是否都编辑过
    });
    // if(JSON.stringify(this.state.form)!==JSON.stringify(this.state.formStart)){
    //   this.setState({isSave:false,isChange:true},()=>{
    //     this.props.onRef(this)//向父组件抛出方法-->父组件统一判断子组件是否都编辑过
    //   })
    // }else{
    //   this.setState({isSave:true,isChange:false},()=>{
    //     this.props.onRef(this)//向父组件抛出方法-->父组件统一判断子组件是否都编辑过
    //   })
    // }
  }
  //时间格式转换 ->2020-1-1
  handleDate(value) {
    const newDate =
      value.getFullYear() +
      "-" +
      (value.getMonth() + 1 + "").padStart(2, 0) +
      "-" +
      (value.getDate() + "").padStart(2, 0);
    return newDate;
  }
  //数字格式正则校验
  handleReg(value, numLength) {
    let reg =
      numLength == "0"
        ? new RegExp(`^([0-9]{1}\\d*)$`)
        : new RegExp(`^(([0-9]{1}\\d*)|(0{1}))(\\.\\d{${numLength}})$`);
    return reg.test(value);
  }
  //计算年龄
  handleMathAge(val, formIndex) {
    console.log(new Date(new Date() - val).getFullYear() - 1970);
    return new Date(new Date() - val).getFullYear() - 1970;
  }

  //back
  handleBack() {
    //this.props.history.push({ pathname:'/entry-register/detail'});
    window.history.back(-1);
  }
  //提交前校验必填
  handleCheck() {
    const { form, list } = this.state;
    let newisRequired = {},
      boo = true; //true -> 校验通过；false ->校验失败
    list.map((item, index) => {
      if (item.isRequired) {
        if (item.fieldCode == "profilePicture") {
          form[item.fieldCode][0] == "" || form[item.fieldCode][1] == ""
            ? (newisRequired[item.fieldCode] = true) && (boo = false)
            : (newisRequired[item.fieldCode] = false);
        } else {
          form[item.fieldCode] == ""
            ? (newisRequired[item.fieldCode] = true) && (boo = false)
            : (newisRequired[item.fieldCode] = false);
        }
      }
    });
    newisRequired = Object.assign(this.state.isRequired, newisRequired);
    this.setState({
      isRequired: newisRequired,
    });
    console.log("已校验必填");
    return boo;
  }
  //数据删除-保存
  handleDelete() {
    const { id, groupCode, empId, empRecordId } = this.state;
    const { acOperateEmp, optionSource } = this.props;
    let data = {
      id,
      empId,
      groupCode,
      param: {},
      empRecordId,
      operation: "DELETE",
      optionSource, //员工详情-修改操作-新增参数
    };
    console.log(data);
    acOperateEmp(data).then((res) => {
      if (res.success) {
        this.setState({ isSave: true, id: res.data.id }, () => {
          this.props.onRef(this); //向父组件抛出方法-->父组件统一判断子组件是否都保存成功
        });
      } else {
        Toast.info("删除失败");
      }
    });
  }
  //提交
  submit(boo) {
    const { isCheckOk, isUpdate, flowNo } = boo || {
      isCheckOk: false,
      isUpdate: false,
      flowNo: undefined,
    };
    console.log(isCheckOk, isUpdate);
    const { id, operation, groupCode, empId, compId, form } = this.state;

    if (!this.handleCheck()) {
      //提交前校验必填
      return;
    }
    // if(!this.handleCheckNumber()){//提交前校验数字格式
    //   return
    // }
    if (!this.handleCheckMobile()) {
      //提交前校验手机号格式
      return;
    }
    if (!this.handleCheckEmail()) {
      //提交前校验邮箱格式
      return;
    }
    if (groupCode === "BASIC_INFO") {
      //基本信息校验格式
      if (!this.handleCheckIdNo()) {
        //提交前校验身份证件信息格式
        return;
      }
    }
    //ocr
    console.log('form.profilePicture', form)
    if (!isCheckOk && groupCode === "ARCHIVE_INFO" && form.profilePicture[0] !== "") {
      this.handleCheckOcr();
      return;
    }

    const { acOperateEmp, optionSource, acSetCardInfo, acSetGroupRequired, cardInfo } = this.props;
    const { addMultipleYn, empRecordId } = this.state;
    const _operation = !id ? operation["ADD"] : operation["EDIT"];
    const _id = id || 0;
    let data = {
      id: _id,
      empId,
      groupCode,
      param: form,
      empRecordId,
      operation: _operation,
      optionSource, //登记详情-修改操作-新增参数
      ocrFlowNo: flowNo, //证件号识别流水
      updateProfile: isUpdate, //证件号识别流水
    };

    if (groupCode === "ARCHIVE_INFO") {
      data.ocrInfo = cardInfo
    };

    console.log(data);
    if (groupCode == "BASIC_INFO") {
      this.handleBasicInfo();
    } else {
      acOperateEmp(data).then((res) => {
        if (res.success) {
          //acSetGroupRequired({[groupCode]:true})//记录各个分组是否编辑
          this.setState({ isSave: true, id: res.data.id, formStart: form, isChange: false }, () => {
            this.props.onRef(this); //向父组件抛出方法-->父组件统一判断子组件是否都保存成功
            if (!addMultipleYn) {
              Toast.info(`保存成功`, 1, () => {
                this.handleBack();
              });
            }
            acSetCardInfo, (null)
          });
        } else {
          Toast.info("保存失败");
        }
      });
    }
  }
  handleBasicInfo() {
    const { id, operation, groupCode, empId, form, addMultipleYn, empRecordId } = this.state;
    const { optionSource, acupdateDetailH5, taxSubId, cardInfo } = this.props;
    const _operation = !empId ? operation["ADD"] : operation["EDIT"];
    const _id = id || 0;
    console.log('cardinfo', cardInfo)
    // const _operation = operation["EDIT"]
    let data = {
      operateEmpDto: {
        clientSource: "ENTRY_H5",
        empId: empId,
        groupCode: groupCode,
        id: _id,
        operation: _operation,
        empRecordId: empRecordId,
        optionSource: optionSource,
        param: form,
      },
      taxSubId: taxSubId,
      ocrInfo: cardInfo
    };



    acupdateDetailH5(data).then((res) => {
      console.log('acupdateDetailH5', res)
      if (res.success) {
        //acSetGroupRequired({[groupCode]:true})//记录各个分组是否编辑
        this.setState({ isSave: true, id: res.data.id, formStart: form, isChange: false }, () => {
          this.props.onRef(this); //向父组件抛出方法-->父组件统一判断子组件是否都保存成功
          if (!addMultipleYn) {
            Toast.info(`保存成功`, 1, () => {
              this.handleBack();
            });
          }
        });
      } else {
        Toast.info("保存失败");
      }
    });
  }
  /*
   * res:上传成功附件
   * fieldCode:表单id
   */
  //图片上传返回结果
  handleuploadResult(res, fieldCode, index) {
    const { url } = res.data;
    const fileFormArr = this.state.form[fieldCode];
    const fileFilesArr = this.state.files[fieldCode];
    if (fieldCode == "profilePicture") {
      fileFormArr[index] = res.data.archiveId;
      if (url.slice(-3) == "pdf") {
        // fileFilesArr.push({url:'static/fujian.webp',id:res.data.archiveId,newUrl:res.data.url})
        fileFilesArr[index] = {
          url: "static/fujian.webp",
          id: res.data.archiveId,
          newUrl: res.data.url,
        };
      } else {
        // fileFilesArr.push({url:res.data.url,id:res.data.archiveId})
        fileFilesArr[index] = { url: res.data.url, id: res.data.archiveId };
      }
    } else {
      fileFormArr.push(res.data.archiveId);
      if (url.slice(-3) == "pdf") {
        fileFilesArr.push({
          url: "static/fujian.webp",
          id: res.data.archiveId,
          newUrl: res.data.url,
        });
      } else {
        fileFilesArr.push({ url: res.data.url, id: res.data.archiveId });
      }
    }
    // fileFilesArr.push({url:res.data.url,id:res.data.archiveId})

    let newFilesForm = "",
      newFiles = "";
    newFilesForm = Object.assign(this.state.form, { [fieldCode]: fileFormArr });
    newFiles = Object.assign(this.state.files, { [fieldCode]: fileFilesArr });

    this.setState(
      {
        form: newFilesForm,
        files: newFiles,
      },
      () => {
        Toast.hide();
        this.handleCheckChange();
      }
    );
  }
  /*
   * res:剩余附件
   * fieldCode:表单id
   * fileIndex:删除附件索引
   */
  //图片移除返回结果
  handleremoveResult(res, fieldCode, fileIndex) {
    const fileFormArr = this.state.form[fieldCode];
    const fileFilesArr = this.state.files[fieldCode];

    let newfileFormArr, newfileFilesArr;
    if (fieldCode == "profilePicture") {
      newfileFormArr = fileFormArr.map((value, index) => {
        if (fileIndex == index) return "";
        return value;
      });
      newfileFilesArr = fileFilesArr.map((value, index) => {
        if (fileIndex == index) return "";
        return value;
      });
    } else {
      newfileFormArr = fileFormArr.filter((value, index) => {
        return fileIndex !== index;
      });
      newfileFilesArr = fileFilesArr.filter((value, index) => {
        return fileIndex !== index;
      });
    }

    let newFilesForm = "",
      newFiles = "";
    newFilesForm = Object.assign(this.state.form, { [fieldCode]: newfileFormArr });
    newFiles = Object.assign(this.state.files, { [fieldCode]: newfileFilesArr });
    this.setState(
      {
        form: newFilesForm,
        files: newFiles,
      },
      () => {
        this.handleCheckChange();
      }
    );
  }
  handleOptionFormat(value) {
    // console.log(value)
    if (value.length !== 0 && value[0] == value[1]) {
      let arr = [value[0], value[2]];
      return arr.join("/");
    }
    return value.join("/");
  }
  hanldeValue(val) {
    let value = val.join();
    if (value.substring(2, 4) == "90") {
      return value !== "" ? [...[value.substring(0, 2) + "0000", value]] : "";
    } else {
      return value !== ""
        ? [...[value.substring(0, 2) + "0000", value.substring(0, 4) + "00", value]]
        : "";
    }
  }

  renderItemByType(item) {
    const { form, files, date, isDisable, isSave } = this.state;
    const minDate = new Date(1949, 10, 1, 0, 0, 0);
    let maxDate
    const maxDateNotCurrentDateFieldNames = ['inWorkDay', "birthday", 'birthdayDate']
    if (maxDateNotCurrentDateFieldNames.includes(item.fieldCode)) {
      maxDate = new Date();
    } else {
      maxDate = new Date(2099, 10, 1, 0, 0, 0);
    }

    switch (item.fieldType) {
      case "TEXT":
        return (
          <>
            <InputItem
              clear //清除功能
              name={item.fieldCode}
              value={form[item.fieldCode]}
              onChange={(value) => {
                this.handleChange(value, item.fieldCode, "TEXT");
              }}
              defaultValue={form[item.fieldCode]} //默认值
              placeholder={item.fieldRemark}
              disabled={isDisable ? isDisable : !item.isEdit} //是否禁用
              onBlur={(v) => {
                this.checkIdCard(v, item.fieldCode, item.isRequired);
              }}
              maxLength={item["length"]} //最大长度限制
            ></InputItem>

            {/*拍身份证*/}

            {item.fieldCode == "idNo" && form.idType == "PRC_ID" ? (
              <div className="take" onClick={(e) => this.handleUploadClick(e)}>
                <img
                  src={take_pictures}
                />拍照录入

              </div>

            ) : null}
          </>
        );
      //}
      case "NUMBER":
        return (
          <InputItem
            type="money"
            moneyKeyboardAlign="left"
            clear //清除功能
            name={item.fieldCode}
            value={form[item.fieldCode]}
            onChange={(value) => {
              this.handleChange(value, item.fieldCode, "NUMBER");
            }}
            defaultValue={form[item.fieldCode]} //默认值
            placeholder={item.fieldRemark}
            disabled={isDisable ? isDisable : !item.isEdit} //是否禁用
            onBlur={(v) => {
              this.checkNumber(v, item.fieldCode, item.isRequired, item["length"]);
            }}
          ></InputItem>
        );
      case "DATE":
        return (
          <DatePicker
            minDate={minDate}
            maxDate={maxDate}
            mode="date"
            extra={item.fieldRemark}
            // value={form[item.fieldCode]}
            // onChange={(value)=>{this.handleChange(value,item.fieldCode,"DATE")}}

            value={date[item.fieldCode]}
            onChange={(value) => {
              this.handleChange(value, item.fieldCode, "DATE", item.isRequired);
            }}
            placeholder={item.fieldRemark}
            disabled={isDisable ? isDisable : !item.isEdit} //是否禁用
          >
            <List.Item arrow={isDisable ? "empty" : "horizontal"}></List.Item>
          </DatePicker>
        );
      case "OPTION":
        // return (
        //   <Picker
        //     data={item.optionList}//数据源{[value:'',label:'']}
        //     cols={1}
        //     extra="请选择"
        //     value={[form[item.fieldCode]]}
        //     //format={(labels) => { return labels.join(',');}}
        //     onChange={(value)=>{this.handleChange(value,item.fieldCode,"OPTION",item.isRequired)}}
        //     disabled={!item.isEdit}//是否不可用
        //   >
        //     <List.Item arrow={isDisable ? "empty" : "horizontal"}></List.Item>
        //   </Picker>
        // );
        if (["householdCountry", "workCity"].includes(item.fieldCode)) {
          return (
            <Picker
              data={item.optionList} //数据源{[value:'',label:'']}
              cols={item.cols}
              extra="请选择"
              value={this.hanldeValue([form[item.fieldCode]])}
              // format={(labels) => { return labels.join('-');}}
              // value={form[item.fieldCode]}
              format={(labels) => {
                return this.handleOptionFormat(labels);
              }}
              onChange={(value) => {
                this.handleChange(value, item.fieldCode, "OPTION", item.isRequired);
              }}
              disabled={isDisable ? isDisable : !item.isEdit}
            // format={(value)=>{this.handleOptionFormat(value)}}
            >
              <List.Item arrow={isDisable ? "empty" : "horizontal"}></List.Item>
            </Picker>
          );
        } else {
          return (
            <Picker
              data={item.optionList} //数据源{[value:'',label:'']}
              cols={1}
              extra="请选择"
              value={[form[item.fieldCode]]}
              //format={(labels) => { return labels.join(',');}}
              onChange={(value) => {
                this.handleChange(value, item.fieldCode, "OPTION", item.isRequired);
              }}
              disabled={!item.isEdit} //是否不可用
            >
              <List.Item arrow={isDisable ? "empty" : "horizontal"}></List.Item>
            </Picker>
          );
        }
      case "FILE":
        return (
          <div className="file-box">
            <Upload
              files={files[item.fieldCode]}
              fieldCode={item.fieldCode}
              uploadResult={this.handleuploadResult.bind(this)} //上传结果
              removeResult={this.handleremoveResult.bind(this)} //移除结果
              disabled={isDisable ? isDisable : !item.isEdit}
              isDisable={isDisable ? isDisable : !item.isEdit} //删除按钮显示隐藏
              isSave={isSave}
            ></Upload>
          </div>
        );
      default:
        return null;
    }
  }
  render() {
    const {
      list,
      form,
      groupName,
      groupCode,
      empId,
      id,
      operation,
      isRequired,
      isChecked,
      isCheckedEmail,
      isCheckedNum,
      isDisable,
    } = this.state;
    return (
      <div className="entry-form">
        <List>
          {list.map((item, index) => (
            <List.Item key={index}>
              <div>
                {item.isRequired ? <span className="required">*</span> : null}
                <span
                  className="label-name"
                  style={!item.isEdit || isDisable ? { color: "#a5a5a5" } : null}
                >
                  {item.fieldName}
                </span>
                {groupCode == "ARCHIVE_INFO" && !(!item.isEdit || isDisable) ? (
                  <p
                    style={{ color: "rgb(165, 165, 165)", fontSize: "12px", margin: "7px 0 0 7px" }}
                  >
                    {tipsUpload[item.fieldCode]}
                  </p>
                ) : null}
              </div>
              {this.renderItemByType(item)}
              {item.isRequired && isRequired[item.fieldCode] ? (
                <p className="requiredNode">{item.fieldName}不允许为空</p>
              ) : null}
              {isCheckedNum[item.fieldCode] ? (
                <p className="requiredNode">小数位数必须{item.length}位</p>
              ) : null}
              {checkMobileField[groupCode] &&
                checkMobileField[groupCode].includes(item["fieldCode"]) &&
                isChecked[item.fieldCode] ? (
                <p className="requiredNode">请检查电话格式</p>
              ) : null}
              {checkEmailField[groupCode] &&
                checkEmailField[groupCode].includes(item["fieldCode"]) &&
                isCheckedEmail[item.fieldCode] ? (
                <p className="requiredNode">请检查邮箱格式</p>
              ) : null}
            </List.Item>
          ))}
        </List>
      </div>
    );
  }
}

const mapStateToProps = (state) => ({
  selectedInfo: state.entryRegister.selectedInfo,
  countrylist: state.entryRegister.countrylist,
  citylist: state.entryRegister.citylist,
  optionSource: state.entryRegister.optionSource,
  provincelist: state.entryRegister.provincelist,
  taxSubId: state.entryRegister.taxSubId,

  cardInfo: state.entryRegister.cardInfo,
  baseInfo: state.entryRegister.baseInfo
});

const mapDispatchToProps = {
  acRegistDetail: action.acRegistDetail,
  acOperateEmp: action.acOperateEmp,
  acCheckIdAndMobile: action.acCheckIdAndMobile,
  acUploadFile: action.acUploadFile,
  acupdateDetailH5: action.acupdateDetailH5,
  acSetGroupRequired: action.acSetGroupRequired, //各个分组是否编辑保存必填字段
  acOcrCheck: action.acOcrCheck,
  acSetBaseInfo: action.acSetBaseInfo,  //跳转暂存基础信息
  acSetCardInfo: action.acSetCardInfo
};

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(CompForm));
