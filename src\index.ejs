<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
  <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0">
  <meta name="robots" content="noindex, nofollow">

  <!-- <meta http-equiv="Pragma" content="no-cache">
  <meta http-equiv="Cache-Control" content="no-cache">
  <meta http-equiv="Expires" content="0"> -->

  <title><%= htmlWebpackPlugin.options.title %></title>
  <link rel="shortcut icon" href="./bitbug_favicon.ico" type="image/x-icon">
  <link rel="icon" href="./static/bitbug_favicon.ico" type="image/x-icon">
  <!-- <script src="/config/env_config.js"></script>
  <script src="/static/wx.js"></script>
  <script src="/static/env.js"></script> -->
  <script src="/__PUBLIC_PATH_PLACEHOLDER__/config/env_config.js"></script>
  <script src="/__PUBLIC_PATH_PLACEHOLDER__/static/wx.js"></script>
  <script src="/__PUBLIC_PATH_PLACEHOLDER__/static/env.js?v=<%= htmlWebpackPlugin.options.appVersion %>"></script>
  <% for (var chunk in htmlWebpackPlugin.files.css) { %>
  <link rel="preload" href="<%= htmlWebpackPlugin.files.css[chunk] %>" as="style">
  <% } %>
  <% for (var chunk in htmlWebpackPlugin.files.chunks) { %>
  <link rel="preload" href="<%= htmlWebpackPlugin.files.chunks[chunk].entry %>" as="script">
  <% } %>

  <!-- <base href="/"> -->
</head>

<body>
  <%=htmlWebpackPlugin.files.webpackManifest%>
  <div id="app">
    <!--appContent-->
  </div>
  <!--appState-->
</body>

</html>