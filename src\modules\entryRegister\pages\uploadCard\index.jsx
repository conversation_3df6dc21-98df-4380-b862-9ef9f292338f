import React, { Component } from "react";
import "./index.scss";
import {
  ImagePicker,
  NavBar,
  Icon,
  Toast
} from "antd-mobile";

import { connect } from "react-redux";
import action from "modules/entryRegister/redux/action";


class UploadCard extends Component {
  constructor(props) {
    super(props);
    this.state = {
      list: [
        {
          img: require("../../../../assets/images/card/face1.png"),
          tip: "标准",
          icon: "success",
        },
        {
          img: require("../../../../assets/images/card/face2.png"),
          tip: "边框缺失",
        },
        {
          img: require("../../../../assets/images/card/face3.png"),
          tip: "照片模糊",
        },
        {
          img: require("../../../../assets/images/card/face4.png"),
          tip: "闪光强烈",
        },
      ],   
 
      personalPhoto:[], //front
      nationalPhoto:[], //back
      

    };
  }

  //back
  handleBack() {
    window.history.back(-1);
  }

  //跳转详情
  handleDetailClick() {
    const { acOcrResult,acSetCardInfo } = this.props;
    const {personalPhoto,nationalPhoto} = this.state;

    if(!personalPhoto.length) return Toast.info('请上传人像面照片', 1);
    if(!nationalPhoto.length) return Toast.info('请上传国徽面照片', 1);

    acOcrResult({personalPhoto:personalPhoto[0].id ,nationalPhoto:nationalPhoto[0].id}).then(res=>{
      if(res.success){
        acSetCardInfo({
          ...res.data,
           nationalPhoto:nationalPhoto[0].id,
           personalPhoto:personalPhoto[0].id
          })
         this.props.history.push({ pathname: "/entry-register/uploadDetail" });  
      }else{
        Toast.hide();
        Toast.info(res.message, 1);
      }
    })
  }

   //处理图片
   handleFilesonChange(file, type, index,str){
    console.log("handleFilesonChange>", file, type, index,str)
    if(type==="add"){
      let name = file[file.length-1].file.name
      Toast.loading('上传中...')
      this.handleImg(file).then((res)=>{this.handleUpload(res,type,name,str)}).catch((err)=>{
        Toast.info(err, 1);
      })   
    }
    if(type==="remove"){
      str == 'front' ? this.setState(
        {
          personalPhoto:[],
        },
      ) :  this.setState(
        {
          nationalPhoto:[],
        }, 
      );
    }
  }

    //图片上传
    handleUpload(file,type,name,str){
      const { acUploadFile } = this.props;
      acUploadFile(file,name).then(res=>{
        if(res.success){
          console.log("handleUpload-res>>>",res)
          let fileFiles = [];
          fileFiles.push({ url: res.data.url, id: res.data.archiveId }) ;
          str == 'front' ?  
           this.setState({ personalPhoto:fileFiles}) : 
           this.setState({ nationalPhoto:fileFiles});

          Toast.hide();
          fileFiles = []
        
        }else{
          Toast.hide();
          Toast.info(res.message, 1);
        }
      })
    }

  handleImg(file){
    console.log(2,file)
    return new Promise((resolve, reject) => {
      let handleFile = file[file.length-1]
      let isLt2M = handleFile.file.size / 1024 / 1024 < 10; // 判定图片大小是否小于10MB
      if (!isLt2M) {
          reject('图片大小不得超过10MB');
      }
      let image = new Image(),
          resultBlob = "";
      image.src = URL.createObjectURL(handleFile.file);
      image.onload = () => {
          resultBlob = this.compressUpload(image);
          resolve(resultBlob);
      };
      image.onerror = () => {
          reject();
      };
    });
  }
   //压缩
   compressUpload(image){
    let canvas = document.createElement("canvas");
    let ctx = canvas.getContext("2d");
    let initSize = image.src.length;
    let { width } = image,
        { height } = image;
    canvas.width = width;
    canvas.height = height;
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    ctx.drawImage(image, 0, 0, width, height);
    let compressData = canvas.toDataURL("image/jpeg", 0.1);
    let blobImg = this.dataURItoBlob(compressData);
    return blobImg;
  }
  /* base64转Blob对象 */
  dataURItoBlob(data) {
    let byteString;
    if (data.split(",")[0].indexOf("base64") >= 0) {
        byteString = atob(data.split(",")[1]);
    } else {
        byteString = unescape(data.split(",")[1]);
    }
    let mimeString = data.split(",")[0].split(":")[1].split(";")[0];
    let ia = new Uint8Array(byteString.length);
    for (let i = 0; i < byteString.length; i += 1) {
        ia[i] = byteString.charCodeAt(i);
    }
    return new Blob([ia], { type: mimeString });
  }

  render() {
    const { list,personalPhoto ,nationalPhoto} = this.state;
    return (
      <div className="upload-card">
        <NavBar
          key={1}
          mode="light"
          icon={<Icon type="left" />}
          style={{ color: "black" }}
          onLeftClick={this.handleBack.bind(this)}
        >
          身份证上传
        </NavBar>
        <div className="container">
          <div className="card_front">
            <ImagePicker
              files={personalPhoto}
              accept="image/*"
              onChange={(value,type,index) =>{this.handleFilesonChange(value,type,index,'front')} }
              onImageClick={(index, fs) => console.log(index, fs)} 
              length="1"
              selectable={personalPhoto.length < 1}      
          />
         </div>
         <div className="card_back">
           <ImagePicker
            files={nationalPhoto}
            accept="image/*"
            onChange={(value,type,index) =>{this.handleFilesonChange(value,type,index,'back')} }
            onImageClick={(index, fs) => console.log(index, fs)}
            length="1"
            selectable={nationalPhoto.length < 1}     
           />
          </div>
          <p className="tips">请确保证件照片清晰完整</p>
          <div className="item-box">
            {list.map((item, index) => {
              return (
                <div className="item" key={index}>
                  <img src={item.img}></img>
                  <p>
                    <i className={["icon", item.icon ? "success" : "error"].join(" ")} />
                    {item.tip}
                  </p>
                </div>
              );
            })}
          </div>
        </div>
        <div className="next">
          <div className="btn" onClick={(e) => this.handleDetailClick(e)}>
            下一步
          </div>
        </div>
      </div>
    );
  }
}

const mapStateToProps = (state) => ({

});

const mapDispatchToProps = {
  acUploadFile:action.acUploadFile,
  acOcrResult:action.acOcrResult,
  acSetCardInfo:action.acSetCardInfo
};

export default connect(mapStateToProps,mapDispatchToProps)(UploadCard);
