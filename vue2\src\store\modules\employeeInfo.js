import fetch from '@/request/newFetch';
import fetchUpload from '@/request/fetchUpload';

const state = {
  selectedInfo: {},
  token: '',
  countrylist: {},
  citylist: {},
  provincelist: {},
  optionSource: '', // 来源
  groupRequired: [] // 各个分组是否已编辑必填
};

const mutations = {
  SET_SELECTED_INFO(state, payload) {
    state.selectedInfo = payload;
  },
  SET_LOGIN_TOKEN(state, payload) {
    state.token = payload;
  },
  SET_COUNTRY_LIST(state, payload) {
    state.countrylist = payload;
  },
  SET_CITY_LIST(state, payload) {
    state.citylist = payload;
  },
  SET_PROVINCE_LIST(state, payload) {
    state.provincelist = payload;
  },
  SET_OPTION_SOURCE(state, payload) {
    state.optionSource = payload;
  },
  SET_GROUP_REQUIRED(state, payload) {
    state.groupRequired = payload;
  }
};

const actions = {
  // 设置选择信息
  setSelectInfo({ commit }, payload) {
    commit('SET_SELECTED_INFO', payload);
  },

  // 下发短信
  async sendMsg({ commit }, param) {
    try {
      const res = await fetch.post(`/hrsaas-emp/sms/v1/sms-captcha`, param);
      return res;
    } catch (error) {
      throw error;
    }
  },

  // 验证短信
  async checkMsg({ commit }, param) {
    try {
      const res = await fetch.post(`/hrsaas-emp/sms/v1/sms-auth`, param);
      return res;
    } catch (error) {
      throw error;
    }
  },

  // 保存token
  saveToken({ commit }, payload) {
    commit('SET_LOGIN_TOKEN', payload);
  },

  // 登记详情列表
  async registDetail({ commit }, param) {
    try {
      const res = await fetch.get(`/hrsaas-emp/entry/v1/emp/query/registdetail`, param);
      return res;
    } catch (error) {
      throw error;
    }
  },

  // 员工详情操作
  async operateEmp({ commit }, param) {
    try {
      const res = await fetch.post(`/hrsaas-emp/empDetail/operateEmp`, param, { Source: "WECHAT_APPLET" });
      return res;
    } catch (error) {
      throw error;
    }
  },

  // 手机号校验
  async checkIdAndMobile({ commit }, param) {
    try {
      const res = await fetch.post(`/hrsaas-emp/empDetail/checkIdNoAndMobile`, param, { Source: "WECHAT_APPLET" });
      return res;
    } catch (error) {
      throw error;
    }
  },

  // 员工详情查询
  async getEmpDetail({ commit }, param) {
    try {
      const res = await fetch.post(`/hrsaas-emp/empDetail/getEmpDetail`, param, { 
        Source: "WECHAT_APPLET", 
        'Cache-Control': 'no-cache, no-store, must-revalidate' 
      });
      return res;
    } catch (error) {
      throw error;
    }
  },

  // 查询企业ID
  async getCompId({ commit }, param) {
    try {
      const res = await fetch.post(`/hrsaas-emp/empDetail/query/compId`, param, { Source: "WECHAT_APPLET" });
      return res;
    } catch (error) {
      throw error;
    }
  },

  // OCR识别
  async ocrCheck({ commit }, param) {
    try {
      const res = await fetch.post(`/hrsaas-emp/empDetail/ocrCheck`, param, { Source: "WECHAT_APPLET" });
      return res;
    } catch (error) {
      throw error;
    }
  },

  // 获取员工所在公司
  async getEmpTaxSubject({ commit }, param) {
    try {
      const res = await fetch.post(`/hrsaas-emp/empDetail/getEmpTaxSubject`, param, { Source: "WECHAT_APPLET" });
      return res;
    } catch (error) {
      throw error;
    }
  },

  // 上传文件
  async uploadFile({ commit }, { file, name }) {
    try {
      let formData = new FormData();
      formData.append("file", file, name);
      const res = await fetchUpload({
        url: `/hrsaas-emp/archive/upload`,
        method: 'post',
        data: formData
      });
      return res;
    } catch (error) {
      throw error;
    }
  },

  // 下载文件
  async download({ commit }, param) {
    try {
      const res = await fetch.down(`/hrsaas-emp/archive/download?archiveId=${param.archiveId}`);
      return res;
    } catch (error) {
      throw error;
    }
  },

  // 国际列表
  async getCountries({ commit }, params) {
    try {
      const res = await fetch.get('/hrsaas-salary/enterprise/employee/countries', params);
      commit('SET_COUNTRY_LIST', res.data);
      return res;
    } catch (error) {
      throw error;
    }
  },

  // 城市列表
  async getCity({ commit }, params) {
    try {
      const res = await fetch.get('/hrsaas-salary/insuredProject/manage/getCity', params);
      commit('SET_CITY_LIST', res.data);
      return res;
    } catch (error) {
      throw error;
    }
  },

  // 联动城市列表
  async getProvince({ commit }, params) {
    try {
      const res = await fetch.get('/hrsaas-emp/custom/v1/query/province', params);
      commit('SET_PROVINCE_LIST', res.data);
      return res;
    } catch (error) {
      throw error;
    }
  },

  // 获取url参数
  getUrlParams({ commit }, { name, str }) {
    const reg = new RegExp(`(^|&)${name}=([^&]*)(&|$)`);
    const r = str.substr(1).match(reg);
    if (r != null) return decodeURIComponent(r[2]);
    return null;
  },

  // 来源
  setSource({ commit }, params) {
    commit('SET_OPTION_SOURCE', params);
  },

  // 各个分组是否编辑保存必填字段
  setGroupRequired({ commit }, params) {
    commit('SET_GROUP_REQUIRED', params);
  }
};

const getters = {
  selectedInfo: state => state.selectedInfo,
  token: state => state.token,
  countrylist: state => state.countrylist,
  citylist: state => state.citylist,
  provincelist: state => state.provincelist,
  optionSource: state => state.optionSource,
  groupRequired: state => state.groupRequired
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
};
