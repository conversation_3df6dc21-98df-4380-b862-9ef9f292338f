import React, { Component } from "react";
import {  List, Button ,Toast} from "antd-mobile";
//import "./index.scss";
import { connect } from "react-redux";
import action from "../redux/action";

class CompList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      entryInfoList: [],//登记详情数据
      employeeDetailLableDto:this.props.employeeDetailLableDto//个人信息or岗位信息
    };
  }
  //获取入职登记列表信息
  handleRegistDetail(){
    const { acRegistDetail, token ,employeeDetailLableDto,compEmpId} = this.props;
    acRegistDetail({compEmpId}).then(res => {
      if (res.success) {
        this.setState({
          // entryInfoList: res.data.employeeDetailLableDto[employeeDetailLableDto].groupModels,
          entryInfoList: this.handleDeailList(res.data.employeeDetailLableDto),
          empId: res.data.empId,
          empRecordId:res.data.empRecordId
        },()=>{
          console.log(this.state.entryInfoList);
          this.handleCheckRequired();
        })
        this.props.handleEmpRecordId(res.data.empRecordId);//将用工id传给父组件
      }else{
        Toast.info(res.message);
      }
    })
  }
  handleDeailList(items){
    let arr = [];
    for(let i = 0;i<items.length;i++){
      if(items[i].lableCode!=="POST_INFO"){
        arr = [...arr,...this.handleListData(items[i])]
      }
    }
    return arr
  }
  //拼接lableCode
  handleListData(data){
    return data.groupModels.map((v,k)=>{
      v["def_lableCode"] = data.lableCode
      return v
    })
  }
  //检查基本信息模块必填字段是否填写
  handleCheckRequired(){
    const { entryInfoList } = this.state;
    const { acSetGroupRequired} = this.props; 
    let state = []
    entryInfoList.forEach(item=>{
      let groupCode = item.groupCode;
      let groupName = item.groupName;
      let baseInfo = item.models[0].fieldModels;
      let baseInfoResult = baseInfo.every((v)=>{
        if(v["isRequired"]&&!v["fieldValue"]){//任职信息 暂不校验
          return false
        }else{
          return true
        }
      })
      state.push({
        id:groupCode,
        name:groupName,
        isRequired:baseInfoResult,
      })
    })
    acSetGroupRequired(state)
    // const groupCode = entryInfoList[0].groupCode;//基本信息
    // const baseInfo = entryInfoList[0].models[0].fieldModels;
    // console.log(baseInfo)
    // let baseInfoResult = baseInfo.every((v)=>{
    //   if(v["isRequired"]&&!v["fieldValue"]){
    //     return false
    //   }else{
    //     return true
    //   }
    // })
    // acSetGroupRequired({[groupCode]:baseInfoResult})//记录各个分组是否编辑
  }
  handleSubmit(){
    Toast.info('敬请期待！');
  }

  componentDidMount() {
    this.handleRegistDetail();//获取入职登记列表信息
  }

  // 由目录页跳转到对应详情页
  handleClick (item){
    console.log(item)
    const groupName = item.groupName,
          groupCode = item.groupCode,
          addMultipleYn = item.addMultipleYn,//是否支持多条
          empId = this.state.empId,
          empRecordId = this.state.empRecordId,
          id = item.models[0].id;
    const {acGetCountries,acGetCity,acGetProvince} = this.props;
    let getCountry,getCity,getProvince;
    if(['BASIC_INFO','POSITION_INFO'].includes(groupCode)){//进入基本信息，任职信息前调取国籍/城市接口
      getCountry = acGetCountries()
      getCity = acGetCity()
      getProvince = acGetProvince()
      
    }
    Promise.all([getCountry, getCity,getProvince]).then((res)=>{
      console.log(res) 
      let list = item.models
      this.props.history.push({ pathname:'/entry-register/form',state:{list,groupName,groupCode,empId,id,addMultipleYn,empRecordId}});
    }).catch((error) => {
      console.log(error)
    })
  };

  render() {
    const { entryInfoList } = this.state;
    const Item = List.Item;
    return (
        <section className="section">
          <List>
            {
              entryInfoList.map((item,index) =>(
                <Item arrow="horizontal" onClick={(e)=>this.handleClick(item,e)} key={item.seqNo}>{item.groupName}</Item>
              ))
            }
          </List>
          <div style={{height:'80px'}}></div>
        </section>
    );
  }
}

const mapStateToProps = (state) => ({
  token: state.entryRegister.token,
});

const mapDispatchToProps = {
  acRegistDetail:action.acRegistDetail,//入职登记列表详情
  acGetCountries:action.acGetCountries,//国家列表
  acGetCity:action.acGetCity,//城市列表
  acGetProvince:action.acGetProvince,//城市联动列表
  acSetGroupRequired:action.acSetGroupRequired//各个分组是否编辑保存必填字段
};

export default connect(mapStateToProps, mapDispatchToProps)(CompList);

