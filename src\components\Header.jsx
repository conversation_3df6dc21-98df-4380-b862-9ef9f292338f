import React, { Component } from "react";
import { connect } from "react-redux";

class CompHeader extends Component {
  constructor(props) {
    super(props);
  }
  componentDidMount() {
    
  }
  render() {
    const style={
      textAlign: "center",
      height: "40px",
      lineHeight: "40px",
      background: "#ffffff",
      fontSize: "16px",
      fontFamily: "PingFangSC-Medium, PingFang SC",
      fontWeight: "500",
      color: "#383838",
    };
    return (
        <header className="comp-header" style={style}>{this.props.title}</header>
    );
  }
}

const mapStateToProps = (state) => ({

});

const mapDispatchToProps = {

};

export default connect(mapStateToProps, mapDispatchToProps)(CompHeader);

