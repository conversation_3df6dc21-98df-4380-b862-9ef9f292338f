import React, { Component } from "react";
import { connect } from "react-redux";
import { Button } from 'antd-mobile';

class CompFooter extends Component {
  constructor(props) {
    super(props);
  }
  componentDidMount() {
    //console.log(this.props)
  }
  render() {
    const footerStyle={
      height: "80px",
      width: "100%",
      position: "fixed",
      bottom: "0",
      zIndex:999,
    };
    const submitBtn={
      margin: "0 26px",
      borderRadius: "20px",
      height: "40px",
      lineHeight: "40px",
    };
    return (
        // <header className="comp-header" style={style}>{this.props.title}</header>
        <footer style={footerStyle}>
          {/* <Button type="primary" style={submitBtn}  onClick={()=>{this.submit(id,operation)}}>{this.props.text}</Button> */}
          <Button type="primary" style={submitBtn} onClick={this.props.fun.bind(this)}>{this.props.text}</Button>
        </footer>
    );
  }
}

const mapStateToProps = (state) => ({

});

const mapDispatchToProps = {

};

export default connect(mapStateToProps, mapDispatchToProps)(CompFooter);

