import * as AT from './actionTypes';
import fetch from 'request/newFetch';

// 小程序强制阅读文档协议渲染
const acGetConfigInfo = (params) => (dispatch) => {
    return fetch.post('olading-user/protocol/getProtocolLsit', params).then((res) => {
        dispatch({
            type: AT.SELCONFIGINFO,
            payload: res.data
        })
        return res;
    })
}

// 小程序强制阅读文档协议同意调用
const acConfirmRead = (params) => () => {
    return fetch.post('olading-user/protocol/confirmRead', params).then(res => res)
}

//获取url参数
const getUrlParams = (name, str) => ()=>{
    const reg = new RegExp(`(^|&)${ name}=([^&]*)(&|$)`);
    const r = str.substr(1).match(reg);
    if (r != null) return  decodeURIComponent(r[2]); return null;
}

export default {
    acGetConfigInfo,
    acConfirmRead,
    getUrlParams
}
