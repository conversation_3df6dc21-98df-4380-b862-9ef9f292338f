/* eslint-disable global-require */
/* eslint-disable no-undef */
/* eslint-disable no-return-assign */
/* eslint-disable max-len */
import Vue from 'vue';
import App from './App.vue';
import router from './router';
import store from './store';
import './assets/scss/base.scss';
import smoothscroll from 'smoothscroll-polyfill';

// Import Vant components
import Vant from 'vant';
import 'vant/lib/index.css';

Vue.use(Vant);

// Production error tracking
if (process.env.NODE_ENV === 'production' && __ENV_CONFIG__.BUILD_ENV === 'online') {
  const fundebug = require('fundebug-javascript');
  fundebug.apikey = 'e2a48d47e4a406ca9a9c5ed71c32656c7e59936af358971a6302f75d5f8a55c3';
}

Vue.config.productionTip = false;

// Global state monitoring
const updateCurrentState = () => {
  sessionStorage.currentState = JSON.stringify(store.state || {});
};

store.subscribe(() => updateCurrentState());

new Vue({
  router,
  store,
  render: h => h(App),
}).$mount('#app');

// Mobile keyboard handling
document.addEventListener("touchend", function(e) {
  if (e.target.tagName !== "INPUT") {
    document.activeElement.blur();
  }
}, false);

// Handle popstate events
window.addEventListener('popstate', function(e) {
  window.location.reload();
});
