import React from 'react';
import { Provider } from 'react-redux';
import Router from '../routes';
import { AppContainer } from 'react-hot-loader'
import ErrorBoundary from './ErrorBoundary'
import history from './history'

// const createApp = (store,history) => (
const createApp = (store) => (
  <AppContainer>
    <ErrorBoundary>
      <Provider store={store}>
        <Router history={history} />
      </Provider>
    </ErrorBoundary>
  </AppContainer>
);

export default createApp;
