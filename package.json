{"name": "hr-sass-h5", "version": "1.0.0", "description": "React 16.0", "main": "src/index.js", "scripts": {"dev": "cross-env NODE_ENV=development BUILD_CONFIG=local webpack-dev-server --config webpack.client.config.js --progress --colors", "dev:server": "npm run clean && cross-env NODE_ENV=production BUILD_CONFIG=localServer npm-run-all --parallel build:client-watch build:server-watch", "build:client-watch": "webpack --config webpack.client.config.js -p --progress --colors --watch", "build:server-watch": "webpack --config webpack.server.config.js -p --progress --colors --watch", "build": "webpack --config webpack.client.config.js -p --progress --colors", "build:full": "npm run clean && cross-env NODE_ENV=production BUILD_CONFIG=remo teServer npm-run-all --parallel build:client build:server", "build:client": "webpack --config webpack.client.config.js -p --progress --colors", "build:server": "webpack --config webpack.server.config.js -p --progress --colors", "clean": "rm -rf dist/", "lint": "eslint src", "test": "jest --coverage", "test:watch": "jest --watch --coverage"}, "author": "Vance_wang", "license": "MIT", "devDependencies": {"@babel/core": "^7.0.0", "@babel/plugin-proposal-class-properties": "^7.5.5", "@babel/plugin-syntax-dynamic-import": "^7.0.0", "@babel/preset-env": "^7.0.0", "@babel/preset-react": "^7.0.0", "@types/react": "^17.0.0", "autoprefixer": "9.1.3", "babel-eslint": "^9.0.0", "babel-loader": "^8.0.1", "babel-plugin-module-resolver": "^3.1.1", "copy-webpack-plugin": "^4.5.2", "cross-env": "^5.2.0", "css-loader": "^1.0.0", "eslint": "^5.5.0", "eslint-config-airbnb": "^17.1.0", "eslint-import-resolver-babel-module": "5.0.0-beta.1", "eslint-plugin-import": "^2.14.0", "eslint-plugin-jsx-a11y": "^6.1.1", "eslint-plugin-react": "^7.11.1", "file-loader": "^2.0.0", "html-webpack-plugin": "^3.2.0", "jest": "^23.5.0", "mini-css-extract-plugin": "^0.4.2", "node-sass": "^4.14.1", "npm-run-all": "^4.1.3", "post-compile-webpack-plugin": "^0.1.2", "postcss-loader": "^3.0.0", "react-input-groups": "^1.0.3", "rimraf": "^2.6.2", "sass-loader": "^7.1.0", "style-loader": "^0.23.0", "url-loader": "^1.1.1", "webpack": "^4.17.1", "webpack-cli": "^3.1.0", "webpack-dev-server": "^3.1.7", "webpack-manifest-plugin": "^2.0.3"}, "dependencies": {"@ant-design/icons": "^4.4.0", "antd-mobile": "^2.2.14", "axios": "^0.18.0", "babel-plugin-import": "^1.12.0", "connected-react-router": "^4.4.1", "copy-to-clipboard": "^3.3.1", "dayjs": "^1.10.7", "friendly-errors-webpack-plugin": "^1.7.0", "fundebug-javascript": "^1.9.0", "history": "^4.6.3", "less": "^3.9.0", "less-loader": "^5.0.0", "lodash": "^4.17.10", "old-env-map": "^0.0.5", "old-fetch": "^0.0.17", "prop-types": "^15.6.2", "rc-form": "^2.4.12", "react": "^16.4.2", "react-dom": "^16.4.2", "react-fastclick": "^3.0.2", "react-hot-loader": "4.5.3", "react-json-pretty": "^2.1.0", "react-loadable": "^5.5.0", "react-read-pdf": "^2.0.9", "react-pdf": "^5.3.0", "react-redux": "^5.0.7", "react-router-dom": "^4.3.1", "react-wx-images-viewer": "^1.0.6", "redux": "^4.0.0", "redux-logger": "^3.0.6", "redux-persist": "^5.10.0", "redux-thunk": "^2.3.0", "smoothscroll-polyfill": "^0.4.4", "uglifyjs-webpack-plugin": "^2.2.0", "webpack-bundle-analyzer": "^3.4.1", "webpack-inline-manifest-plugin": "^4.0.1", "weixin-js-sdk": "^1.4.0-test"}, "theme": {"brand-primary": "#2C7CFF", "brand-wait": "#2C7CFF", "brand-primary-tap": "#5b9d65", "color-text-base": "#333"}, "jest": {"coverageDirectory": "./coverage/", "coveragePathIgnorePatterns": ["<rootDir>/scripts/*"], "moduleNameMapper": {}, "modulePaths": ["<rootDir>/src/"], "resetMocks": true, "setupFiles": [], "testPathIgnorePatterns": ["/node_modules/", "<rootDir>/scripts/*"]}}