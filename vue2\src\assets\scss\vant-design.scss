// Vant UI 组件样式覆盖
// 主题色配置
:root {
  --van-primary-color: #2C7CFF;
  --van-success-color: #2C7CFF;
  --van-warning-color: #ff976a;
  --van-danger-color: #ee0a24;
  --van-text-color: #383838;
  --van-text-color-2: #969799;
  --van-text-color-3: #c8c9cc;
  --van-background-color: #f7f8fa;
  --van-background-color-light: #fafafa;
}

// 覆盖Vant组件样式以匹配原Ant Design Mobile样式
.van-button {
  &--primary {
    background-color: #2C7CFF;
    border-color: #2C7CFF;
    
    &:active {
      background-color: #5b9d65;
      border-color: #5b9d65;
    }
  }
}

.van-nav-bar {
  background-color: #ffffff;
  
  .van-nav-bar__title {
    font-size: 16px;
    font-family: "PingFangSC-Medium, PingFang SC";
    font-weight: 500;
    color: #383838;
  }
}

.van-tabs {
  .van-tab {
    font-size: 16px;
    color: #383838;
    
    &--active {
      color: #2C7CFF;
    }
  }
  
  .van-tabs__line {
    background-color: #2C7CFF;
  }
}

.van-list {
  .van-cell {
    padding: 12px 16px;
    font-size: 16px;
    
    .van-cell__title {
      color: #383838;
    }
    
    .van-cell__value {
      color: #969799;
    }
  }
}

.van-form {
  .van-field {
    padding: 12px 16px;
    
    .van-field__label {
      color: #383838;
      font-size: 16px;
    }
    
    .van-field__control {
      font-size: 16px;
      color: #383838;
    }
  }
}

.van-toast {
  font-size: 14px;
}

.van-dialog {
  .van-dialog__header {
    font-size: 16px;
    font-weight: 500;
    color: #383838;
  }
  
  .van-dialog__message {
    font-size: 14px;
    color: #383838;
  }
}
