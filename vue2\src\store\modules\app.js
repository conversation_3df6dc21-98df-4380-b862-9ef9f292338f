const state = {
  loading: false,
  title: '',
  userInfo: null,
  token: null
};

const mutations = {
  SET_LOADING(state, loading) {
    state.loading = loading;
  },
  SET_TITLE(state, title) {
    state.title = title;
  },
  SET_USER_INFO(state, userInfo) {
    state.userInfo = userInfo;
  },
  SET_TOKEN(state, token) {
    state.token = token;
    if (token) {
      sessionStorage.setItem('token', token);
    } else {
      sessionStorage.removeItem('token');
    }
  }
};

const actions = {
  setLoading({ commit }, loading) {
    commit('SET_LOADING', loading);
  },
  setTitle({ commit }, title) {
    commit('SET_TITLE', title);
  },
  setUserInfo({ commit }, userInfo) {
    commit('SET_USER_INFO', userInfo);
  },
  setToken({ commit }, token) {
    commit('SET_TOKEN', token);
  },
  getUrlParams({ commit }, { key, search }) {
    const urlParams = new URLSearchParams(search);
    return urlParams.get(key);
  }
};

const getters = {
  loading: state => state.loading,
  title: state => state.title,
  userInfo: state => state.userInfo,
  token: state => state.token || sessionStorage.getItem('token')
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
};
