import React, { Component } from "react";
import {withRouter} from "react-router-dom";
import { List, Button, Toast, InputItem, DatePicker, Picker, ImagePicker , NavBar ,Icon} from "antd-mobile";
import "./scss/DefForm.scss";
import { createForm } from "rc-form";
import { connect } from "react-redux";
import action from "modules/entryRegister/redux/action";
import {validPhoneReg, valideEmail,validIdNo} from "utils/validReg";
import { checkMobileField, checkEmailField } from "utils/constData";
import Footer from "components/Footer";
import Upload from "components/UpLoad";


class DefForm extends Component {
  constructor(props) {
    super(props);
    this.state = {
      list:[],//详情
      formStart: {},//初始化表单数据
      form: {},//待提交表单数据
      checkform:{},//校验字段
      files: {},//附件信息
      groupName:'',//分组名称
      groupCode:'',//分组编码
      addMultipleYn:false,//是否支持多条
      empId:'',//企业员工ID
      id:'',//操作ID
      operation:{//操作类型
        ADD:"ADD",
        EDIT:"EDIT",
        DELETE:"DELETE"
      },//操作名
      isRequired:{},
      isChecked:{},//手机格式校验
      isCheckedEmail:{},//邮箱格式校验
      isCheckedNum:{},//数字格式校验
      date:{},//存放日期值
      isSave:false,//是否保存成功
      isChange:true,//是否编辑过
      isDisable:false,//页面是否禁止编辑 true:全屏禁止操作
      empRecordId:""//用工记录id,
    };
  }
  //初始化数据
  componentDidMount() {
    this.props.onRef(this)//向父组件抛出方法
    const { groupName,groupCode,empId,addMultipleYn,def_lableCode,empRecordId } = this.props.data;
    const isDisable = def_lableCode == "POST_INFO" ? true : false;//POST_INFO:岗位信息-岗位信息中的分组不允许编辑
    const list = this.props.list;
    //const id = this.props.id;
    const id = 0;
    let form = {};
    let files = {};
    let date = {};
    list.map((item,index) => {
      form[item.fieldCode] = "";
      if (item.fieldType === 'OPTION') {
        this.handleOption(item)//初始化option
      }
      if(item.fieldType === 'FILE'){
        //files[item.fieldCode] = files.fieldValue||[];//初始化附件
        files[item.fieldCode] = [];//初始化附件
        form[item.fieldCode] = [];
      }
      if(item.fieldType === 'DATE'){
        //date[item.fieldCode] = (item.fieldValue||"") == "" ? "" : new Date(item.fieldValue);//初始化时间格式
        date[item.fieldCode] = "";//初始化时间格式
      }
    });
    let formStart = JSON.parse(JSON.stringify(form))
    this.setState({form,files,date,list,groupName,groupCode,empId,id,addMultipleYn,formStart,isDisable,empRecordId});
  }

  //对list里面type是option的数据处理
  handleOption (item) {
    const { selectedInfo,countrylist,citylist } = this.props;
    let optionArr = [],
        cols = 1;
    switch(item.fieldCode){
      case "country" : 
        countrylist.map((it,index)=>{
          optionArr.push({
            label: it,
            value: it,
          });
        })
        cols = 1;
        break;
      case "householdCountry" : 
        citylist.map((it,index)=>{
          optionArr.push({
            label: it.dictName,
            value: it.dictCode,
          });
        })
        cols = 1;
        break;
      default : 
        item.options.map((it) => {
          optionArr.push({
            label: it.optionEnumName,
            value: it.optionEnumCode,
            enableYn: it.enableYn,
          });
        });
        cols = 1;
        break;
    }
    item.optionList = optionArr;
    item.cols = cols;
    return item;
  }
  //校验某字段是否存在
  handleCheckOwnProp(item){
    return this.state.form.hasOwnProperty(item)
  }
  //证件号码格式校验
  handleCheckIdNo(){
    if(this.handleCheckOwnProp('idNo')&&this.handleCheckOwnProp('idType')){
      const { idNo,idType } = this.state.form;
      const resdata = validIdNo(idNo,idType);
      console.log("已校验证件号手机号")
      if(!resdata.boo) Toast.info(resdata.data);
      return resdata.boo;
    }else{
      console.log("未校验证件号手机号")
      return true;
    }
  }

  //手机号格式校验
  handleCheckMobile(){
    const {form ,list ,groupCode} = this.state;
    const checkField = checkMobileField[groupCode]||[];
    let newisChecked = {},
        boo = true;
    checkField.map((item,index)=>{
      if(this.handleCheckOwnProp(item)){
        let resdata = validPhoneReg(form[item]);
        resdata.boo ? (newisChecked[item] = false) : (newisChecked[item] = true)&&(boo=false)
      }
    })
    newisChecked = Object.assign(this.state.isChecked,newisChecked);
    this.setState({
      isChecked:newisChecked,
    });
    console.log("已校验手机格式")
    return boo
  }

  //邮箱格式校验
  handleCheckEmail(){
    const {form ,list ,groupCode} = this.state;
    const checkField = checkEmailField[groupCode]||[];
    let newisCheckedEmail = {},
        boo = true;
    checkField.map((item,index)=>{
      if(this.handleCheckOwnProp(item)){
        let resdata = valideEmail(form[item]);
        resdata.boo ? (newisCheckedEmail[item] = false) : (newisCheckedEmail[item] = true)&&(boo=false)
      }
    })
    newisCheckedEmail = Object.assign(this.state.isCheckedEmail,newisCheckedEmail);
    this.setState({
      isCheckedEmail:newisCheckedEmail,
    });
    console.log("已校验邮箱格式")
    return boo
  }
  //数字格式校验
  handleCheckNumber(){
    const {form ,list} = this.state;
    let newisCheckedNum = {},
        boo = true;
    list.map((item,index)=>{
      if(item.fieldType==="NUMBER"){
        this.handleReg(form[item.fieldCode],item["length"]) || form[item.fieldCode] == "" ?
        (newisCheckedNum[item.fieldCode] = false) :
        (newisCheckedNum[item.fieldCode] = true)&&(boo=false)
      }
    })
    newisCheckedNum = Object.assign(this.state.isCheckedNum,newisCheckedNum);
    this.setState({
      isCheckedNum:newisCheckedNum,
    });
    console.log("已校数字格式")
    return boo
  }

  //失去焦点：数据校验 -证件号+手机号 -必填项
  checkIdCard(v,type,isrequired){
    const { idNo, mobile, idType } = this.state.form;
    const { empId } = this.state;
    const { acCheckIdAndMobile} = this.props;
    if ((type === "idNo" || type === "mobile") && idNo !== "" && mobile !== "" && idType !== ""){
      acCheckIdAndMobile({
        compEmpId:empId,
        // compId:compId,
        idNo: idNo,
        idType: idType,
        mobile: mobile,
      }).then((res)=>{
        if(res.success){
          if(Object.keys(res.data).length!==0){
            let newForm = {},
                newDate = {};
            newForm = Object.assign({}, this.state.form, { ["empAge"]: res.data.age,["birthdayDate"]: res.data.birthday,["birthday"]: res.data.birthday,["empSex"]: res.data.sex })
            newDate = Object.assign({}, this.state.date, { ["birthdayDate"]: new Date(res.data.birthday),["birthday"]: new Date(res.data.birthday) })
            this.setState({form:newForm,date:newDate}, ()=>{
              console.log("自动计算成功")
            })
          }
        }else{
          Toast.info(res.message);
        }
      });
    }
    if(isrequired){
      this.setState({
        isRequired:Object.assign(this.state.isRequired,{[type]:v!=="" ? false : true})
      },()=>{
        console.log("校验必填字段："+type)
      });
    }
  }
  //失去焦点：数据校验 -小数点个数
  checkNumber(v,type,isrequired,numLength) {
    let val = Number(v).toFixed(numLength);
    let newForm = Object.assign({}, this.state.form, {[type]:val})
    this.setState({
      form:newForm,
    });
    // this.setState({
    //   isCheckedNum:Object.assign(this.state.isCheckedNum,{[type]: this.handleReg(v,numLength)||v=='' ? false : true})
    // },()=>{
    //   console.log("校验数字字段："+type)
    // });
  }
  //双向绑定
  handleChange(val,formIndex,type,isrequired) {
    console.log(val,formIndex)
    let newForm = {},
        newDate = {};
    switch (type) {
      case "OPTION":
        newForm = Object.assign({}, this.state.form, { [formIndex]: val.join('') });
        this.setState({form:newForm}, ()=>{
          this.handleCheckChange()
        })
      break;
      case "NUMBER" :
        newForm = Object.assign({}, this.state.form, { [formIndex]:  val})
        this.setState({form:newForm}, ()=>{
          this.handleCheckChange()
        })
      break;
      case "DATE" :
        newForm = Object.assign({}, this.state.form, { [formIndex]: this.handleDate(val) })
        newDate = Object.assign({}, this.state.date, { [formIndex]: val })
        this.setState({form:newForm,date:newDate}, ()=>{
          this.handleCheckChange()
        })
      break;
      default :
        newForm = Object.assign({}, this.state.form, { [formIndex]: val })
        this.setState({form:newForm}, ()=>{
          this.handleCheckChange()
        })
    }
    if(isrequired){
      this.setState({
        isRequired:Object.assign(this.state.isRequired,{[formIndex]:val!=="" ? false : true})
      },()=>{
        console.log("校验必填字段："+formIndex)
      });
    }
  }
  //表单编辑与否
  handleCheckChange(){
    this.setState({isSave:false,isChange:true},()=>{
      this.props.onRef(this)//向父组件抛出方法-->父组件统一判断子组件是否都编辑过
    })
    // if(JSON.stringify(this.state.form)!==JSON.stringify(this.state.formStart)){
    //   this.setState({isSave:false,isChange:true},()=>{
    //     this.props.onRef(this)//向父组件抛出方法-->父组件统一判断子组件是否都编辑过
    //   })
    // }else{
    //   this.setState({isSave:true,isChange:false},()=>{
    //     this.props.onRef(this)//向父组件抛出方法-->父组件统一判断子组件是否都编辑过
    //   })
    // }
  }
  //时间格式转换 ->2020-1-1
  handleDate(value){
    const newDate = value.getFullYear() + '-' + (value.getMonth() + 1 +'').padStart(2,0) + '-' + (value.getDate()+'').padStart(2,0);
    return newDate
  }
  //数字格式正则校验
  handleReg(value,numLength){
    let reg = numLength == "0" ? new RegExp(`^([0-9]{1}\\d*)$`) : new RegExp(`^(([0-9]{1}\\d*)|(0{1}))(\\.\\d{${numLength}})$`)
    return reg.test(value)
  }
  

  //back
  handleBack () {
    //this.props.history.push({ pathname:'/entry-register/detail'});
    window.history.back(-1)
  }
  //提交前校验必填
  handleCheck(){
    const {form ,list} = this.state;
    let newisRequired = {},
        boo = true;//true -> 校验通过；false ->校验失败
    list.map((item,index)=>{
      if(item.isRequired){
        form[item.fieldCode] == "" ?
        (newisRequired[item.fieldCode] = true)&&(boo=false) :
        (newisRequired[item.fieldCode] = false)
      }
    })
    newisRequired = Object.assign(this.state.isRequired,newisRequired);
    this.setState({
      isRequired:newisRequired,
    });
    console.log("已校验必填")
    return boo
  }
  //数据删除-保存
  handleDelete(){
    const {id, groupCode,empId,empRecordId } = this.state;
    const { acOperateEmp ,optionSource} = this.props;
    let data = {
      id,
      empId,
      groupCode,
      param: {},
      empRecordId,
      operation:"DELETE",
      optionSource//员工详情-修改操作-新增参数
    };
    console.log(data)
    acOperateEmp(data).then(res => {
      if(res.success){
        this.setState({isSave:true,id:res.data.id},()=>{
          this.props.onRef(this)//向父组件抛出方法-->父组件统一判断子组件是否都保存成功
        })
      }else{
        Toast.info("删除失败");
      }
    })
  }
  //提交
  submit (){
    const {id,operation, groupCode,empId,empRecordId } = this.state;
    if(!this.handleCheck()){//提交前校验必填
      return;
    }
    // if(!this.handleCheckNumber()){//提交前校验数字格式
    //   return
    // }
    if(!this.handleCheckMobile()){//提交前校验手机号格式
      return
    }
    if(!this.handleCheckEmail()){//提交前校验邮箱格式
      return
    }
    if( groupCode === "BASIC_INFO"){//基本信息校验格式
      if(!this.handleCheckIdNo()){//提交前校验身份证件信息格式
        return
      }
    }
    
    const { acOperateEmp ,optionSource} = this.props;
    const { form,addMultipleYn } = this.state;
    const _operation = !id ? operation["ADD"] : operation["EDIT"];
    const _id = id||0;
    let data = {
      id:_id,
      empId,
      groupCode,
      empRecordId,
      param: form,
      operation:_operation,
      optionSource//员工详情-修改操作-新增参数
    };
    console.log(data)
    acOperateEmp(data).then(res => {
      if(res.success){
        this.setState({isSave:true,id:res.data.id,formStart:form,isChange:false},()=>{
          this.props.onRef(this)//向父组件抛出方法-->父组件统一判断子组件是否都保存成功
          if(!addMultipleYn){
            this.handleBack()
          }
        })
      }else{
        Toast.info("保存失败");
      }
    })
  }
  /*
  * res:上传成功附件
  * fieldCode:表单id
  */
  //图片上传返回结果
  handleuploadResult(res,fieldCode){
    const { url } = res.data;
    const fileFormArr = this.state.form[fieldCode];
    const fileFilesArr = this.state.files[fieldCode];
    fileFormArr.push(res.data.archiveId)
    if(url.slice(-3)=="pdf"){
      fileFilesArr.push({url:'static/fujian.webp',id:res.data.archiveId,newUrl:res.data.url})
    }else{
      fileFilesArr.push({url:res.data.url,id:res.data.archiveId})
    }
    // fileFilesArr.push({url:res.data.url,id:res.data.archiveId})
    
    let newFilesForm = '',
        newFiles = '';
    newFilesForm = Object.assign(this.state.form,{[fieldCode]: fileFormArr});
    newFiles = Object.assign(this.state.files,{[fieldCode]: fileFilesArr});

    this.setState({
      form:newFilesForm,
      files:newFiles
    },()=>{
      Toast.hide();
      this.handleCheckChange()
    });
  }
  /*
  * res:剩余附件
  * fieldCode:表单id
  * fileIndex:删除附件索引
  */
  //图片移除返回结果
  handleremoveResult(res,fieldCode,fileIndex){
    const fileFormArr = this.state.form[fieldCode];
    const fileFilesArr = this.state.files[fieldCode];

    const newfileFormArr = fileFormArr.filter((value,index)=>{
      //return value !== res.id
      return fileIndex !== index
    })
    const newfileFilesArr = fileFilesArr.filter((value,index)=>{
      //return value.id !== res.id
      return fileIndex !== index
    })
    let newFilesForm = '',
        newFiles = '';
    newFilesForm = Object.assign(this.state.form,{[fieldCode]: newfileFormArr});
    newFiles = Object.assign(this.state.files,{[fieldCode]: newfileFilesArr});
    this.setState({
      form:newFilesForm,
      files:newFiles
    },()=>{
      this.handleCheckChange()
    });
  }


  renderItemByType(item) {
    const { form, files,date,isDisable,isSave } = this.state;
    const minDate = new Date(1949,10,1,0,0,0);
    const maxDate = new Date();
    
    switch (item.fieldType) {
      case "TEXT":
        return (
          <InputItem
            clear //清除功能
            name={item.fieldCode}
            value={form[item.fieldCode]}
            onChange={(value)=>{this.handleChange(value,item.fieldCode,"TEXT")}}
            defaultValue={form[item.fieldCode]} //默认值
            placeholder={item.fieldRemark}
            disabled={isDisable ? isDisable : !item.isEdit} //是否禁用
            onBlur={(v)=>{this.checkIdCard(v,item.fieldCode,item.isRequired)}}
            maxLength={item["length"]}//最大长度限制
          ></InputItem>
        );
        //}
      case "NUMBER":
        return (
          <InputItem
            type="money"
            moneyKeyboardAlign="left"
            clear//清除功能
            name={item.fieldCode}
            value={form[item.fieldCode]}
            onChange={(value)=>{this.handleChange(value,item.fieldCode,"NUMBER")}}
            defaultValue={form[item.fieldCode]}//默认值
            placeholder={item.fieldRemark}
            disabled={isDisable ? isDisable : !item.isEdit} //是否禁用
            onBlur={(v)=>{this.checkNumber(v,item.fieldCode,item.isRequired,item["length"])}}
          ></InputItem>
        );
      case "DATE":
        return (
          <DatePicker
            minDate={minDate}
            maxDate={maxDate}
            mode="date"
            extra={item.fieldRemark}
            value={date[item.fieldCode]}
            onChange={(value)=>{this.handleChange(value,item.fieldCode,"DATE",item.isRequired)}}
            placeholder={item.fieldRemark}
            disabled={isDisable ? isDisable : !item.isEdit} //是否禁用
          >
            <List.Item arrow={isDisable ? "empty" : "horizontal"}></List.Item>
          </DatePicker>
        );
      case "OPTION":
        return (
          <Picker
            data={item.optionList}//数据源{[value:'',label:'']}
            cols={1}
            extra="请选择"
            value={[form[item.fieldCode]]}
            //format={(labels) => { return labels.join(',');}}
            onChange={(value)=>{this.handleChange(value,item.fieldCode,"OPTION",item.isRequired)}}
            disabled={isDisable ? isDisable : !item.isEdit} //是否禁用
          >
            <List.Item arrow={isDisable ? "empty" : "horizontal"}></List.Item>
          </Picker>
        );
      case "FILE":
        return (
          <div className="file-box">
            <Upload
              files={files[item.fieldCode]}
              fieldCode={item.fieldCode}
              uploadResult={this.handleuploadResult.bind(this)}//上传结果
              removeResult={this.handleremoveResult.bind(this)}//移除结果
              disabled={isDisable ? isDisable : !item.isEdit}
              isDisable={isDisable ? isDisable : !item.isEdit}//删除按钮显示隐藏
              isSave={isSave}
            >
            </Upload>
          </div>
        );
      default:
        return null;
    }
  }
  render() {
    const { list, form ,groupName,groupCode,empId,id,operation,isRequired,isChecked,isCheckedEmail,isCheckedNum,isDisable} = this.state;
    return (
      <div className="entry-form">
        <List>
          {list.map((item, index) => (
            <List.Item key={index}>
              <div>
                {item.isRequired ? <span className="required">*</span> : null}
                <span className="label-name" style={(!item.isEdit || isDisable)? {color:"#a5a5a5"}:null}>{item.fieldName}</span>
              </div>
              {this.renderItemByType(item)}
              {item.isRequired && isRequired[item.fieldCode] ? <p className="requiredNode">{item.fieldName}不允许为空</p> : null}
              {isCheckedNum[item.fieldCode] ? <p className="requiredNode">小数位数必须{item.length}位</p> : null}
              {
                (checkMobileField[groupCode] && checkMobileField[groupCode].includes(item["fieldCode"]))&& isChecked[item.fieldCode] ? 
                <p className="requiredNode">请检查电话格式</p> : null
              }
              {
                (checkEmailField[groupCode] && checkEmailField[groupCode].includes(item["fieldCode"]))&& isCheckedEmail[item.fieldCode] ? 
                <p className="requiredNode">请检查邮箱格式</p> : null
              }
            </List.Item>
          ))}
        </List>
      </div>
    );
  }
}

const mapStateToProps = (state) => ({
  selectedInfo: state.entryRegister.selectedInfo,
  countrylist: state.entryRegister.countrylist,
  citylist: state.entryRegister.citylist,
  optionSource: state.entryRegister.optionSource,
});

const mapDispatchToProps = {
  acRegistDetail: action.acRegistDetail,
  acOperateEmp:action.acOperateEmp,
  acCheckIdAndMobile:action.acCheckIdAndMobile,
  acUploadFile:action.acUploadFile
};

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(DefForm));
