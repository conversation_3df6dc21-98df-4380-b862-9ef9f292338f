import React, { Component } from "react";
import { Tabs, List, Button ,Toast,Modal} from "antd-mobile";
import "./index.scss";
import { connect } from "react-redux";
import action from "../../redux/action";
import CompList from "../../components/List";
import Footer from "components/Footer";

const alert = Modal.alert;
class EntryDetail extends Component {
  constructor(props) {
    super(props);
    this.state = {
      empRecordId:''//用工id
    };
  }
  //提交HR
  handleSubmit(){
    console.log(this.props)
    const { acUpdateReg ,groupRequired} = this.props;
    const { empRecordId } = this.state;
    // const _groupRequired = groupRequired||[];
    for(let i in groupRequired){
      let item = groupRequired[i]
      console.log('item',item)
      if(!item.isRequired && item.id !='WORK_INFO'){
        Toast.info(`请填写${item.name}必填项`, 1, () => {
          return
        });
        return
      }
    }
    alert('提示', '请检查信息是否填写正确，提交后将无法修改', [
      { text: '取消', onPress: () => console.log('提交取消') },
      { text: '确定', onPress: () => {
        acUpdateReg({empRecordId}).then((res)=>{
          if(res.success){
            Toast.info('提交成功，请等待HR审核确认', 3, () => {
              sessionStorage.setItem('token','');
              this.props.history.push({ pathname:'/',search: '?isOnDuty=true&updateTime='+res.data.updateTime});
            });
          }
        })
      }}
    ])
    // if(!_groupRequired || !_groupRequired["BASIC_INFO"]){
    //   Toast.info('请填写基本信息', 1, () => {
    //     return
    //   });
    // }else{
    //   alert('提示', '请检查信息是否填写正确，提交后将无法修改', [
    //     { text: '取消', onPress: () => console.log('提交取消') },
    //     { text: '确定', onPress: () => {
    //       acUpdateReg({empRecordId}).then((res)=>{
    //         if(res.success){
    //           Toast.info('提交成功，请等待HR审核确认', 3, () => {
    //             sessionStorage.setItem('token','');
    //             this.props.history.push({ pathname:'/index',search: '?isOnDuty=true&updateTime='+res.data.updateTime});
    //           });
    //         }
    //       })
    //     }}
    //   ])
    // }
  }
  //获取用工id
  handleEmpRecordId(val){
    console.log('empRecordId:'+val)
    this.setState({
      empRecordId:val
    });
  }

  componentDidMount() {

  }

  render() {
    const { entryInfoList, tabs } = this.state;
    const Item = List.Item;
    const compEmpId = this.props.location.state.compEmpId;
    return (
      <div className="entry-detail">
        <header className="title">入职登记</header>
        <nav className= "nav">温馨提示：此个人信息为您主动提交至该企业，且内容真实无误</nav>
        <CompList history={this.props.history} employeeDetailLableDto={0} compEmpId={compEmpId} handleEmpRecordId={this.handleEmpRecordId.bind(this)}/>
        <Footer fun={this.handleSubmit.bind(this)} text={'提交给HR'} />
      </div>
    );
  }
}

const mapStateToProps = (state) => ({
  token: state.entryRegister.token,
  groupRequired: state.entryRegister.groupRequired,
});

const mapDispatchToProps = {
  acRegistDetail:action.acRegistDetail,//入职登记列表详情
  acGetCountries:action.acGetCountries,//国家列表
  acGetCity:action.acGetCity,//城市列表
  acUpdateReg:action.acUpdateReg,//登记表提交
};

export default connect(mapStateToProps, mapDispatchToProps)(EntryDetail);
