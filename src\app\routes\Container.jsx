import React, { Component } from 'react';
import { Route, Switch , BrowserRouter ,useHistory} from 'react-router-dom';
import map from 'lodash/map';
import routerList from './routerList';
import NotFound from 'components/NotFound';


class IndexPage extends Component {
  componentDidMount() {
    this.updateTitle(this.props);
  }

  componentWillUpdate(nextProps) {
    this.updateTitle(nextProps);
  }

  updateTitle = (props) => {
    routerList.forEach(route => {
      if (route.path === props.location.pathname) {
        document.title = route.title || document.title || '';
      }
    })
  }
  render() {
    return (
      // <BrowserRouter basename="/gd/hrsaas/h5-hrsaas/">
      <BrowserRouter basename={window.env.baseRouter}>
      <div className="container">
        {/*<h1>你好！这里放公共内容！</h1>*/}
        <Switch>
          {map(routerList, (route, idx) => (
            <Route key={idx} {...route} />
          ))}
          <Route path="*" component={NotFound} />
        </Switch>
      </div>
      </BrowserRouter>
    )
  }
}
export default IndexPage
