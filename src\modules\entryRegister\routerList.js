import Loadable from "react-loadable";
import Loading from "components/Loading";

const Index = Loadable({ loader: () => import("./"), loading: Loading });
const Protocol = Loadable({ loader: () => import("./protocol"), loading: Loading });
const EntryDetail = Loadable({ loader: () => import("./pages/entryDetail"), loading: Loading });
const EntryForm = Loadable({ loader: () => import("./pages/entryForm"), loading: Loading });
const UploadCard = Loadable({ loader: () => import("./pages/uploadCard"), loading: Loading });
const UploadDetail = Loadable({ loader: () => import("./pages/uploadDetail/"), loading: Loading });

const routerList = [
  {
    path: "/index",
    component: Index,
    title: "入职登记",
  },
  {
    path: "/protocol",
    component: Protocol,
    title: "用户服务协议",
  },
  {
    path: "/form",
    component: EntryForm,
  },
  {
    path: "/detail",
    component: EntryDetail,
    titlel: "入职登记",
  },
  {
    path: "/card",
    component: UploadCard,
    titlel: "身份证上传",
  },
  {
    path: "/uploadDetail",
    component: UploadDetail,
    titlel: "身份基本信息",
  },
];
// 需要指定父级路由
Array.from(routerList, (rt) => (rt.path = "/entry-register" + rt.path));
export default routerList;
