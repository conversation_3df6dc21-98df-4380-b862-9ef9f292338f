<template>
  <div class="employee-info">
    <Detail />
  </div>
</template>

<script>
import { mapActions } from 'vuex';
import Detail from './pages/detail.vue';

export default {
  name: 'EmployeeInfo',
  components: {
    Detail
  },
  data() {
    return {
      empId: ''
    };
  },
  async mounted() {
    await this.handleGetUrlMsg(); // 获取小程序传参
    this.handleRecord();
  },
  methods: {
    ...mapActions('employeeInfo', ['getUrlParams']),
    
    // 获取小程序传参
    handleGetUrlMsg() {
      const token = this.getUrlParams({
        name: 'token',
        str: this.$route.query.token ? `?token=${this.$route.query.token}` : window.location.search
      });
      
      if (!sessionStorage.getItem("token")) {
        sessionStorage.setItem("token", token);
        this.empId = '';
      }
    },

    // 记录操作
    handleRecord() {
      const url = window.env.domain + '/api/olading-user/user/operate/record?operateType=EMPLOYEE_INFO';
      const token = sessionStorage.getItem("token");
      
      fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': token || "",
          'Content-Type': 'application/json'
        },
      })
      .then(res => res.json())
      .catch(error => console.error('Error:', error))
      .then(response => console.log('Success:', response));
    }
  }
};
</script>

<style lang="scss" scoped>
.employee-info {
  width: 100%;
  height: 100%;
}
</style>
