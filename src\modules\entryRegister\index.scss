@import '../../assets/scss/helpers';
.login, .onduty {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 0 28px;
  background-color: #ffffff;
  // .title {
  //   text-align: center;
  //   height: 40px;
  //   line-height: 40px;
  //   background: #ffffff;
  //   font-size: 16px;
  //   font-family: PingFangSC-Medium, PingFang SC;
  //   font-weight: 500;
  //   color: #383838;
  // }
  .logo {
    height: 250px;
    box-sizing: border-box;
    padding-top: 50px;
    background-color: ping;
    background: url('../../assets/images/iphone.png') center 50px no-repeat;
    background-size: 155px 156px;
  }
  .content {
    margin: 0 auto;

    .tip {
        height: 16px;
        font-size: 15px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #71788F;
        line-height: 16px;
        padding-bottom:20px;
        text-align: center;
    }
    .iphone {
      display:flex;
      height: 45px;
      line-height: 45px;
      background: #f7f7f7;
      border-radius: 24px;
      margin: 0 auto;
      padding-right: 32px;
      // background: url('../../assets/images/Icon-shouji.png') 12px center
      //   no-repeat;
        p {
        width: 32px;
        height: 45px;
        text-align: center;
        background: url('../../assets/images/Icon-shouji.png') 10px center
            no-repeat;
        }
        input {
            border: none;
            height: 45px;
            width: 100%;
            background: #f7f7f7;
        }
        input::-webkit-input-placeholder{
            width: 105px;
            height: 16px;
            font-size: 15px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #BBC2D8;
            line-height: 16px;
        }
    }
    .code {
      
      margin-top: 20px;
      display: flex;
      height: 45px;
      line-height: 45px;
      position: relative;
      justify-content: space-between;
      height: 45px;

      .left {
        display:flex;
        width:100%;
        background-color: #f7f7f7;
        border-radius: 24px;
        padding-right: 32px;
        margin-right: 24px;
        p {
          width: 35px;
          height: 45px;
          background: url('../../assets/images/Icon-suo.png') 10px center
            no-repeat;
        }
        input {
          width:100%;
          border: none;
          height: 45px;
          background: #f7f7f7;
        }
        input::-webkit-input-placeholder{
            width: 105px;
            height: 16px;
            font-size: 15px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #BBC2D8;
            line-height: 16px;
        }
      }
      .right {
        width: 200px;
        height: 45px;
        text-align: center;
        line-height: 45px;
        border-radius: 24px;
        border: 1px solid #4185f8;

        font-size: 15px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #4185F8;

      }
    }
    .err-tip {
      margin: 0;
      padding: 0;
      text-align: left;

      height: 14px;
      font-size: 13px;
      font-family: PingFangHK-Regular, PingFangHK;
      font-weight: 400;
      color: #e14c46;
      line-height: 14px;
      margin-left: 10px;
      margin-top: 5px;
    }
  }
  .vertify {
    height: 45px;
    line-height: 45px;
    text-align: center;
    background: #4185f8;
    border-radius: 24px;

    font-size: 16px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #ffffff;
    margin-top: 50px;
  }
  .read {
    margin-top: 20px;
    .am-checkbox-agree {
      margin: 0;
      padding: 0;    
      .am-checkbox-agree-label {
        margin-left: 22px;
        font-size: 12px;
        color: #6A6F7F;
        display: flex;
        align-items: center;
        .am-checkbox-inner {
          top: 2px;
          width: 15px;
          height: 15px;
          border-radius: 50% !important;
          border: 1px solid #6A6F7F !important;
    
         }
  
       }
    }
     span {
       color: #4185F7;
     }
  }
  .body{
    height: 50%;
    width:100%;
    .body-pic{
      height:300px;
      display:flex;
      justify-content: center;
      align-items: flex-end;
      div{
        height: 70%;
        width: 70%;
        background: url('../../assets/images/<EMAIL>') center no-repeat;
        background-size: 200px 200px;
      }
    }
    .body-qr{
      height:300px;
      display:flex;
      justify-content: center;
      align-items: flex-end;
      div{
        height: 70%;
        width: 70%;
        background: url('../../assets/images/wuxiao1.png') center no-repeat;
        // background-size: 200px 200px;
      }
    }
    .body-msg{
      font-size: 15px;
      font-family: PingFangSC-Regular, PingFang SC;
      color: #71788F;
      text-align: center;
    }
    
  }
}
.entry {
  .tip {
    font-size: 12px;
    color: #93d215;
    background-color: #f5f5f9;
    line-height: 30px;
    padding: 0 10px;
  }
  .item-name {
    border-left: 2px solid #93d215;
    padding-left: 10px;
  }
  .submit-btn {
    width: 100%;
    position: fixed !important;
    bottom: 0;
    left: 0;
  }

  .header {
    float: left;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 375px;
    height: 40px;
    background: #ffffff;
    font-size: 16px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #383838;
  }
  .info {
    display: flex;
    height: 63px;
    line-height: 63px;
    cursor: pointer;
    .person {
      flex: 1;
      text-align: center;
      font-size: 16px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #4185f8;
    }
    .person::after {
      content: '';
      display: block;
      width: 30px;
      position: relative;
      bottom: 15px;
      left: 50%;
      transform: translateX(-50%);
      border: 1px solid #4185f8;
      border-radius: 3px;
    }
    .position {
      flex: 1;
      text-align: center;
    }
  }
}
.text-container {
  padding: 20px;
}
