import axios from 'axios'
import { Toast } from 'antd-mobile';

var defaultHeader = {
  Accept: 'application/json',
  'Content-Type': 'application/json'
};

const instance = axios.create({
  baseURL: window.env.domain + "/api",
  timeout: 60000,
  headers: defaultHeader,
  // withCredentials: true,
});

// 请求拦截
instance.interceptors.request.use(config => {
  let token = sessionStorage.getItem("token");  
  config.headers['Authorization'] = token;
  config.headers['AgentClientDomain'] = location.hostname;
  return config;
}, err => {
  return Promise.reject(err);
});
// 响应拦截
instance.interceptors.response.use((response)=> {
  if (response.config.responseType === 'blob') {

  }else{
    if(!response.data.success){
      Toast.info(response.data.message || '接口错误');
      return Promise.reject(response.data);
    }
  }
  return response
}, (error)=>{
  if (error) {
    console.error(error);
  }
  return Promise.reject(error)
})

function request(url,params,headers={},method,blob){
  return new Promise((resolve,reject)=>{
    let data = {}
    if(method =='get') data = {"params":params}
    if(method =='post') data = {"data":params}
    instance({
      url,
      method,
      headers,
      ...data,
      ...blob
    }).then((res)=>{
      resolve(res.data);
    }).catch(err => {
			reject(err);
		})
  })
}
function get(url,params,headers){
  return request(url,params,headers,'get')
}
function post(url,params,headers){
  return request(url,params,headers,'post')
}
function down(url,params,headers={}){
  return request(url,params,headers,'post',{responseType: 'blob'})
}
export default {
  get,post,down
}

