/* eslint-disable */
const MiniCssExtractPlugin = require("mini-css-extract-plugin");
const ManifestPlugin = require("webpack-manifest-plugin");
const PostCompile = require("post-compile-webpack-plugin");
const rimraf = require("rimraf");
const WebpackInlineManifestPlugin = require("webpack-inline-manifest-plugin");
const UglifyJsPlugin = require("uglifyjs-webpack-plugin");
const CopyWebpackPlugin = require("copy-webpack-plugin");
const HtmlWebpackPlugin = require("html-webpack-plugin");
const FriendlyErrorsPlugin = require("friendly-errors-webpack-plugin");
const webpack = require("webpack");
const autoprefixer = require("autoprefixer");
const path = require("path");
const buildConfigs = require("./config/buildEnv");
const pkg = require("./package.json");
const proxyTable = require("./config/proxies");
const theme = require("./package.json").theme;

const splitChunkRules = require("./config/splitChunkRules");
const BundleAnalyzerPlugin = require("webpack-bundle-analyzer").BundleAnalyzerPlugin;

const NODE_ENV = process.env.NODE_ENV || "development";
const BUILD_DOMAIN = process.env.BUILD_DOMAIN || "localServer";
const VERSION = `v${pkg.version}`;
const IS_PROD = NODE_ENV === "production";

const SOURCE_DIR = path.resolve(__dirname, "src");
const BUILD_DIR = path.resolve(__dirname, "dist");

const apiHost = buildConfigs[BUILD_DOMAIN];
const createConfig = (settings) => {
  const IS_SERVER = settings.type === "server";
  const OUTPUT_DIR = IS_SERVER
    ? path.join(BUILD_DIR, "server")
    : // ? path.join(BUILD_DIR, VERSION, 'server')
      // : path.join(BUILD_DIR, VERSION);
      path.join(BUILD_DIR);

  // console.info('----OUTPUT_DIR----',OUTPUT_DIR)

  return {
    mode: NODE_ENV,
    target: IS_SERVER ? "node" : "web",
    context: SOURCE_DIR,
    entry: IS_SERVER
      ? {
          server: "./app/index.js",
        }
      : {
          client: ["react-hot-loader/patch", "./index.js"],
        },
    output: {
      path: OUTPUT_DIR,
      // publicPath: "/",
      publicPath: '/__PUBLIC_PATH_PLACEHOLDER__/',
      filename: "assets/[name].[hash:8].js",
      libraryTarget: IS_SERVER ? "commonjs2" : "umd",
    },
    resolve: {
      extensions: [".js", ".jsx", ".json"],
    },
    performance: {
      // false | "error" | "warning" // 不显示性能提示 | 以错误形式提示 | 以警告...
      hints: IS_PROD ? "warning" : false,
      // 开发环境设置较大防止警告
      // 入口体积限制 1000kb
      maxEntrypointSize: 1000000,
      // 最大单个资源体积，限制200kb
      maxAssetSize: 200000,
    },
    optimization: IS_SERVER
      ? {}
      : {
          splitChunks: {
            cacheGroups: splitChunkRules,
          },
          runtimeChunk: {
            name: "manifest",
          },
          // 压缩js
          minimizer: [
            new UglifyJsPlugin({
              cache: true,
              parallel: true,
              sourceMap: false,
              uglifyOptions: {
                compress: {
                  dead_code: true,
                  drop_console: true,
                },
              },
            }),
          ],
        },
    module: {
      rules: [
        {
          test: /\.(jsx|js)$/,
          exclude: /node_modules/,
          use: {
            loader: "babel-loader",
          },
        },
        IS_SERVER
          ? {
              test: /\.scss$/,
              exclude: /node_modules/,
              use: [MiniCssExtractPlugin.loader, "css-loader", "sass-loader"],
            }
          : {
              test: /\.scss$/,
              exclude: /node_modules/,
              use: IS_PROD
                ? [
                    MiniCssExtractPlugin.loader,
                    {
                      loader: "css-loader",
                      options: { minimize: true },
                    },
                    {
                      loader: "postcss-loader",
                      options: {
                        plugins: () => [autoprefixer({ browsers: "last 5 versions" })],
                        sourceMap: true,
                      },
                    },
                    "sass-loader",
                  ]
                : [
                    {
                      loader: "style-loader",
                      options: { singleton: true },
                    },
                    "css-loader",
                    {
                      loader: "postcss-loader",
                      options: {
                        plugins: () => [autoprefixer({ browsers: "last 5 versions" })],
                        sourceMap: true,
                      },
                    },
                    "sass-loader",
                  ],
            },
        {
          //todo 暂时仅配置了纯客户端开发环境的less处理 待配置生产环境
          test: /\.less$/,
          use: [
            "style-loader",
            "css-loader",
            { loader: "less-loader", options: { modifyVars: theme, javascriptEnabled: true } },
          ],
          include: /node_modules/,
        },
        {
          test: /\.css$/,
          include: /node_modules/,
          use: [MiniCssExtractPlugin.loader, "css-loader"],
        },
        {
          test: /\.(svg|woff2?|ttf|eot|jpe?g|png|gif)(\?.*)?$/i,
          use: IS_PROD
            ? {
                loader: "file-loader",
                options: {
                  name: "[name].[hash:8].[ext]",
                  outputPath: "assets/images/",
                },
              }
            : {
                loader: "url-loader",
              },
        },
      ],
    },
    plugins: [
      new webpack.DefinePlugin({
        "process.env.BUILD_CONFIG": JSON.stringify(apiHost),
        "process.env.NODE_ENV": JSON.stringify(NODE_ENV),
      }),
      new MiniCssExtractPlugin({
        filename: "assets/css/style.[hash:8].css",
        chunkFilename: "assets/css/[id].[hash:8].css",
      }),
      // Server-only plugins
    ]
      .concat(
        !IS_SERVER
          ? []
          : [
              new ManifestPlugin(),
              new PostCompile(() => {
                rimraf.sync(path.join(OUTPUT_DIR, "assets", "css"));
                rimraf.sync(path.join(OUTPUT_DIR, "assets", "images"));
              }),
              // Client-only plugins
            ]
      )
      .concat(
        IS_SERVER
          ? []
          : [
              new webpack.HotModuleReplacementPlugin(),
              new webpack.NamedModulesPlugin(),
              new webpack.NoEmitOnErrorsPlugin(),
              new FriendlyErrorsPlugin(),
              new CopyWebpackPlugin([
                NODE_ENV === "development"
                  ? {
                      from: path.resolve("config"),
                      to: path.resolve("dist/config"),
                    }
                  : "",
                {
                  from: path.resolve("static"),
                  to: path.resolve("dist/static"),
                },
                {
                  from: path.resolve("host"),
                  to: path.resolve("dist/"),
                },
              ]),
              new HtmlWebpackPlugin({
                title: "",
                filename: "./index.html",
                template: "./index.ejs",
                version:Date.now(),
              }),
              new WebpackInlineManifestPlugin({
                name: "webpackManifest",
              }),
            ]
      ),
    // .concat(IS_PROD?[new BundleAnalyzerPlugin()]:[]),
    devtool: IS_PROD ? "source-map" : "eval-source-map",
    devServer: {
      useLocalIp: true,
      port: process.env.PORT || 8083,
      host: "0.0.0.0",
      publicPath: "/",
      contentBase: false,
      historyApiFallback: true,
      proxy: proxyTable,
      quiet: true, // necessary for FriendlyErrorsPlugin
      disableHostCheck: true, // 为了手机可以访问
      inline: true, //实时刷新
      hot: true, // 使用热加载插件 HotModuleReplacementPlugin
      clientLogLevel: "none", // HRM WDS 在浏览器控制台的输出,
      overlay: {
        //当出现编译器错误或警告时，就在网页上显示一层黑色的背景层和错误信息
        errors: true,
      },
    },
  };
};

module.exports = createConfig;
