<template>
  <div class="employee-info">
    <CompList />
    <div style="height: 80px;"></div>
    <Footer 
      :fun="handleSubmit" 
      text="提交个人信息" 
    />
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import Header from '@/components/Header.vue';
import CompList from '../components/List.vue';
import Footer from '@/components/Footer.vue';

export default {
  name: 'Detail',
  components: {
    Header,
    CompList,
    Footer
  },
  computed: {
    ...mapGetters('employeeInfo', ['groupRequired'])
  },
  mounted() {
    // 组件挂载后的逻辑
  },
  methods: {
    handleSubmit() {
      const { groupRequired } = this;
      
      for (let i in groupRequired) {
        let item = groupRequired[i];
        if (!item.isRequired) {
          this.$toast(`请填写${item.name}必填项`);
          return;
        }
      }
      
      this.$toast.success('提交成功！');
    }
  }
};
</script>

<style lang="scss" scoped>
.employee-info {
  width: 100%;
  height: 100%;
}
</style>
