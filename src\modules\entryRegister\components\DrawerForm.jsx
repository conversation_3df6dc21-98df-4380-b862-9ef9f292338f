import React, { Component } from "react";
import {withRouter} from "react-router-dom";
import { List, Button, Toast, InputItem, DatePicker, Picker, ImagePicker , NavBar ,Icon} from "antd-mobile";
import "./scss/DrawerForm.scss";
import { createForm } from "rc-form";
import { connect } from "react-redux";
import action from "modules/entryRegister/redux/action";
import {validPhoneReg, valideEmail,validIdNo} from "utils/validReg";
import { checkMobileField, checkEmailField } from "utils/constData";
import Footer from "components/Footer";
import Upload from "components/UpLoad";

//新增多条--表单模板
class CompDrawerForm extends Component {
  constructor(props) {
    super(props);
    this.state = {
      list:[],//详情
      form: {},//表单数据
      checkform:{},//校验字段
      files: {},//附件信息
      groupName:'',//分组名称
      groupCode:'',//分组编码
      addMultipleYn:false,//是否支持多条
      empId:'',//企业员工ID
      id:'',//操作ID
      operation:{//操作类型
        ADD:"ADD",
        EDIT:"EDIT",
        DELETE:"DELETE"
      },//操作名
      isRequired:{},
      isChecked:{},//手机格式校验
      isCheckedEmail:{},//邮箱格式校验
      date:{},//存放日期值
    };
  }
  //初始化数据
  componentDidMount() {
    this.props.onRef(this)//向父组件抛出方法
    const { groupName,groupCode,empId,addMultipleYn} = this.props.data;
    const list = this.props.list;
    //const id = this.props.id;
    const id = 0;
    let form = {};
    let files = {};
    let date = {};
    list.map((item,index) => {
      form[item.fieldCode] = "";
      if (item.fieldType === 'OPTION') {
        this.handleOption(item)//初始化option
      }
      if(item.fieldType === 'FILE'){
        //files[item.fieldCode] = files.fieldValue||[];//初始化附件
        files[item.fieldCode] = [];//初始化附件
        form[item.fieldCode] = [];
      }
      if(item.fieldType === 'DATE'){
        //date[item.fieldCode] = (item.fieldValue||"") == "" ? "" : new Date(item.fieldValue);//初始化时间格式
        date[item.fieldCode] = "";//初始化时间格式
      }
    });
    this.setState({form,files,date,list,groupName,groupCode,empId,id,addMultipleYn});
  }

  //对list里面type是option的数据处理
  handleOption (item) {
    const { selectedInfo,countrylist,citylist } = this.props;
    //console.log(item)
    let optionArr = [],
        cols = 1;
    switch(item.fieldCode){
      case "country" : 
        countrylist.map((it,index)=>{
          optionArr.push({
            label: it,
            value: it,
          });
        })
        cols = 1;
        break;
      case "householdCountry" : 
        citylist.map((it,index)=>{
          optionArr.push({
            label: it.dictName,
            value: it.dictCode,
          });
        })
        cols = 1;
        break;
      default : 
        item.options.map((it) => {
          optionArr.push({
            label: it.optionEnumName,
            value: it.optionEnumCode,
            enableYn: it.enableYn,
          });
        });
        cols = 1;
        break;
    }
    item.optionList = optionArr;
    item.cols = cols;
    return item;
  }

  //证件号码格式校验
  handleCheckIdNo(){
    const { idNo,idType } = this.state.form;
    const resdata = validIdNo(idNo,idType);
    console.log("已校验证件号手机号")
    if(!resdata.boo) Toast.info(resdata.data);
    return resdata.boo;
  }

  //手机号格式校验
  handleCheckMobile(){
    const {form ,list ,groupCode} = this.state;
    const checkField = checkMobileField[groupCode]||[];
    let newisChecked = {},
        boo = true;
    checkField.map((item,index)=>{
      let resdata = validPhoneReg(form[item]);
      resdata.boo ? (newisChecked[item] = false) : (newisChecked[item] = true)&&(boo=false)
    })
    newisChecked = Object.assign(this.state.isChecked,newisChecked);
    this.setState({
      isChecked:newisChecked,
    });
    console.log("已校验手机格式")
    return boo
  }

  //邮箱格式校验
  handleCheckEmail(){
    const {form ,list ,groupCode} = this.state;
    const checkField = checkEmailField[groupCode]||[];
    let newisCheckedEmail = {},
    boo = true;
    checkField.map((item,index)=>{
      let resdata = valideEmail(form[item]);
      resdata.boo ? (newisCheckedEmail[item] = false) : (newisCheckedEmail[item] = true)&&(boo=false)
    })
    newisCheckedEmail = Object.assign(this.state.isCheckedEmail,newisCheckedEmail);
    this.setState({
      isCheckedEmail:newisCheckedEmail,
    });
    console.log("已校验邮箱格式")
    return boo
  }

  //失去焦点：数据校验 -证件号+手机号 -必填项
  checkIdCard(v,type){
    const { idNo, mobile, idType } = this.state.form;
    const { empId } = this.state;
    const { acCheckIdAndMobile} = this.props;
    if ((type === "idNo" || type === "mobile") && idNo !== "" && mobile !== "" && idType !== ""){
      acCheckIdAndMobile({
        compEmpId:empId,
        idCard: idNo,
        idType: idType,
        mobile: mobile,
      });
    }
    if(isrequired){
      this.setState({
        isRequired:Object.assign(this.state.isRequired,{[type]:v!=="" ? false : true})
      },()=>{
        console.log("校验必填字段："+type)
      });
    }
  }
  //双向绑定
  handleChange(val,formIndex,type,isrequired) {
    console.log(val,formIndex)
    let newForm = {},
        newDate = {}
    switch (type) {
      case "OPTION":
        newForm = Object.assign({}, this.state.form, { [formIndex]: val.join('') });
        this.setState({form:newForm}, ()=>{
          //this.handleCheck()
        })
      break;
      case "DATE" :
        newForm = Object.assign({}, this.state.form, { [formIndex]: this.handleDate(val) })
        newDate = Object.assign({}, this.state.date, { [formIndex]: val })
        this.setState({form:newForm,date:newDate}, ()=>{
          //this.handleCheck()
        })
      break;
      default :
        newForm = Object.assign({}, this.state.form, { [formIndex]: val })
        this.setState({form:newForm}, ()=>{
          //this.handleCheck()
        })
    }
    if(isrequired){
      this.setState({
        isRequired:Object.assign(this.state.isRequired,{[formIndex]:val!=="" ? false : true})
      },()=>{
        console.log("校验必填字段："+formIndex)
      });
    }
    
  }
  //时间格式转换 ->2020-1-1
  handleDate(value){
    const newDate = value.getFullYear() + '-' + (value.getMonth() + 1 +'').padStart(2,0) + '-' + (value.getDate()+'').padStart(2,0);
    return newDate
  }

  //back
  handleBack () {
    //this.props.history.push({ pathname:'/entry-register/detail'});
    window.history.back(-1)
  }
  //提交前校验必填
  handleCheck(){
    const {form ,list} = this.state;
    let newisRequired = {},
        boo = true;//true -> 校验通过；false ->校验失败
    list.map((item,index)=>{
      if(item.isRequired){
        form[item.fieldCode] == "" ?
        (newisRequired[item.fieldCode] = true)&&(boo=false) :
        (newisRequired[item.fieldCode] = false)
      }
    })
    newisRequired = Object.assign(this.state.isRequired,newisRequired);
    this.setState({
      isRequired:newisRequired,
    });
    console.log("已校验必填")
    return boo
  }
  //提交
  submit (){
    const {id,operation, groupCode,empId} = this.state;
    if(!this.handleCheck()){//提交前校验必填
      return;
    }
    if(!this.handleCheckMobile()){//提交前校验手机号格式
      return
    }
    if(!this.handleCheckEmail()){//提交前校验邮箱格式
      return
    }
    if( groupCode === "BASIC_INFO"){//基本信息校验格式
      if(!this.handleCheckIdNo()){//提交前校验身份证件信息格式
        return
      }
    }
    
    
    const { acOperateEmp ,optionSource} = this.props;
    const { form } = this.state;
    const _operation = !id ? operation["ADD"] : operation["EDIT"];
    const _id = id||0;
    let data = {
      id:_id,
      empId,
      groupCode,
      param: form,
      operation:_operation,
      optionSource//登记详情-修改操作-新增参数
    };
    
    this.props.getDrawerFormMsg('123')

    console.log(data)
    //Toast.info("添加成功");
    acOperateEmp(data).then(res => {
      if(res.success){
        Toast.info("保存成功");
        this.handleBack()
      }else{
        Toast.info("保存失败");
      }
    })
  }
  /*
  * res:上传成功附件
  * fieldCode:表单id
  */
  //图片上传返回结果
  handleuploadResult(res,fieldCode){
    const fileFormArr = this.state.form[fieldCode];
    const fileFilesArr = this.state.files[fieldCode];
    fileFormArr.push(res.data.archiveId)
    fileFilesArr.push({url:res.data.url,id:res.data.archiveId})
    
    let newFilesForm = '',
        newFiles = '';
    newFilesForm = Object.assign(this.state.form,{[fieldCode]: fileFormArr});
    newFiles = Object.assign(this.state.files,{[fieldCode]: fileFilesArr});

    this.setState({
      form:newFilesForm,
      files:newFiles
    });
  }
  /*
  * res:剩余附件
  * fieldCode:表单id
  * fileIndex:删除附件索引
  */
  //图片移除返回结果
  handleremoveResult(res,fieldCode,fileIndex){
    const fileFormArr = this.state.form[fieldCode];
    const fileFilesArr = this.state.files[fieldCode];

    const newfileFormArr = fileFormArr.filter((value,index)=>{
      //return value !== res.id
      return fileIndex !== index
    })
    const newfileFilesArr = fileFilesArr.filter((value,index)=>{
      //return value.id !== res.id
      return fileIndex !== index
    })
    let newFilesForm = '',
        newFiles = '';
    newFilesForm = Object.assign(this.state.form,{[fieldCode]: newfileFormArr});
    newFiles = Object.assign(this.state.files,{[fieldCode]: newfileFilesArr});
    this.setState({
      form:newFilesForm,
      files:newFiles
    });
  }


  renderItemByType(item) {
    const { form, files,date } = this.state;
    const minDate = new Date(1949,10,1,0,0,0);
    const maxDate = new Date();
    
    switch (item.fieldType) {
      case "TEXT":
        return (
          <InputItem
            clear //清除功能
            name={item.fieldCode}
            value={form[item.fieldCode]}
            onChange={(value)=>{this.handleChange(value,item.fieldCode,"TEXT")}}
            defaultValue={form[item.fieldCode]} //默认值
            placeholder={item.fieldRemark}
            disabled={!item.isEdit} //是否禁用
            onBlur={(v)=>{this.checkIdCard(v,item.fieldCode,item.isRequired)}}
            maxLength={item["length"]}//最大长度限制
          ></InputItem>
        );
        //}
      case "NUMBER":
        return (
          <InputItem
            clear//清除功能
            name={item.fieldCode}
            value={form[item.fieldCode]}
            onChange={(value)=>{this.handleChange(value,item.fieldCode,"TEXT")}}
            defaultValue={form[item.fieldCode]}//默认值
            placeholder={item.fieldRemark}
            disabled={!item.isEdit}//是否禁用
            //onBlur={this.checkIdCard("idNo")}
          ></InputItem>
        );
      case "DATE":
        return (
          <DatePicker
            minDate={minDate}
            maxDate={maxDate}
            mode="date"
            extra={item.fieldRemark}
            // value={form[item.fieldCode]}
            // onChange={(value)=>{this.handleChange(value,item.fieldCode,"DATE")}}

            value={date[item.fieldCode]}
            onChange={(value)=>{this.handleChange(value,item.fieldCode,"DATE",item.isRequired)}}
            placeholder={item.fieldRemark}
            disabled={!item.isEdit}
          >
            <List.Item arrow="horizontal"></List.Item>
          </DatePicker>
        );
      case "OPTION":
        return (
          <Picker
            data={item.optionList}//数据源{[value:'',label:'']}
            cols={1}
            extra="请选择"
            value={[form[item.fieldCode]]}
            //format={(labels) => { return labels.join(',');}}
            onChange={(value)=>{this.handleChange(value,item.fieldCode,"OPTION",item.isRequired)}}
            disabled={!item.isEdit}//是否不可用
          >
            <List.Item arrow="horizontal"></List.Item>
          </Picker>
        );
      case "FILE":
        return (
          <div className="file-box">
            <Upload
              files={files[item.fieldCode]}
              fieldCode={item.fieldCode}
              uploadResult={this.handleuploadResult.bind(this)}//上传结果
              removeResult={this.handleremoveResult.bind(this)}//移除结果
            >
            </Upload>
          </div>
        );
      default:
        return null;
    }
  }
  render() {
    const { list, form ,groupName,groupCode,empId,id,operation,isRequired,isChecked,isCheckedEmail} = this.state;
    const footerStyle={
      height: "80px",
      width: "100%",
      position: "fixed",
      //bottom: "0"
    };
    const submitBtn={
      margin: "0 26px",
      borderRadius: "20px",
      height: "40px",
      lineHeight: "40px",
    };
    return (
      <div className="entry-form">
        <List>
          {list.map((item, index) => (
            <List.Item key={index}>
              <div>
                {item.isRequired ? <span className="required">*</span> : null}
                <span className="label-name">{item.fieldName}</span>
              </div>
              {this.renderItemByType(item)}
              {item.isRequired && isRequired[item.fieldCode] ? <p className="requiredNode">{item.fieldName}不允许为空</p> : null}
              {
                (checkMobileField[groupCode] && checkMobileField[groupCode].includes(item["fieldCode"]))&& isChecked[item.fieldCode] ? 
                <p className="requiredNode">请检查电话格式</p> : null
              }
              {
                (checkEmailField[groupCode] && checkEmailField[groupCode].includes(item["fieldCode"]))&& isCheckedEmail[item.fieldCode] ? 
                <p className="requiredNode">请检查邮箱格式</p> : null
              }
            </List.Item>
          ))}
        </List>
        <div style={{height:'20px'}}></div>
        {/* <Footer fun={this.submit.bind(this)} text={'添加'} /> */}
        <footer style={footerStyle}>
          <Button type="primary" style={submitBtn} onClick={this.submit.bind(this)}>添加</Button>
        </footer>
      </div>
    );
  }
}

const mapStateToProps = (state) => ({
  selectedInfo: state.entryRegister.selectedInfo,
  countrylist: state.entryRegister.countrylist,
  citylist: state.entryRegister.citylist,
  optionSource: state.entryRegister.optionSource,
});

const mapDispatchToProps = {
  acRegistDetail: action.acRegistDetail,
  acOperateEmp:action.acOperateEmp,
  acCheckIdAndMobile:action.acCheckIdAndMobile,
  acUploadFile:action.acUploadFile
};

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(CompDrawerForm));
