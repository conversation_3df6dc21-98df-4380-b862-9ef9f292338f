function createAsyncAction(name, callback, meta = {}) {
  if (typeof callback !== 'function') {
    throw new Error('[createAsyncAction] callback must be a function');
  }

  return (dispatch) => {
    dispatch({
      meta,
      type: `${name}_REQUEST`,
    });

    try {
      return callback(dispatch)
        .then((resData) => {
          const action = {
            meta,
            type: `${name}_SUCCESS`,
            payload: resData,
          };

          dispatch(action);
          return action;
        })
        .catch((err) => {
          const action = {
            meta,
            type: `${name}_ERROR`,
            payload: err,
            error: true,
          };

          dispatch(action);
          return action;
        });
    } catch (err) {
      const action = {
        meta,
        type: `${name}_ERROR`,
        payload: err,
        error: true,
      };

      dispatch(action);
      return Promise.resolve(action);
    }
  };
}

export default createAsyncAction;
