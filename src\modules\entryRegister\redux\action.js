//import fetch from 'request/fetch';
import fetch from 'request/newFetch';
//import fetchH5 from 'request/fetchH5';
import fetchUpload from 'request/fetchUpload';

import * as AT from './actionTypes'

const acSetSelectInfo = (payload) => (dispatch, getState) => {dispatch({type: AT.SELECTEDINFO, payload})}
//下发短信
const acSendMsg = (param) => (dispatch, getState) => fetch.post(`/hrsaas-emp/sms/v1/sms-captcha`, param).then(res => res);
//验证短信
const acCheckMsg = (param) => (dispatch, getState) => fetch.post(`/hrsaas-emp/sms/v1/sms-auth`, param).then(res => res);
//保存token
const acSaveToken = (payload) => (dispatch, getState) => {dispatch({type: AT.LOGIN_TOKEN,payload})};
//登记详情列表
const acRegistDetail = (param) => (dispatch, getState) => fetch.get(`/hrsaas-emp/entry/v1/emp/query/registdetail?source=ENTRY_H5`, param,{Source: "H5",'Cache-Control': 'no-cache, no-store, must-revalidate'}).then(res => res);
//员工详情操作
const acOperateEmp = (param) => (dispatch, getState) => fetch.post(`/hrsaas-emp/empDetail/operateEmp`, param,{Source: "H5"}).then(res => res);
//手机号校验
const acCheckIdAndMobile = (param) => (dispatch, getState) => fetch.post(`/hrsaas-emp/empDetail/checkIdNoAndMobile`, param,{Source: "H5"}).then(res => res);
//员工详情查询
const acgetEmpDetail = (param) => (dispatch, getState) => fetch.post(`/hrsaas-emp/empDetail/getEmpDetail`, param).then(res => res);
//登记表提交
const acUpdateReg = (param) => (dispatch, getState) => fetch.post(`/hrsaas-emp/entry/v1/emp/update/registstatus`, param,{Source: "H5"}).then(res => res);
//入职详情基本信息提交
const acupdateDetailH5 = (param) => (dispatch, getState) => fetch.post(`/hrsaas-emp/entry/v1/emp/update/entryDetailH5`, param,{Source: "H5"}).then(res => res);
//二维码校验
const acQrCodeRuleCheck = (param) => (dispatch, getState) => fetch.get(`/hrsaas-emp/entry/v1/emp/query/qrCodeRuleCheck/`+param.uid,{},{Source: "H5"}).then(res => res);
//OCR识别
const acOcrCheck = (param) => (dispatch, getState) => fetch.post(`/hrsaas-emp/empDetail/ocrCheck`, param,{Source:"H5"}).then(res => res);

//获取OCR识别结果 

const acOcrResult = (param) => (dispatch, getState) => fetch.post(`/hrsaas-emp/empDetail/ocrResult`, param,{Source: "H5"}).then(res => res);



//上传文件
const acUploadFile = (file,name) => (dispatch, getState) => {
  let formData = new FormData();
  formData.append("file", file, name)
  return fetchUpload({
    url: `/hrsaas-emp/archive/upload`,
    method: 'post',
    data: formData
  }).then(res => {
    return res
  });
}
//下载文件
const acDownload = (param) => (dispatch, getState) => fetch.down(`/hrsaas-emp/archive/download?archiveId=`+param.archiveId)

//国际列表
const acGetCountries = (params) => (dispatch, getState) => {
  return fetch.get('/hrsaas-emp/custom/v1/query/country', params,{Source: "H5"}).then((res) => {
    dispatch({
      type: AT.SET_COUNTRYLIST,
      payload: res.data
    })
    return res;
  })
}
//城市列表
const acGetCity = (params) => (dispatch, getState) => {
  return fetch.get('/hrsaas-emp/custom/v1/query/city', params,{Source: "H5"}).then((res) => {
    dispatch({
      type: AT.SET_CITYLIST,
      payload: res.data
    })
    return res;
  })
}
//联动城市列表
const acGetProvince = (params) => (dispatch, getState) => {
  return fetch.get('/hrsaas-emp/custom/v1/query/province', params,{Source: "H5"}).then((res) => {
    dispatch({
      type: AT.SET_PROVINCELIST,
      payload: res.data
    })
    return res;
  })
}
//获取url参数
const getUrlParams = (name, str) => (dispatch, getState)=>{
  const reg = new RegExp(`(^|&)${ name}=([^&]*)(&|$)`);
  const r = str.substr(1).match(reg);
  if (r != null) return  decodeURIComponent(r[2]); return null;
}

//来源
const acSetSource = (params) => (dispatch, getState) => {
    dispatch({
      type: AT.SET_OPTIONSOURCE,
      payload: params
    })
}

//各个分组是否编辑保存必填字段
const acSetGroupRequired = (params) => (dispatch, getState) => {
  dispatch({
    type: AT.SET_GROUPREQUIRED,
    payload: params
  })
}

//公司id
const acSetTaxsubId = (params) => (dispatch, getState) => {
  dispatch({
    type: AT.SET_TAXSUBID,
    payload: params
  })
}

// 身份证信息
const acSetCardInfo = (params) => (dispatch, getState) => {
  dispatch({
    type: AT.SET_CARDINFO,
    payload: params
  })
}
// 身份证信息
const acSetBaseInfo = (params) => (dispatch, getState) => {
  dispatch({
    type: AT.SET_BASEINFO,
    payload: params
  })
}


export default {
  acSetSelectInfo,
  acSendMsg,
  acCheckMsg,
  acRegistDetail,
  acSaveToken,
  acOperateEmp,
  acGetCountries,
  acGetCity,
  acCheckIdAndMobile,
  acgetEmpDetail,
  acUploadFile,
  acDownload,
  getUrlParams,
  acUpdateReg,
  acSetSource,
  acSetGroupRequired,
  acGetProvince,
  acupdateDetailH5,
  acSetTaxsubId,
  acQrCodeRuleCheck,
  acOcrCheck,
  acOcrResult,
  acSetCardInfo,
  acSetBaseInfo
};
