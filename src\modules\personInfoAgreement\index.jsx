import React, { Component } from 'react';
import { withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import "./index.scss";
import action from "modules/personInfoAgreement/redux/action"


class Agreement extends Component {
  constructor(props) {
    super(props);
    this.state = {
      count: 5,
      active: false,
      classStr: 'btn-ok',
      protocolId: 'special',
      agreementIndex: 0
    };
    this.cancel = this.cancel.bind(this);
    this.agree = this.agree.bind(this);
  }

  // 不同意逻辑处理
  cancel() {
    if (this.cancelEvent) {
      clearTimeout(this.cancelEvent);
      this.cancelEvent = null;
    }
    this.cancelEvent = setTimeout(() => {
      wx.miniProgram.navigateTo({url: '/pages/login/main?mark=no'})
    }, 500)
  }

  // 同意并下一步逻辑处理
  agree() {
    if (this.state.count !== 0) return;
    if (this.throttle) {
      clearTimeout(this.throttle);
      this.throttle = null;
    }
    this.throttle = setTimeout(() => {
      this.resolveLogic();
    }, 500)
  }

  //获取小程序传参
  handleGetUrlMsg() {
    const token = this.props.getUrlParams ('token', this.props.history.location.search)
    const protocolId = this.props.getUrlParams ('protocolId', this.props.history.location.search)
    sessionStorage.setItem("token",token);
    this.handleGetConfigInfo(protocolId);
    this.setState(() => {
      return {
        protocolId
      }
    })
  }

  resolveLogic() {
    this.props.acConfirmRead({
      confirm: true,
      protocolId: this.props.configInfo[this.state.agreementIndex].protocolId
    }).then(res => {
      if (res.success) {
        if (this.state.agreementIndex < this.props.configInfo.length - 1) {
          this.setState({
            agreementIndex: this.state.agreementIndex + 1,
            active: false,
            classStr: 'btn-ok',
            count: 5
          })
          let editor = document.querySelector('.ql-editor');
          editor.scrollTo(0, 0);
          this.timerFn();
        } else {
          wx.miniProgram.switchTab({url: '/pages/switchWork/main'})
          //wx.miniProgram.navigateTo({url: '/pages/login/main?mark=yes'})
        }
      }
    })
  }

  componentDidMount() {
    this.handleGetUrlMsg();
    this.timerFn();
    this.throttle = null;
  }

  componentWillUnmount() {
    clearTimeout(this.throttle)
    clearTimeout(this.cancelEvent)
  }

  timerFn() {
    this.timer = setInterval(() => {
      this.setState({
        count: this.state.count - 1
      })
      if (this.state.count === 0) {
        this.setState({
          active: true,
          classStr: 'btn-ok active'
        })
        clearInterval(this.timer);
      }
    }, 1000)
  }

  handleGetConfigInfo(protocolId) {
    let params = {
      protocolType: 'SECRET',
      showType: 'Force'
    }
    if (protocolId !== 'special') {
      params.showType = 'Protocol'
    }
    this.props.acGetConfigInfo(params).then(res => {
      if (res.success) {
        if (protocolId !== 'special') {
          const index = res.data.findIndex(v => v.protocolId == protocolId);
          this.setState({
            agreementIndex: index
          })
        }
      }
    });
  }

  render() {
    const richText = this.props.configInfo[this.state.agreementIndex] &&
                     this.props.configInfo[this.state.agreementIndex].protocolContent ?
                     this.props.configInfo[this.state.agreementIndex].protocolContent : '';
    const active = this.state.active;
    const show = this.state.protocolId;
    document.title = '用户协议中心';
    return (
      <div className="ql-container">
        <div className="ql-editor" dangerouslySetInnerHTML={{ __html: richText }}></div>
        {show === 'special' && <div className="popup-btn">
          <button className="btn-no" onClick={this.cancel}>不同意</button>
          <button className={this.state.classStr} onClick={this.agree}>同意并下一步
            {!active && <span>（倒计时{this.state.count}秒）</span>}
          </button>
        </div>}
      </div>
    );
  }
}

const mapStateToProps = (state) => ({
  configInfo: state.personInfoAgreement.configInfo
});

const mapDispatchToProps = {
  acGetConfigInfo: action.acGetConfigInfo,
  acConfirmRead: action.acConfirmRead,
  getUrlParams: action.getUrlParams
};

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(Agreement));
