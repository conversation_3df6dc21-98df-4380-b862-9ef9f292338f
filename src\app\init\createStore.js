import {
  createStore,
  applyMiddleware,
  compose,
} from 'redux';
import { connectRouter, routerMiddleware } from 'connected-react-router';
import reduxThunk from 'redux-thunk';
import reduxLogger from 'redux-logger'
import {makeRootReducer} from './reducers';
import routes from '../routes/routerList';
import getSessionState from 'utils/getSessionState'

function createAppStore(history, preloadedState = getSessionState()) {
  // enhancers
  let composeEnhancers = compose;

  if (typeof window !== 'undefined') {
    // eslint-disable-next-line no-underscore-dangle
    composeEnhancers = window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ || compose;
  }

  // middlewares
  const middlewares = [
    routerMiddleware(history),
    reduxThunk,
    reduxLogger
  ];

  const store = createStore(
    connectRouter(history)(makeRootReducer()),
    preloadedState,
    composeEnhancers(applyMiddleware(...middlewares)),
  );

  store.asyncReducers = {}
  if (module.hot) {
    // Enable Webpack hot module replacement for reducers
    module.hot.accept("./reducers", () => {
      const nextRootReducer = require("./reducers").default;
      store.replaceReducer(nextRootReducer(store.asyncReducers));
    });
  }
  return {
    store,
    history,
    routes,
  };
}

export default createAppStore;
